package worker

import (
	"context"
	"crypto/tls"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"finbox/go-api/models/asynctasks"
	"finbox/go-api/thirdparty/upiautopay/digioupi"
	"fmt"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/hibiken/asynq"
)

type Consumer struct {
	Server *asynq.Server
}

var log = logger.Log

func NewServer() *Consumer {
	reportError := func(ctx context.Context, task *asynq.Task, err error) {
		taskID, _ := asynq.GetTaskID(ctx)
		retried, _ := asynq.GetRetryCount(ctx)
		maxRetry, _ := asynq.GetMaxRetry(ctx)
		if retried >= maxRetry {
			uErr := asynctasks.Update(taskID, asynctasks.TaskStatusFailed, 0, err.Error())
			if uErr != nil {
				log.WithField("taskID", taskID).Error(uErr)
				sentry.CaptureException(uErr)
			}
			err = fmt.Errorf("retry exhausted for task %s, error: %w", task.Type(), err)
			log.WithField("taskID", taskID).Error(err)
			if task.Type() == constants.TaskDigioUPIAutoPayStatusPolling {
				err = digioupi.HandleUPIPollingFailure(task.Payload())
				if err != nil {
					log.WithField("taskID", taskID).Errorf("failed to handle UPI polling failure: %v", err)
					sentry.CaptureException(err)
				}
			}
		} else {
			err = fmt.Errorf("error from task %s, error: %w", task.Type(), err)
			log.WithField("taskID", taskID).Error(err)
		}
	}

	redisClientOpt := asynq.RedisClientOpt{
		Addr: conf.RedisConf["Addr"].(string),
	}

	// tls only in prod.
	if conf.ENV == conf.ENV_PROD {
		redisClientOpt.TLSConfig = &tls.Config{MinVersion: tls.VersionTLS12}
	}

	srv := asynq.NewServer(
		redisClientOpt,
		asynq.Config{
			// concurrent workers to use
			//Assign more workers (higher weights) to I/O-bound queues and fewer workers (lower weights) to CPU-bound queues.
			//hence, high priority to I/O based task and low priority to CPU heavy task.
			// Set concurrency based on the number of CPU cores and task characteristics
			//numCPU := runtime.NumCPU()
			// concurrency := numCPU * 2 // Example: 2x the number of CPU cores for I/O-bound tasks
			Concurrency:    4,
			ErrorHandler:   asynq.ErrorHandlerFunc(reportError),
			RetryDelayFunc: customisedRetryDelayFunc,
			Queues: map[string]int{
				constants.QueueNameReports:              1,
				constants.QueueNameInternalTesting:      1,
				constants.QueueNameArthan:               1,
				constants.QueueNameMuthootCL:            1,
				constants.QueueNameIIFL:                 1,
				constants.QueueNameDigioEnach:           2,
				constants.QueueNameDigioUPIAutopay:      2,
				constants.QueueNameLandT:                1,
				constants.QueueNameDigioPresentation:    1,
				constants.QueueNameCashfreePresentation: 1,
				constants.QueueNameTDL:                  1,
				constants.QueueNameWebhookEvents:        8,
				constants.QueueNameS3SyncBuckets:        1,
				constants.QueueNameABFLPL:               1,
				constants.QueueNameCasheMC:              1,
				constants.QueueTemporalScheduleCreation: 1,
				constants.QueueNameUserExpiry:           1,
				constants.QueueNameIndifi:               1,
				constants.QueueNameHDFCTDL:              1,
				constants.QueueNameInsertModuleMapping:  1,
			},
		},
	)
	return &Consumer{
		Server: srv,
	}
}

func customisedRetryDelayFunc(n int, e error, t *asynq.Task) time.Duration {
	if t.Type() == constants.TaskMuthootCLRepayment {
		return time.Minute * 60 * 24
	}
	if t.Type() == constants.TaskDigioUPIAutoPayStatusPolling {
		return time.Second * 60
	}

	return asynq.DefaultRetryDelayFunc(n, e, t)
}

func (c *Consumer) StartWorkers() error {
	mux := asynq.NewServeMux()

	//add the tasks here
	mux.HandleFunc(constants.TaskABFLCustomReport, c.AbflCustomReportTask)
	mux.HandleFunc(constants.TaskLandTStatusUpdate, c.LandTStatusUpdateTask)
	mux.HandleFunc(constants.TaskIIFLSendToLOS, c.IIFLSendLoanDetailsTask)
	mux.HandleFunc(constants.TaskArthanSendToLOS, c.ArthanSendLoanDetailsTask)
	mux.HandleFunc(constants.TaskDigioEnachPollingStatus, c.DigioEmandateStatusPollingTask)
	mux.HandleFunc(constants.TaskTDLBringBackUsers, c.TDLBringBackInActiveUsersTask)
	mux.HandleFunc(constants.TaskTDLBringBackInactiveUsers, c.TDLBringBackOfferPageInActiveUsersTask)
	mux.HandleFunc(constants.TaskDigioBulkPresentation, c.DigioBulkPresentationTask)
	mux.HandleFunc(constants.TaskTDLArchiveUsers, c.TDLArchiveUserTask)
	mux.HandleFunc(constants.TaskS3Sync, c.SyncClientS3)
	mux.HandleFunc(constants.TaskWebhookEvents, c.WebhookEventConsumerTask)
	mux.HandleFunc(constants.TaskExitSurvey, c.ExitSurvey)
	mux.HandleFunc(constants.TaskABFLPLEsign, c.ABFLPLEsignStatusTask)
	mux.HandleFunc(constants.TaskMuthootCLRepayment, c.MuthootCLRepaymentTask)
	mux.HandleFunc(constants.TaskCasheStatusUpdate, c.CasheStatusUpdateTask)
	mux.HandleFunc(constants.TaskIndifiStatusUpdate, c.IndifiStatusUpdateTask)
	mux.HandleFunc(constants.TaskCreateTemporalSchedule, c.CreateTemporalSchedule)
	mux.HandleFunc(constants.TaskExpireUsers, c.ExpireUsers)
	mux.HandleFunc(constants.TaskTdlHDFCStatusUpdate, c.TdlHDFCUpdateTask)
	mux.HandleFunc(constants.TaskDigioUPIAutoPayStatusPolling, c.DigioUPIAutopayStatusPollingTask)
	mux.HandleFunc(constants.TaskInsertInModuleMapping, c.InsertInModuleMapping)
	mux.HandleFunc(constants.TaskPFLUtmReport, c.PflUtmReportTask)
	mux.Use(loggingMiddleware)
	log.Info("starting worker server")

	// Run worker server.
	if err := c.Server.Run(mux); err != nil {
		log.Error(err)
		sentry.CaptureException(err)
		return err
	}
	return nil
}

func loggingMiddleware(h asynq.Handler) asynq.Handler {
	return asynq.HandlerFunc(func(ctx context.Context, t *asynq.Task) error {
		start := time.Now()
		taskID, _ := asynq.GetTaskID(ctx)
		err := asynctasks.UpdateStatus(taskID, asynctasks.TaskStatusInProcess)
		if err != nil {
			log.WithField("taskID", taskID).Error(err)
			sentry.CaptureException(err)
		}
		err = h.ProcessTask(ctx, t)
		if err != nil {
			log.WithField("taskID", taskID).Error(err)
			return err
		}
		err = asynctasks.Update(taskID, asynctasks.TaskStatusSuccess, time.Since(start).Seconds(), "")
		if err != nil {
			log.WithField("taskID", taskID).Error(err)
			sentry.CaptureException(err)
		}
		return nil
	})
}
