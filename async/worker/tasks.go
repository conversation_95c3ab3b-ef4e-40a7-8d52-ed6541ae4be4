package worker

import (
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/async"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/constants"
	"finbox/go-api/core/services/schedulerworkflowconfigservice"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/lenders/hdfctdl"
	"finbox/go-api/functions/lenders/indifi"
	onemuthootcl "finbox/go-api/functions/lenders/onemuthootCL"
	"finbox/go-api/functions/loanutils"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/reports/abfl"
	"finbox/go-api/functions/reports/pfl"
	"finbox/go-api/functions/services/leegality"
	"finbox/go-api/functions/services/tdl"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/usersurveyresponse"
	"finbox/go-api/temporal/workflows"
	"finbox/go-api/utils/arthantasks"
	"finbox/go-api/utils/digioenachtasks"
	"finbox/go-api/utils/expiryutils"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/iifltasks"
	"finbox/go-api/utils/landttasks"
	"finbox/go-api/utils/s3utils"
	"finbox/go-api/utils/tdlutils"
	"fmt"
	"strconv"
	"strings"

	"github.com/hibiken/asynq"
	"github.com/sirupsen/logrus"
)

// AbflCustomReportTask fetch payload and call the handler.
func (c *Consumer) AbflCustomReportTask(ctx context.Context, t *asynq.Task) error {

	defer errorHandler.RecoveryNoResponse()
	var payload abfl.AbflCustomReport
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return abfl.GenerateABFLCustomReport(ctx, payload)
}

// LandTStatusUpdateTask calls l and t status update api.
func (c *Consumer) LandTStatusUpdateTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: LandTStatusUpdateTask")

	defer errorHandler.RecoveryNoResponse()
	var payload map[string]string
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return landttasks.StatusUpdateTask(ctx, payload)
}

// IndifiStatusUpdateTask calls indifi status update api.
func (c *Consumer) IndifiStatusUpdateTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: IndifiStatusUpdateTask")
	defer errorHandler.RecoveryNoResponse()
	var payload async.UserLoanDetails
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return indifi.GetStatusFromLenderAndUpdate(ctx, payload)
}

// CasheStatusUpdateTask calls cashe status update api.
func (c *Consumer) CasheStatusUpdateTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: CasheStatusUpdateTask")
	defer errorHandler.RecoveryNoResponse()
	var payload async.UserLoanDetails
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return landttasks.CasheStatusUpdateTask(ctx, payload)
}

// IIFLSendLoanDetailsTask send payload and call the handler.
func (c *Consumer) IIFLSendLoanDetailsTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: IIFLSendLoanDetailsTask")

	defer errorHandler.RecoveryNoResponse()
	var payload map[string]string
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return iifltasks.SendLoanDetailsTask(ctx, payload)
}

// ArthanSendLoanDetailsTask send payload and call the handler.
func (c *Consumer) ArthanSendLoanDetailsTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: ArthanSendLoanDetailsTask")

	defer errorHandler.RecoveryNoResponse()
	var payload map[string]string
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return arthantasks.SendLoanDetailsTask(ctx, payload)
}

// DigioEmandateStatusPollingTask send payload and call the handler.
func (c *Consumer) DigioEmandateStatusPollingTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: DigioEmandateStatusPollingTask")
	defer errorHandler.RecoveryNoResponse()
	var payload map[string]string
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return digioenachtasks.StatusPollingTask(ctx, payload)
}

// DigioUPIAutopayStatusPollingTask
func (c *Consumer) DigioUPIAutopayStatusPollingTask(ctx context.Context, t *asynq.Task) error {
	log.Debug("starting polling task: DigioUPIAutopayStatusPollingTask")
	defer errorHandler.RecoveryNoResponse()
	var payload map[string]string
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return digioenachtasks.UPIStatusPollingTask(ctx, payload)
}

// TDLBringBackInActiveUsersTask handler for tdl bring back inactive users
func (c *Consumer) TDLBringBackInActiveUsersTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: tdl bring back users")

	defer errorHandler.RecoveryNoResponse()
	var payload map[string]string
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return loanutils.BringBackInActiveLoansToMLO(payload["source_entity_id"])
}

// TDLBringBackOfferPageInActiveUsersTask handler for tdl bring back inactive users
func (c *Consumer) TDLBringBackOfferPageInActiveUsersTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: tdl bring back users")

	defer errorHandler.RecoveryNoResponse()
	var payload map[string]string
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return loanutils.BringBackOfferInActiveLoansToMLO()
}

// TDLArchiveUserTask calls tdl archive user handler.
func (c *Consumer) TDLArchiveUserTask(ctx context.Context, t *asynq.Task) error {
	defer errorHandler.RecoveryNoResponse()
	var payload map[string]string
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}

	return tdlutils.ArchiveUserTask(ctx, payload)
}

func (c *Consumer) WebhookEventConsumerTask(ctx context.Context, t *asynq.Task) (err error) {
	defer errorHandler.RecoveryNoResponse()
	var payload activity.WebhookEventData
	if err = json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	log.Infof("webhhok event received, userID: %s, sourceEntityID: %s", payload.UserID, payload.SourceEntityID)
	return activity.ConsumeAndProcessWebhook(&payload)
}

// SyncClientS3 pushes message to client specific bucket
func (c *Consumer) SyncClientS3(ctx context.Context, t *asynq.Task) error {
	defer errorHandler.RecoveryNoResponse()
	var (
		payload map[string]string
		err     error
	)
	if err = json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Errorln(err)
		return err
	}

	objectKey, objectKeyFound := payload["objectKey"]
	if !objectKeyFound {
		log.Errorln("objectKey not found in payload - ", payload)
		return nil
	}

	if err = s3utils.CopyObjectToClientS3(objectKey); err != nil {
		log.Errorln("error copying objectKey ", objectKey, " ", err)
	}

	return err
}

func (c *Consumer) DigioBulkPresentationTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: DigioBulkPresentationTask")
	defer errorHandler.RecoveryNoResponse()
	var data map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &data); err != nil {
		log.Error(err)
		return err
	}
	return digioenachtasks.BulkPresentationTask(ctx, data)
}

func (c *Consumer) ExitSurvey(ctx context.Context, t *asynq.Task) error {
	defer errorHandler.RecoveryNoResponse()

	var data struct {
		SourceEntityID string                           `json:"sourceEntityID"`
		Form           []usersurveyresponse.ResponseRow `json:"form"`
	}
	if err := json.Unmarshal(t.Payload(), &data); err != nil {
		log.Error(err)
		return err
	}

	if journey.SendExitSurveyToClient(data.SourceEntityID) {
		return tdl.ExitSurvey(data.Form)
	}

	return errors.New("not applicable")
}

func (c *Consumer) MuthootCLRepaymentTask(ctx context.Context, t *asynq.Task) error {
	log.Info("starting polling task: MuthootCLRepaymentTask")
	defer errorHandler.RecoveryNoResponse()

	var data map[string]any
	if err := json.Unmarshal(t.Payload(), &data); err != nil {
		log.Error(err)
		return err
	}

	txnAmountStr := data["txnAmount"].(string)
	txnAmount, err := strconv.ParseFloat(txnAmountStr, 64)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{"userID": data["userID"].(string), "errCustom": "unable to parse transaction amount to float"}).Error(err)
		return err
	}

	err = onemuthootcl.SendPaymentConfirmation(context.Background(), data["userID"].(string), data["sourceEntityID"].(string), constants.MuthootCLID, data["txnID"].(string), txnAmount, data["txnCreatedAt"].(string), data["loanNo"].(string), data["orderID"].(string))
	if err != nil {
		logger.Log.WithFields(logrus.Fields{"userID": data["userID"].(string), "errCustom": "failure in SendPaymentConfirmation"}).Error(err)
		return err
	}

	return nil
}

func (c *Consumer) ABFLPLEsignStatusTask(ctx context.Context, t *asynq.Task) error {
	log.Info("starting polling task: ABFLPLEsignStatusTask")
	defer errorHandler.RecoveryNoResponse()

	var data map[string]any
	if err := json.Unmarshal(t.Payload(), &data); err != nil {
		log.Error(err)
		return err
	}

	status, err, _ := leegality.GetESignStatus(data["userID"].(string), data["documentID"].(string), data["lenderID"].(string))
	if err != nil {
		logger.Log.WithFields(logrus.Fields{"userID": data["userID"].(string), "loanApplicationID": data["documentID"].(string), "status": status, "errCustom": "failure in getting esign status"}).Error(err)
		return err
	}

	err = userjourney.SetWaitStatus(nil, data["userID"].(string), userjourney.WaitStatusFalse, "esign completed")
	if err != nil {
		logger.Log.WithFields(logrus.Fields{"userID": data["userID"].(string), "errCustom": "failure in removing user wait state"}).Error(err)
		return err
	}

	err = usersutil.SetUserWaitState(data["userID"].(string), false)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{"userID": data["userID"].(string), "errCustom": "failure in removing user wait state"}).Error(err)
		return err
	}

	return nil
}

// CreateTemporalSchedule creates a schedule in temporal
func (c *Consumer) CreateTemporalSchedule(ctx context.Context, t *asynq.Task) error {
	log.Infoln("starting task: CreateTemporalSchedule")
	defer errorHandler.RecoveryNoResponse()

	var workflowConfig schedulerworkflowconfigservice.Config

	if err := json.Unmarshal(t.Payload(), &workflowConfig); err != nil {
		log.Errorf("error unmarshalling payload: %v", err)
		return err
	}

	workflowDefnBytes, err := json.Marshal(workflowConfig.WorkflowDefinitionMap)
	if err != nil {
		log.Errorf("error marshalling workflow definition: %v", err)
		return err
	}

	if err := temporalclient.WorkerClient.CreateSchedule(ctx, temporalclient.SchedulerOptions{
		ScheduleID:           workflowConfig.ScheduleID,
		CronExpression:       workflowConfig.CronExpression,
		PauseOnFailure:       false,
		MaximumRetryAttempts: workflowConfig.RetryCount,
		TaskQueue:            temporalclient.WorkerClient.Queue,
		Workflow: temporalclient.Workflow{
			ID:               workflowConfig.WorkflowID,
			WorkflowFunction: workflows.SchedulerWorkflow,
			Arguments:        []interface{}{string(workflowDefnBytes)},
		},
	}); err != nil {
		log.Errorln(err)
		if !strings.Contains(err.Error(), "scheduleID is already registered") {
			return err
		}
	}

	return nil
}

// ExpireUsers expires users in bulk
func (c *Consumer) ExpireUsers(ctx context.Context, t *asynq.Task) error {
	log.Infoln("starting task: ExpireUsers")
	defer errorHandler.RecoveryNoResponse()

	var expiryData []expiryutils.ExpiryData
	err := json.Unmarshal(t.Payload(), &expiryData)
	if err != nil {
		log.Errorln(err)
		return err
	}

	return expiryutils.BulkExpire(expiryData)
}

// TDL HDFC StatusUpdateTask calls hdfc status update api.
func (c *Consumer) TdlHDFCUpdateTask(ctx context.Context, t *asynq.Task) error {
	log.Errorln("starting polling task: TDLHDFCStatusUpdateTask")
	defer errorHandler.RecoveryNoResponse()
	var payload hdfctdl.UpdateData
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error(err)
		return err
	}
	return tdlutils.TdlUpdateStatus(ctx, payload)
}

func (c *Consumer) InsertInModuleMapping(ctx context.Context, t *asynq.Task) error {
	log.Infoln("starting polling task: InsertInModuleMapping")
	defer errorHandler.RecoveryNoResponse()
	var payload async.InsetModuleMapping
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("failed to unmarshal into required payload: ", err)
		return err
	}

	if !general.InArr(payload.Status, constants.UserModuleStatusList) {
		errMsg := fmt.Sprintf("Status is not valid %v", payload.Status)
		log.Error(errMsg)
		return errors.New(errMsg)
	}

	err := usermodulemapping.Create(nil, payload.UserID, payload.UserID, payload.ModuleName, payload.Status, payload.LoanApplicationID)
	if err != nil {
		errMsg := fmt.Sprintf("error marking disbursal in module mapping %v", err)
		log.Error(errMsg)
		return errors.New(errMsg)
	}
	log.Infoln("InsertInModuleMapping: Task completed - userID : ", payload.UserID)
	return nil
}

func (c *Consumer) PflUtmReportTask(ctx context.Context, t *asynq.Task) error {

	defer errorHandler.RecoveryNoResponse()
	var payload pfl.PflUtmReport
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.WithContext(ctx).Errorf("failed to unmarshal payload. Error: %v,  Payload: %v", err, payload)
		return err
	}
	return pfl.GeneratePFLUtmReport(ctx, payload)
}
