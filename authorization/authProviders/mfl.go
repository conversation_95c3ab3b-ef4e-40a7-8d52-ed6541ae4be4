package authProviders

import (
	"context"
	"database/sql"
	"errors"
	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/repository/psql"
	"finbox/go-api/internal/repository/psql/querybuilder"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	workflowinstancesql "finbox/go-api/internal/repository/psql/workflowinstance"
	"finbox/go-api/thirdparty/redgate"
	"fmt"
)

type MFLAuthorizationInfo struct {
	SourceType       string
	BranchCode       string
	ApplicationState string
}

var workflowInstanceRepo = workflowinstancesql.NewWorkflowInstancesRepository(psql.Database)

func (a *MflAuthorizer) AuthorizeUser(ctx context.Context, authRequest redgate.AuthorizeUserRequest) (*redgate.AuthorizationResponse, error) {
	userInfo, err := GetUserInfo(ctx, map[string]interface{}{"userID": authRequest.UserID})
	if err != nil {
		return nil, err
	}
	if authRequest.Data == nil {
		authRequest.Data = map[string]interface{}{}
	}
	authRequest.Data["branchCode"] = userInfo.BranchCode
	authRequest.Data["state"] = authRequest.LoanApplicationState

	authRequest.Hierarchy = redgate.HierarchyQuery{
		Metadata: map[string]string{
			"sourceType": userInfo.SourceType,
		},
	}
	authResp, err := redgate.CheckUserAuthorization(ctx, &authRequest)
	if err != nil {
		err := fmt.Errorf("[AuthorizeUser] Failed to authorize user %s for resource %s: %v", authRequest.UserID, authRequest.ResourceName, err)
		logger.WithContext(ctx).Errorf(err.Error())
		return nil, err
	}

	return authResp, nil
}

func (a *MflAuthorizer) ExecutePolicy(ctx context.Context, policyRequest redgate.ExecutePolicyRequest) (*redgate.ExecutePolicyResponse, error) {
	userInfo, err := GetUserInfo(ctx, map[string]interface{}{"userID": policyRequest.UserID})
	if err != nil {
		return nil, err
	}
	manualCreditReviewStateObj, err := workflowInstanceRepo.DBGetWorkflowStateByTaskType(ctx, &workflowinstancesql.GetWorkflowStateByTaskTypeParam{
		LoanApplicationID: policyRequest.LoanApplicationID,
		TaskType:          "mfl_manual_credit_review",
	})
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, fmt.Errorf("[ExecutePolicy] Fetch workflow state by task type failed: %w", err)
	}
	var manualCreditReviewState string
	if manualCreditReviewStateObj != nil {
		manualCreditReviewState = manualCreditReviewStateObj.CurrentState
	}
	policyRequest.Data = map[string]interface{}{
		"branchCode": userInfo.BranchCode,
		"state":      policyRequest.LoanApplicationState,
		"workflows": map[string]interface{}{
			"manual_credit_review": map[string]interface{}{
				"currentState": manualCreditReviewState,
			},
		},
	}
	policyRequest.Hierarchy = redgate.HierarchyQuery{
		Metadata: map[string]string{
			"sourceType": userInfo.SourceType,
		},
	}
	response, err := redgate.ExecutePolicy(ctx, policyRequest)
	if err != nil {
		err = fmt.Errorf("[ExecutePolicy] Failed to Execute RedGate policy err: %v, policy: %v", err, policyRequest.PolicyName)
		logger.WithContext(ctx).Errorf(err.Error())
		return nil, err
	}
	return response, nil
}

func (a *MflAuthorizer) ExecutePolicyWithoutUserInfo(ctx context.Context, policyRequest redgate.ExecutePolicyRequest) (*redgate.ExecutePolicyResponse, error) {

	response, err := redgate.ExecutePolicy(ctx, policyRequest)
	if err != nil {
		err = fmt.Errorf("[ExecutePolicyWithoutUserInfo] Failed to Execute RedGate policy err: %v, policyRequest: %+v", err, policyRequest)
		logger.WithContext(ctx).Errorf(err.Error())
		return nil, err
	}

	return response, nil
}

func GetUserInfo(ctx context.Context, valueMap map[string]interface{}) (*MFLAuthorizationInfo, error) {
	userInfoReq := querybuilder.DBJSONLookupParam{
		TableName:    "users",
		LookupColumn: "user_id",
		LookupValue:  valueMap["userID"].(string),
		JSONColumn:   "dynamic_user_info",
		JsonFields:   map[string]interface{}{"source_type": "", "loanBranchDetails.branchCode": ""},
	}

	userInfo, err := querybuilder.DBExtractJSONFieldsByKey(ctx, userInfoReq)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetUserInfoAndState] failed to extract user info: %v", err)
		return nil, fmt.Errorf("user not found")
	}

	result := &MFLAuthorizationInfo{
		SourceType: userInfo.FieldsValueMap["source_type"].(string),
		BranchCode: userInfo.FieldsValueMap["loanBranchDetails.branchCode"].(string),
	}

	if result.SourceType == "" || result.BranchCode == "" {
		logger.WithContext(ctx).Errorf("[GetUserInfoAndState] missing sourceType or branchCode for user %s", userInfoReq.LookupValue)
		return nil, fmt.Errorf("unknown branch code")
	}

	return result, nil
}
func GetLoanState(ctx context.Context, loanApplicationID string) (string, error) {
	masterTaskID := "0196436e-720d-7a01-9f73-6b36c634107f"
	task, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{
		MasterTaskID:   &masterTaskID,
		IdentifierID:   &loanApplicationID,
		IdentifierType: nil,
	}, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[constants.GetLoanStatusText()] error getting tasks for loanApplicationID:%s err:%v", loanApplicationID, err)
	}

	if task == nil {
		return "initial", nil
	}

	workflowInstance, err := workflowInstanceRepo.DBGetWorkflowInstancesByParams(ctx, &workflowinstancesql.DBGetWorkflowInstancesParam{
		IdentifierID: task.Id,
	}, nil)
	if err != nil {
		return "", fmt.Errorf("[GetLoanDetailsConst] error getting workflow instance for task:%s err:%v", task.Id, err)
	}
	if workflowInstance.CurrentState != "" {
		return workflowInstance.CurrentState, nil
	}
	return "initial", nil
}
