package constants

const (
	MasterUserTypeDefault     = "default"
	MasterUserTypeDSA         = "dsa"
	MasterUserTypeSubDSA      = "sub-dsa"
	MasterUserTypePlatformDSA = "platform-dsa"
	MasterUserTypeSuperDSA    = "super-dsa"
)

const (
	LoginStatusDisabled = 0
	LoginStatusEnabled  = 1
)

const (
	BulkAddSDSAMaxLimit = 1000
	BulkAddSDSASuffix   = "_sdsa_add"
)

const (
	BulkReassignManagerMaxLimit = 1000
	BulkReassignManagerSuffix   = "_reassign_manager"
)

const (
	RejectReasonDeny         = "deny"
	RejectReasonAllow        = "allow"
	RejectReasonCategory     = "category"
	RejectReasonDenyCategory = "deny_category" // This is for ABFL Master DSA and the requirement is to mask Rejection Category
)

var Limiter3RPSMasterDashboardAPIsMap = map[string]bool{
	"/getODBills":               true,
	"/transactions":             true,
	"/getCreditLineList":        true,
	"/getExportHistory":         true,
	"/loanStatusAggregateCount": true,
	"/getLoanList":              true,
	"/searchOnlyGetLoanList":    true,
	"/users":                    true,
	"/usersAggregateCount":      true,
	"/searchOnlyUsers":          true,
	"/listPartners":             true,
}

var SearchTypeToParam = map[string]string{
	"mobile":     "mobile",
	"email":      "email",
	"agent_code": "agent_code",
	"agent_name": "dsa_name",
}

const (
	RevertAction         = "REVERT"
	RevertAndOfferAction = "REVERT_AND_OFFER"
)

const ABFLCustomReport = "ABFL Custom Report"

const PFLUtmReport = "PFL Utm Report"
