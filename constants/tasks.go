package constants

const (
	TaskABFLCustomReport             = "reports:abflCustomReport"
	TaskLandTStatusUpdate            = "lendertaskslandt:statusupdate"
	TaskIIFLSendToLOS                = "lendertasksiifl:sendtolos"
	TaskArthanSendToLOS              = "lendertasksarthan:sendtolos"
	TaskDigioEnachPollingStatus      = "digioenach:statuspolling"
	TaskDigioEnachCancel             = "digioenach:emandatecancel"
	TaskDigioBulkPresentation        = "digiopresentation:bulkpresentation"
	TaskCashfreePresentation         = "cashfreepresentation:presentation"
	TaskTDLBringBackUsers            = "tdl:bringbackusers"
	TaskTDLBringBackInactiveUsers    = "tdl:bringbackinactiveusers"
	TaskTDLArchiveUsers              = "tdl:archiveusers"
	TaskWebhookEvents                = "webhook_events:eventstrigger"
	TaskS3Sync                       = "s3_sync_buckets_task"
	TaskExitSurvey                   = "exit_survey"
	TaskABFLPLEsign                  = "abflpl:esign"
	TaskCasheStatusUpdate            = "lendertaskscashe:statusupdate"
	TaskIndifiStatusUpdate           = "lendertasksindifi:statusupdate"
	TaskMuthootCLRepayment           = "lendertasksmuthootcl:repayment"
	TaskTdlHDFCStatusUpdate          = "lendertaskstdlhdfcstatusupdate"
	TaskInsertInModuleMapping        = "insert_in_module_mapping"
	TaskDigioUPIAutoPayStatusPolling = "digioupiautopay:upiautopaystatuspolling"
	TaskCreateTemporalSchedule       = "create_temporal_schedule"
	TaskExpireUsers                  = "expire_users"

	// Queues
	QueueNameReports              = "reports"
	QueueNameDigioEnach           = "digioenach"
	QueueNameDigioPresentation    = "digiopresentation"
	QueueNameCashfreePresentation = "cashfreepresentation"
	QueueNameIIFL                 = "iifl"
	QueueNameMuthootCL            = "muthootcl"
	QueueNameArthan               = "arthan"
	QueueNameTDL                  = "tdl"
	QueueNameInternalTesting      = "finbox_testing_queue"
	QueueNameWebhookEvents        = "webhook_events"
	QueueNameS3SyncBuckets        = "s3_sync_queue"
	QueueNameLandT                = "landtlender"
	QueueNameCasheMC              = "cashemclender"
	QueueNameIndifi               = "indifi"
	QueueNameABFLPL               = "abflpl"
	QueueTemporalScheduleCreation = "temporal_schedule_creation_queue"
	QueueNameUserExpiry           = "user_expiry"
	QueueNameHDFCTDL              = "hdfc"
	QueueNameInsertModuleMapping  = "insert_module_mapping"
	QueueNameDigioUPIAutopay      = "digioupiautopay"
	TaskPFLUtmReport              = "reports:pflUtmReport"
)
