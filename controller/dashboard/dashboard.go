package dashboard

import (
	"os"

	"finbox/go-api/conf"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/db"
	loansql "finbox/go-api/internal/repository/psql/loan"
	usersql "finbox/go-api/internal/repository/psql/user"
)

var (
	creds    = conf.LenderServiceCreds
	log      = logger.Log
	database = db.GetDB()

	ENV string = os.Getenv("STAGE")
)

var (
	loanRepo = loansql.NewLoanDBRepository(database)
	userRepo = usersql.NewUserDBRepository(database)
)
