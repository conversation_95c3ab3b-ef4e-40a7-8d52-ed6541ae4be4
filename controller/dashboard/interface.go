package dashboard

import (
	"net/http"
)

type DashboardServiceProvider interface {
	//bank
	GetBankDetailsCont(next http.Handler) http.Handler
	UpdateDSADetailsCont(next http.Handler) http.Handler

	// metadata driven
	FetchUIConfig(next http.Handler) http.Handler
	FetchUIBuilderConfig(next http.Handler) http.Handler
	FetchUIPermissions(next http.Handler) http.Handler

	SubmitFormDataCont(next http.Handler) http.Handler
	ValidateFormDataCont(next http.Handler) http.Handler

	//loan
	BulkLoanClosureCont(next http.Handler) http.Handler

	// Task
	HandleEventDetailsCont(next http.Handler) http.Handler

	GetQuestionnaireCont(next http.Handler) http.Handler
	FetchInvestigationDetailsCont(next http.Handler) http.Handler

	GetTaskListCont(next http.Handler) http.Handler
	GetTasksByLenderAndTypeCont(next http.Handler) http.Handler
	GetSoftApproveStatusCont(next http.Handler) http.Handler
	GetThirdPartyCont(next http.Handler) http.Handler

	// coapplicant
	CreateCoApplicant(next http.Handler) http.Handler
	UpdateCoApplicant(next http.Handler) http.Handler
	CreateCoApplicantV2(next http.Handler) http.Handler

	UpdateSoftApproveStatusCont(next http.Handler) http.Handler

	// Documents
	GetDocumentsCont(next http.Handler) http.Handler
	DownloadDocumentsCont(next http.Handler) http.Handler
	UploadMedia(next http.Handler) http.Handler
	InitGenerateDocsWorkflowCont(next http.Handler) http.Handler

	// masters data
	ListMastersDataCont(next http.Handler) http.Handler

	//workflow
	WorkflowActionCont(next http.Handler) http.Handler

	GetESignAttemptCont(next http.Handler) http.Handler

	GenerateWebLink(next http.Handler) http.Handler
	CreateESignAttemptCont(next http.Handler) http.Handler

	// references
	UpsertReferenceCont(next http.Handler) http.Handler

	LogoutCont(next http.Handler) http.Handler

	SetLoanOfferData(next http.Handler) http.Handler

	GetLoanDeviationCont(next http.Handler) http.Handler

	GetInsuranceWorkflowDataCont(next http.Handler) http.Handler

	GenerateEmailOTPCont(next http.Handler) http.Handler

	VerifyOTPCont(next http.Handler) http.Handler
}
