package dashboard

import (
	"context"
	"database/sql"
	"errors"
	"net/http"

	"finbox/go-api/authentication"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/clutils"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/sourceentity"
	"finbox/go-api/functions/structs"
	creditlinesql "finbox/go-api/internal/repository/psql/creditline"
	usersql "finbox/go-api/internal/repository/psql/user"
	mdashboard "finbox/go-api/models/dashboard"
	sourceentitymodel "finbox/go-api/models/sourceentity"
	"finbox/go-api/utils/general"
)

// GenerateWebLink gets the partner data for given user id
func (dsr *DashboardServiceRepository) GenerateWebLink(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		userObj := ctx.Value("user").(authentication.LenderUserStruct)
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.GenerateWebLinkParam)

		dashboardUserEmail := userObj.Email

		userLoanInfo, err := userRepo.DBGetUserByParamsV2(ctx, usersql.DBGetUserInfoParam{
			UserID: req.UserID,
		})

		switch {
		case errors.Is(err, sql.ErrNoRows):
			log.WithContext(ctx).Errorf("[GenerateWebLink] no user found for given UserID. err: %v, params: %+v", err, req)
			errorHandler.CustomError(w, http.StatusNotFound, mdashboard.UserNotFound)
		case err != nil:
			log.WithContext(ctx).Errorf("[GenerateWebLink] failed to get userLoanInfo by params. err: %v, params: %+v", err, req)
			errorHandler.ReportToSentryV2(ctx, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		if !journey.ShowWebLink(userLoanInfo.SourceEntityID, userLoanInfo.UserID) {
			errorHandler.CustomError(w, http.StatusForbidden, "insufficient permissions to generate web link")
			return
		}

		if journey.IsMuthootCLPartner(userLoanInfo.SourceEntityID) {
			// do merchant GST check only for muthoot
			exists, err := clutils.AnchorTransactionExists(userLoanInfo.SourceEntityID, req.InvoiceIDs, req.MerchantGstNumber)
			if err != nil {
				log.WithContext(ctx).Errorf("[GenerateWebLink] failed to check AnchorTransactionExists. err: %v, params: %+v", err, req)
				errorHandler.ReportToSentryV2(ctx, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
				return
			}

			if exists {
				errorHandler.CustomError(w, http.StatusConflict, "transaction already exists for this merchant")
				return
			}
		}

		if req.TransactionID != "" {
			// check for already existing transaction
			dbReq := creditlinesql.DBCountCreditLineTxnWithCreditLineIDParam{
				TransactionID:  req.TransactionID,
				SourceEntityID: userLoanInfo.SourceEntityID,
			}

			txnCount, err := creditlinesql.DBCountCreditLineTxnWithCreditLineID(ctx, &dbReq)
			if err != nil {
				log.WithContext(ctx).Errorf("[GenerateWebLink] failed to check credit line txn. err: %v, params: %+v", err, req)
				errorHandler.ReportToSentryV2(ctx, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
				return
			}

			if txnCount > 0 {
				errorHandler.CustomError(w, http.StatusConflict, "transaction ID already exists")
				return
			}
		}

		// TODO: This is a workaround and needs to fixed before Tata PayLater goes live
		if journey.IsProgramApplicable(userLoanInfo.SourceEntityID) && req.ProgramName == "" {
			var found bool
			req.ProgramName, found = sourceentitymodel.GetProgramByID(userLoanInfo.SourceEntityID)
			if !found {
				errorHandler.CustomError(w, http.StatusNotFound, "program not found for source entity")
				errorHandler.ReportToSentryWithoutRequest(err)
				return
			}
		}

		sdkParams := structs.WebSDKParamsStruct{
			SourceEntityID:            userLoanInfo.SourceEntityID,
			CustomerID:                userLoanInfo.UniqueID,
			RedirectURL:               req.RedirectURL,
			TransactionID:             req.TransactionID,
			WithdrawAmount:            req.WithdrawAmount,
			HideClose:                 false,
			OrderName:                 req.OrderName,
			SdkType:                   "",
			TTL:                       constants.DefaultSDKSessionTimeout,
			UserToken:                 req.ProgramName,
			ProgramName:               req.ProgramName,
			HidePoweredBy:             journey.HidePoweredBy(userLoanInfo.SourceEntityID),
			MerchantName:              req.MerchantName,
			MerchantBankAccountNumber: req.MerchantBankAccountNumber,
			MerchantGstNumber:         req.MerchantGstNumber,
			MerchantIfscCode:          req.MerchantIfscCode,
			InvoiceIDs:                req.InvoiceIDs,
			InvoiceDates:              req.InvoiceDates,
			MerchantEmail:             req.MerchantEmail,
			MerchantMobile:            req.MerchantMobile,
		}

		sdkURL, _, _, errMessage, errHTTPCode, err := sourceentity.GetWebSDK(sdkParams)
		if err != nil {
			log.WithContext(ctx).Errorf("[GenerateWebLink] failed to generate GetWebSDK. err: %v, params: %+v", err, req)
			errorHandler.ReportToSentryV2(ctx, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "failed to generate generate GetWebSDK")
			return
		}

		if errHTTPCode == http.StatusForbidden {
			errHTTPCode = http.StatusConflict
		}
		if errMessage != "" {
			errorHandler.CustomError(w, errHTTPCode, errMessage)
			return
		}

		usersutil.UpdateUserSource(req.UserID, "dashboard", nil)

		dateTimeNowString := general.GetTimeStampString()
		go func() {
			activityObj := activity.ActivityEvent{
				UserID:            req.UserID,
				SourceEntityID:    userLoanInfo.SourceEntityID,
				LoanApplicationID: "",
				EntityType:        constants.EntityTypeDashboardUser,
				EntityRef:         dashboardUserEmail,
				EventType:         constants.ActivityWebLinkGenerated,
				Description:       "",
			}
			activity.RegisterEvent(&activityObj, dateTimeNowString)
		}()

		var resData = map[string]interface{}{
			"url": sdkURL,
		}

		ctx = context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
