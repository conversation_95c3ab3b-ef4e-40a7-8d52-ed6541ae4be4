package dashboard

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/authentication"
	"finbox/go-api/constants"
	userprofileservice "finbox/go-api/core/services/userprofileservice/singlesubmit"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/fbxerrors"
	loansql "finbox/go-api/internal/repository/psql/loan"
	loanoffersql "finbox/go-api/internal/repository/psql/loanoffer"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	usersql "finbox/go-api/internal/repository/psql/user"
	"finbox/go-api/internal/service/configmanagement"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/thirdparty/sentinel"
	"finbox/go-api/utils/apischemamapper"
	"finbox/go-api/utils/general"
	"fmt"
	"net/http"
	"strconv"
	"strings"
)

func (dsr *DashboardServiceRepository) SubmitFormDataCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.SubmitFormDataRequest)
		dashboard := ctx.Value("dashboard").(string)

		customeErr := dsr.SubmitFormData(ctx, req, dashboard)
		if customeErr != nil {
			logger.WithContext(ctx).Errorf("[SubmitFormDataCont] error: %v, request: %v", customeErr.Err, req)
			fbxerrors.HandleCustomHTTPErrorV2(ctx, w, customeErr)
			return
		}

		resData := make(map[string]interface{})
		resData["msg"] = "success"
		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (dsr *DashboardServiceRepository) SubmitFormData(ctx context.Context, req mdashboard.SubmitFormDataRequest, dashboard string) *fbxerrors.CustomHTTPErrorV2 {
	var (
		submitConfigParam     configmanagement.GetConfigInfoParam
		submitConfigValuesMap configmanagement.ConfigKeyValueMap
		loanparam             loansql.DBGetLoanApplicationParam
	)

	loanparam.LoanApplicationID = req.LoanApplicationID
	loanDetails, err := dsr.LoanDBRepositoryProvider.DBGetLoanApplicationByParams(ctx, loanparam)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		log.WithContext(ctx).Errorf("[SubmitFormData]loan application not found. err: %v, loanparam: %+v", err, loanparam)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusNotFound,
			DisplayMessage: mdashboard.LoanDetailsNotFound,
			Err:            err,
		}
	case err != nil:
		log.WithContext(ctx).Errorf("[SubmitFormData] failed to get loan application by params. err: %v, loanparam: %+v", err, loanparam)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: constants.GenericInternalIssuesMessage,
			IsSentry:       true,
			Err:            err,
		}
	}

	userDetails, err := usersql.DBGetUserByID(ctx, req.UserID)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		log.WithContext(ctx).Errorf("[SubmitFormData] user not found. err: %v, userID: %+v", err, req.UserID)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusNotFound,
			DisplayMessage: mdashboard.UserDetailsNotFound,
			Err:            err,
		}
	case err != nil:
		log.WithContext(ctx).Errorf("[SubmitFormData] failed to get user by userID. err: %v, loanparam: %+v", err, req.UserID)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: constants.GenericInternalIssuesMessage,
			IsSentry:       true,
			Err:            err,
		}
	}

	submitConfigParam.ResourceName = req.ResourceName
	submitConfigValuesMap = map[string]any{
		"lender_id":  loanDetails.LenderID,
		"service":    dashboard,
		"section_id": req.SectionID,
	}
	configResponse, err := dsr.ConfigManagementSrvRepositoryProvider.LegacyProvider().GetConfigInfoByParam(ctx, &submitConfigParam, submitConfigValuesMap)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		log.WithContext(ctx).Errorf("[SubmitFormData] config not found. err: %v, submitConfigParam: %+v", err, submitConfigParam)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusNotFound,
			DisplayMessage: mdashboard.ConfigDataNotFound,
			Err:            err,
		}
	case err != nil:
		log.WithContext(ctx).Errorf("[SubmitFormData] failed to get config. err: %v, submitConfigParam: %+v", err, submitConfigParam)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: constants.GenericInternalIssuesMessage,
			IsSentry:       true,
			Err:            err,
		}
	}

	submitSchema, _ := apischemamapper.NewAPIMapperSchema(configResponse.Config)

	if req.ResourceName == LOAN_OFFER_TAB_RESOURCE_NAME {
		req.Data["user_id"] = loanDetails.UserID
		req.Data["lender_id"] = loanDetails.LenderID
		req.Data["reference_id"] = loanDetails.LenderID
	}

	validationErr, err := dsr.SingleSubmitService.SubmitData(ctx, userprofileservice.SingleSubmitStruct{
		UserObj: authentication.UserStruct{
			UserID:            req.UserID,
			LoanApplicationID: req.LoanApplicationID,
			Name:              userDetails.Name,
			Email:             userDetails.Email,
			Mobile:            userDetails.Mobile,
			SourceEntityID:    userDetails.SourceEntityID,
			Status:            userDetails.Status,
			UniqueID:          userDetails.UniqueID,
		},
		Tag:    submitSchema.Tag,
		Schema: submitSchema,
		Data:   req.Data,
	})
	if err != nil {
		log.WithContext(ctx).Errorf("[SubmitFormData] error from SubmitData for req:%+v, err:%v", req, err)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: constants.GenericInternalIssuesMessage,
			IsSentry:       true,
			Err:            err,
		}
	}

	if len(validationErr) > 0 {
		log.WithContext(ctx).Errorf("[SubmitFormData] validation error: %s", validationErr[0].Message)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: validationErr[0].Message,
		}
	}

	return nil
}

func (dsr *DashboardServiceRepository) ValidateFormDataCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		var (
			ctx                   = r.Context()
			attributes            = ctx.Value("attributes").(map[string]interface{})
			req                   = attributes["req"].(mdashboard.ValidateFormDataRequest)
			submitConfigParam     configmanagement.GetConfigInfoParam
			submitConfigValuesMap configmanagement.ConfigKeyValueMap
			dashboardUser         = ctx.Value("user").(authentication.DashboardUser)
			email                 = dashboardUser.GetEmail()
			successResponse       = map[string]string{
				"msg": "success",
			}
		)

		task, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{ID: &req.TaskID}, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[ValidateFormDataCont] error in getting submit config req:%v, err:%v", req, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "config data not found")
			return
		}

		submitConfigValuesMap = map[string]any{
			"unique_id": task.MasterTaskId,
		}

		submitConfigValuesMap["service"] = req.ResourceName

		submitConfigParam.ResourceName = req.ResourceName

		configResponse, err := dsr.ConfigManagementSrvRepositoryProvider.LegacyProvider().GetConfigInfoByParam(ctx, &submitConfigParam, submitConfigValuesMap)

		switch {
		case errors.Is(err, sql.ErrNoRows):
			// we are updating task if don't have validations for a task
			logger.WithContext(ctx).Infof("[ValidateFormDataCont] no config found for taskID:%s, updating task as completed", req.TaskID)

			err = updateTaskAsCompleted(ctx, req.TaskID, email, req.Remarks)
			if err != nil {
				logger.WithContext(ctx).Errorf("[ValidateFormDataCont] error updating task status req:%v, err:%v", req.TaskID, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, "error updating task")
				return
			}

			ctx = context.WithValue(ctx, "resData", successResponse)
			next.ServeHTTP(w, r.WithContext(ctx))
			return

		case err != nil:
			logger.WithContext(ctx).Errorf("[ValidateFormDataCont] error in getting submit config req:%v, err:%v", req, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		jsonSchema, err := apischemamapper.NewAPIMapperSchema(configResponse.Config)
		if err != nil {
			logger.WithContext(ctx).Errorf("[ValidateFormDataCont] error in getting mapping json schema req:%v, err:%v", req, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "config data not found")
			return
		}

		validationErr := dsr.SingleSubmitService.ValidateData(r.Context(), jsonSchema, req.Data)

		if len(validationErr) > 0 {
			resData := map[string]any{
				"errors": validationErr,
			}
			errorHandler.CustomErrorResponseV3(w, resData, http.StatusBadRequest, constants.APIUpdateUserDetailsValidationError, "request validation failed")
			return
		}

		err = updateTaskAsCompleted(ctx, req.TaskID, email, req.Remarks)
		if err != nil {
			logger.WithContext(ctx).Errorf("[ValidateFormDataCont] error in updating task status req:%v, err:%v", req.TaskID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "error Updating task")
		}

		ctx = context.WithValue(ctx, "resData", successResponse)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func updateTaskAsCompleted(ctx context.Context, taskID, updatedBy, remarks string) error {
	var (
		completedTaskState = mdashboard.Completed
		getTaskParams      taskmanagementsql.DBGetTasksParam
		getTaskStatusParam taskmanagementsql.DBGetTaskStatusParam
	)
	now := general.GetTimeStampString()
	taskmanagementRepo := taskmanagementsql.NewTaskManagementDBRepository(database)
	getTaskParams.ID = &taskID
	task, err := taskmanagementsql.DBGetTasksByParams(ctx, &getTaskParams, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UpdateTaskAsCompleted] Error getting task details for params:%+v, err:%+v", getTaskParams, err)
		return err
	}
	getTaskStatusParam.TaskType = &task.TypeId
	getTaskStatusParam.State = &completedTaskState
	status, err := taskmanagementRepo.DBGetTaskStatusByParams(ctx, &getTaskStatusParam, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UpdateTaskAsCompleted] Error getting task status params:%+v, err:%+v", getTaskStatusParam, err)
		return err
	}
	updateTaskParam := taskmanagementsql.DBUpdateTasksParam{
		StatusId:   &status.Id,
		UpdatedBy:  &updatedBy,
		UpdatedAt:  &now,
		ApprovedBy: &updatedBy,
		ApprovedAt: &now,
	}

	if remarks != "" {
		updatedMetadata, err := updateTaskMetadata(ctx, task.Metadata, "remarks", remarks)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UpdateTaskAsCompleted] %v", err)
			return err
		}
		updateTaskParam.Metadata = updatedMetadata
	}

	err = taskmanagementsql.DBUpdateTasks(ctx, nil, &updateTaskParam, taskID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UpdateTaskToCompleted] Error updating task,params:%+v, err:%v", updateTaskParam, err)
		return err
	}

	return nil
}

func updateTaskMetadata(ctx context.Context, existingMetadata, key, value string) (*json.RawMessage, error) {
	var metadata map[string]interface{}

	if existingMetadata != "" {
		err := json.Unmarshal([]byte(existingMetadata), &metadata)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UpdateTaskMetadata] Error unmarshaling existing metadata:%+v, err:%v", existingMetadata, err)
			return nil, err
		}
	} else {
		metadata = make(map[string]interface{})
	}

	metadata[key] = value

	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		logger.WithContext(ctx).Errorf("[UpdateTaskMetadata] Error marshaling updated metadata err:%v", err)
		return nil, err
	}

	rawMetadataJSON := json.RawMessage(metadataJSON)
	return &rawMetadataJSON, nil
}

func (dsr *DashboardServiceRepository) SetLoanOfferData(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.SubmitFormDataRequest)
		dashboard := ctx.Value("dashboard").(string)

		loanDetails, err := dsr.LoanDBRepositoryProvider.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{
			LoanApplicationID: req.LoanApplicationID,
		})
		switch {
		case errors.Is(err, sql.ErrNoRows):
			logger.WithContext(ctx).Errorf("[SetLoanOfferData] Failed to fetch loan details for loanApplicationID %s: %v", req.LoanApplicationID, err)
			errorHandler.CustomError(w, http.StatusNotFound, mdashboard.ErrFetchLoanApp)
			return
		case err != nil:
			logger.WithContext(ctx).Errorf("[SetLoanOfferData] Failed to fetch loan details for loanApplicationID %s: %v", req.LoanApplicationID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		userInfo, err := dsr.UserDBRepositoryProvider.DBGetUserByParamsV2(ctx, usersql.DBGetUserInfoParam{
			UserID: req.UserID,
		})
		switch {
		case errors.Is(err, sql.ErrNoRows):
			logger.WithContext(ctx).Errorf("[SetLoanOfferData] Failed to fetch user details for user ID %s: %v", req.UserID, err)
			errorHandler.CustomError(w, http.StatusNotFound, mdashboard.UserDetailsNotFound)
			return
		case err != nil:
			logger.WithContext(ctx).Errorf("[SetLoanOfferData] Failed to fetch user details for user ID %s: %v", req.UserID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			errorHandler.ReportToSentryV2(ctx, err)
			return
		}

		// fetch state of the user
		stateCode, err := general.GetTypedValueFromJSON[string](userInfo.DynamicUserInfoStr, ".loanBranchDetails.stateCode")
		if err != nil {
			logger.WithContext(ctx).Errorf("[SetLoanOfferData] Failed to fetch state for user ID %s: %v", req.UserID, err)
			errorHandler.CustomError(w, http.StatusConflict, mdashboard.InvalidState)
			return
		}

		tenureStr, ok := req.Data["tenure"].(string)
		if !ok {
			logger.WithContext(ctx).Errorf("[SetLoanOfferData] tenure not found or not a string for req: %+v", req)
			errorHandler.CustomError(w, http.StatusConflict, mdashboard.InvalidLoanTenure)
			return
		}

		// Tenure should be in year format for sentinel
		tenureMonths, err := strconv.Atoi(tenureStr)
		if err != nil {
			logger.WithContext(ctx).Errorf("[SetLoanOfferData] Invalid tenure value for req: %+v, err: %v", req, err)
			errorHandler.CustomError(w, http.StatusConflict, mdashboard.InvalidLoanTenure)
			return
		}

		tenureYears := float64(tenureMonths) / 12.0
		tenureYearsStr := fmt.Sprintf("%.2f", tenureYears)

		reqData := map[string]any{
			"product":       req.Data["product"],
			"scheme":        req.Data["scheme"],
			"property_type": req.Data["propertyType"],
			"state":         stateCode,
			"loan_amount":   req.Data["loanAmount"],
			"tenure":        tenureYearsStr,
			"roi":           general.ToDecimalString(req.Data["roi"].(string)),
			"foir":          general.ToDecimalString(req.Data["foir"].(string)),
			"ltv":           general.ToDecimalString(req.Data["ltv"].(string)),
		}

		riskGradeEvalInput := map[string]any{
			"loan_amount": req.Data["loanAmount"],
			"risk_grade":  3, // TODO: need to fetch from db, hardcoded until BRE is ready
		}

		ch1 := runEvalFlow(ctx, reqData, constants.LAPMasterDataValidationEndpoint, loanDetails, req.UserID)
		ch2 := runEvalFlow(ctx, riskGradeEvalInput, constants.LAPFetchAuthorityEndpoint, loanDetails, req.UserID)
		res1 := <-ch1
		res2 := <-ch2

		if res1.Err != "" {
			logger.WithContext(ctx).Errorf(" [SetLoanOfferData] Loan details sentinel validation failed: %v, Response: %+v", res1.Err, res1.Data)
			errorHandler.CustomError(w, http.StatusConflict, res1.Err)
			return
		}
		if res2.Err != "" {
			logger.WithContext(ctx).Errorf(" [SetLoanOfferData] Loan details sentinel validation failed: %v, Response: %+v", res2.Err, res2.Data)
			errorHandler.CustomError(w, http.StatusConflict, res2.Err)
			return
		}

		if authority, ok := res2.Data.Data.Final.Output.OutputVariables["authority"]; ok {
			req.Data["authority"] = authority
		}

		req.Data[constants.Warning] = ""
		decision, message := evaluateDecision(res1.Data)

		if message != "" {
			req.Data[constants.Warning] = message
		}

		if decision != constants.Rejected {
			loanAmount := req.Data["loanAmount"].(string)
			customErr := dsr.SubmitFormData(ctx, req, dashboard)
			if customErr != nil {
				logger.WithContext(ctx).Errorf("[SetLoanOfferData] Failed to submit form data for loanApplicationID %s: %v", req.LoanApplicationID, customErr.Err)
				fbxerrors.HandleCustomHTTPErrorV2(ctx, w, customErr)
				return
			}

			customErr = dsr.SyncLoanOfferIDToLoanApplication(ctx, loanAmount, loanDetails)
			if customErr != nil {
				logger.WithContext(ctx).Errorf("[SetLoanOfferData] Failed to sync loan offer ID for loanApplicationID %s: %v", req.LoanApplicationID, customErr.Err)
				errorHandler.CustomError(w, http.StatusInternalServerError, "Sync Loan Offer ID failed")
				return
			}
		}

		ctx = context.WithValue(ctx, "resData", map[string]string{
			"msg": "success",
		})
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func evaluateDecision(evaluationData sentinel.FetchEvaluationResStruct) (string, string) {
	// Marshal the `Data` field into raw JSON
	var message = ""
	dataBytes, err := json.Marshal(evaluationData.Data)
	if err != nil {
		logger.WithContext(context.Background()).Errorf("[evaluateDecision] Failed to marshal evaluation data: %v, data: %+v", err, evaluationData.Data)
		return constants.Rejected, message
	}

	// Parse back into a map to extract `ruleset`
	var dataMap map[string]json.RawMessage
	if err := json.Unmarshal(dataBytes, &dataMap); err != nil {
		logger.WithContext(context.Background()).Errorf("[evaluateDecision] Failed to unmarshal evaluation data: %v, data: %+v", err, evaluationData.Data)
		return constants.Rejected, message
	}

	rulesetRaw, ok := dataMap["ruleset"]
	if !ok {
		return constants.Rejected, message
	}

	var rulesets []sentinel.RuleSetOutput
	if err := json.Unmarshal(rulesetRaw, &rulesets); err != nil {
		logger.WithContext(context.Background()).Errorf("[evaluateDecision] 'ruleset' not found in evaluation data: %+v", evaluationData.Data)
		return constants.Rejected, message
	}

	var failedKeys []string
	for _, rs := range rulesets {
		for _, rule := range rs.Rules {
			if rule.Decision != constants.SentinelRuleFailed {
				continue
			}

			input, ok := rule.Output["input"].(map[string]interface{})
			if !ok {
				continue
			}

			for key := range input {
				formattedKey := strings.ToUpper(strings.ReplaceAll(key, "_", " "))
				failedKeys = append(failedKeys, formattedKey)
			}
		}
	}

	if len(failedKeys) > 0 {
		message := strings.Join(failedKeys, ", ") + " norms not met"
		return constants.Warning, message
	}

	return constants.Approved, message
}

func (dsr *DashboardServiceRepository) SyncLoanOfferIDToLoanApplication(ctx context.Context, loanAmount string, loanInfo loansql.DBGetLoanApplicationResponse) *fbxerrors.CustomHTTPErrorV2 {

	dbParams := loanoffersql.DBGetLapLoanOfferParam{
		UserID:   loanInfo.UserID,
		LenderID: loanInfo.LenderID,
	}

	loanOffer, err := loanoffersql.DBGetLapLoanOfferInfoByParams(ctx, &dbParams, nil)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		logger.WithContext(ctx).Errorf("[SyncLoanOfferIDToLoanApplication] Loan offer is not set for LoanApplicationID: %s, error: %v", loanInfo.LoanApplicationID, err)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusNotFound,
			DisplayMessage: mdashboard.LoanOfferNotFound,
			Err:            err,
		}
	case err != nil:
		logger.WithContext(ctx).Errorf("[SyncLoanOfferIDToLoanApplication] Failed to fetch loan offer for LoanApplicationID: %s, error: %v", loanInfo.LoanApplicationID, err)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: constants.GenericInternalIssuesMessage,
			IsSentry:       true,
			Err:            err,
		}
	}

	amountFloat, err := strconv.ParseFloat(loanAmount, 64)
	if err != nil {
		logger.WithContext(ctx).Errorf("[SyncLoanOfferIDToLoanApplication] Failed to parse loan amount for LoanApplicationID: %s, error: %v", loanInfo.LoanApplicationID, err)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Invalid loan amount",
			Err:            err,
		}
	}

	tenureStr, err := general.GetTypedValueFromJSON[string](loanOffer.OfferMetadataStr, ".tenure")
	if err != nil {
		logger.WithContext(ctx).Errorf("Failed to get 'tenure' from metadata for LoanApplicationID %s: %v", loanInfo.LoanApplicationID, err)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: constants.GenericInternalIssuesMessage,
			Err:            err,
		}
	}

	tenureInt, err := strconv.Atoi(tenureStr)
	if err != nil {
		logger.WithContext(ctx).Errorf("Invalid 'tenure' format for LoanApplicationID %s: %v", loanInfo.LoanApplicationID, err)
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusBadRequest,
			DisplayMessage: "Invalid tenure",
			Err:            err,
		}
	}
	updateErr := dsr.LoanDBRepositoryProvider.DBUpdateLoanApplication(ctx, nil, &loansql.DBUpdateLoanApplicationParam{LoanOfferID: loanOffer.LoanOfferID, Amount: amountFloat, Tenure: tenureInt}, loanInfo.LoanApplicationID)
	if updateErr != nil {
		logger.WithContext(ctx).Errorf("[SyncLoanOfferIDToLoanApplication] Failed to update loan offer ID for LoanApplicationID: %s, error: %v, params : %v", loanInfo.LoanApplicationID, updateErr, loansql.DBUpdateLoanApplicationParam{LoanOfferID: loanOffer.LoanOfferID, Amount: amountFloat, Tenure: tenureInt})
		return &fbxerrors.CustomHTTPErrorV2{
			StatusCode:     http.StatusInternalServerError,
			DisplayMessage: "Failed to update loan offer ID",
			Err:            updateErr,
		}
	}

	return nil
}

func runEvalFlow(ctx context.Context, reqData map[string]any, mutator string, loanDetails loansql.DBGetLoanApplicationResponse, userID string) <-chan sentinel.EvalResult {
	ch := make(chan sentinel.EvalResult, 1)

	go func() {
		defer close(ch)

		success, evalID := sentinel.TriggerEval(ctx, loanDetails.LoanApplicationID, userID, loanDetails.LenderID, loanDetails.SourceEntityID, userID, mutator, "LAP", reqData)
		if !success {
			ch <- sentinel.EvalResult{Err: "Trigger evaluation failed"}
			return
		}

		errStr, evalData := sentinel.FetchEval(ctx, userID, evalID, loanDetails.LenderID, loanDetails.SourceEntityID, mutator,
			sentinel.FetchEvalOptions{
				MaxTries: 2,
				LogInDB:  true,
			},
		)
		if errStr != "" {
			ch <- sentinel.EvalResult{Err: "Fetch evaluation failed: " + errStr}
			return
		}

		ch <- sentinel.EvalResult{Data: evalData}
	}()

	return ch
}
