package dashboard

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	mediasql "finbox/go-api/internal/repository/psql/media"
	"fmt"
	"math/rand"
	"net/http"
	"time"

	"finbox/go-api/authentication"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/emaillib"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/octopus"
	"finbox/go-api/functions/services/hyperverge"
	"finbox/go-api/functions/services/short"
	"finbox/go-api/functions/services/sms"
	"finbox/go-api/infra/s3"
	"finbox/go-api/internal/repository/psql"
	usersql "finbox/go-api/internal/repository/psql/user"
	"finbox/go-api/models/coapplicantkycdetails"
	dashboardModel "finbox/go-api/models/dashboard"
	"finbox/go-api/models/dashboarddocs"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/media"
	"finbox/go-api/models/mediaocrdata"
	"finbox/go-api/models/users"
	"finbox/go-api/models/webhooklogs"
	"finbox/go-api/utils/general"
)

const (
	VKycWebhookIdentifier = "VKycWebhookIdentifier"
)

func VKycWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"]
		var reqVKycData dashboardModel.VKycWebhookDetails

		reqByte, _ := json.Marshal(req)
		err := json.Unmarshal(reqByte, &reqVKycData)
		if err != nil {
			log.WithContext(ctx).Errorf("[VKycWebhookCont] failed to parse req. error: %v, req: %v", err, req)
			errorHandler.ReportToSentryV2(ctx, err)
			return
		}

		if reqVKycData.Data.ReviewerAction == constants.VKycReviewerActionApproved || reqVKycData.Data.ReviewerAction == constants.VKycReviewerActionRejected {

			webHookID := general.GetUUID()
			err = webhooklogs.Create(nil, webHookID, VKycWebhookIdentifier, string(reqByte))
			if err != nil {
				log.WithContext(ctx).Errorf("[VKycWebhookCont] failed to log webhook payload. err: %v, req: %v", err, string(reqByte))
				errorHandler.ReportToSentryV2(ctx, err)
				return
			}
			vKycDetails, err := usersql.DBGetVKycDetails(ctx, reqVKycData.Data.ReferenceID)
			if err != nil {
				log.WithContext(ctx).Errorf("[VKycWebhookCont] not able to fetch VKYC detail. err: %v", err)
				errorHandler.ReportToSentryV2(ctx, err)
				return
			}
			vkycDetailMetadata := make(map[string]any)
			if vKycDetails.Metadata != nil && *vKycDetails.Metadata != "" {
				err = json.Unmarshal([]byte(*vKycDetails.Metadata), &vkycDetailMetadata)
				if err != nil {
					log.WithContext(ctx).Errorf("[VKycWebhookCont] failed to parse metadata. err: %v", err)
					errorHandler.ReportToSentryV2(ctx, err)
					return
				}
			}
			vkycDetailMetadata["webhookData"] = reqVKycData
			vKycStatus := constants.VKycStatusWebhookPrefix + reqVKycData.Data.ReviewerAction

			vkycDetailMetadataInBytes, err := json.Marshal(vkycDetailMetadata)
			if err != nil {
				psql.Log.WithContext(ctx).Errorf("[VKycWebhookCont] can not marshal metaData. err: %+v", err)
				return
			}

			err = usersql.DBUpdateMetaDataInVkyc(ctx, reqVKycData.Data.ReferenceID, vKycStatus, vkycDetailMetadataInBytes)
			if err != nil {
				log.WithContext(ctx).Errorf("[VKycWebhookCont] failed to log webhook payload. err: %v", err)
				errorHandler.ReportToSentryV2(ctx, err)
				return
			}

			go InsertMediaRecoreds(reqVKycData, vKycDetails)

			// Log Activity
			go activity.ActivityLogger(
				vKycDetails.UserID,
				vKycDetails.SourceEntityID,
				vKycDetails.CreatedBy,
				constants.EntityTypeExternal,
				"Idfy Vkyc Webhook Received", "Reviewer Action : "+reqVKycData.Data.ReviewerAction,
				vKycDetails.LoanApplicationID,
				general.GetTimeStampString(),
				false)

		}

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func InsertMediaRecoreds(reqVKycData dashboardModel.VKycWebhookDetails, vKycDetails usersql.DBVKycDetailsResponse) {
	ctx := context.Background()
	uploadReq := []map[string]interface{}{}

	// Add the vKYC documents to Upload list
	for _, vKycDoc := range reqVKycData.Data.Resources.Documents {
		mediaID := general.GetUUID()

		if vKycDoc.Type == constants.VKycDocTypeIndAadhaar {
			docMap := map[string]interface{}{
				"mediaID":      mediaID,
				"mediaURL":     vKycDoc.Value,
				"mediaKey":     fmt.Sprintf("%s/%s%s", vKycDetails.UserID, constants.DocTypeVKYCAadharXML, mediaID),
				"mediaType":    constants.MediaTypeAdditionalDocuments,
				"documentType": constants.DocTypeVKYCAadharXML,
				"documntID":    constants.VKYCAadhaarXMLDocumentID,
			}
			uploadReq = append(uploadReq, docMap)

		}
		if vKycDoc.Type == constants.VKycDocTypeProfileReport {
			docMap := map[string]interface{}{
				"mediaID":      mediaID,
				"mediaURL":     vKycDoc.Value,
				"mediaKey":     fmt.Sprintf("%s/%s%s", vKycDetails.UserID, constants.DocTypeVKYCProfileReport, mediaID),
				"mediaType":    constants.MediaTypeAdditionalDocuments,
				"documentType": constants.DocTypeVKYCProfileReport,
				"documntID":    constants.VKYCProfileReportDocumentID,
			}
			uploadReq = append(uploadReq, docMap)

		}
	}
	// Add the vKYC documents to Upload list
	for _, vKycDoc := range reqVKycData.Data.Resources.Images {
		mediaID := general.GetUUID()

		if vKycDoc.Type == constants.VKycDocTypeSelfie {
			docMap := map[string]interface{}{
				"mediaID":      mediaID,
				"mediaURL":     vKycDoc.Value,
				"mediaKey":     fmt.Sprintf("%s/%s%s.png", vKycDetails.UserID, constants.DocTypeVKYCAadharSelfie, mediaID),
				"mediaType":    constants.MediaTypeAdditionalDocuments,
				"documentType": constants.DocTypeVKYCAadharSelfie,
				"documntID":    constants.VKYCAadhaarSelfieDocumentID,
			}
			uploadReq = append(uploadReq, docMap)

		}
		if vKycDoc.Attr == constants.VKycDocAttrFace && vKycDoc.Type == constants.VKycDocTypeIndAadhaar {
			docMap := map[string]interface{}{
				"mediaID":      mediaID,
				"mediaURL":     vKycDoc.Value,
				"mediaKey":     fmt.Sprintf("%s/%s%s.png", vKycDetails.UserID, constants.DocTypeVKYCAadharFace, mediaID),
				"mediaType":    constants.MediaTypeAdditionalDocuments,
				"documentType": constants.DocTypeVKYCAadharFace,
				"documntID":    constants.VKYCAadhaarFaceDocumentID,
			}
			uploadReq = append(uploadReq, docMap)

		}
	}

	for _, vKycDoc := range reqVKycData.Data.Resources.Videos {
		mediaID := general.GetUUID()
		docMap := map[string]interface{}{
			"mediaID":      mediaID,
			"mediaURL":     vKycDoc.Value,
			"mediaKey":     fmt.Sprintf("%s/%s%s.mp4", vKycDetails.UserID, constants.DocTypeVKYCVideo, mediaID),
			"mediaType":    constants.MediaTypeAdditionalDocuments,
			"documentType": constants.DocTypeVKYCVideo,
			"documntID":    constants.VKYCVideoDocumentID,
		}
		uploadReq = append(uploadReq, docMap)
	}

	isUserMainApplicant := isMainApplicant(vKycDetails.UserID)
	isUserCoApplicant, _ := usersql.IsUserCoApplicant(ctx, vKycDetails.UserID)

	// Upload KYC documents
	for _, docMap := range uploadReq {
		fileURL := docMap["mediaURL"].(string)
		objectKey := docMap["mediaKey"].(string)
		success := s3.UploadFromPublicURL(fileURL, objectKey)
		if !success {
			log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error uploading %s file to s3", docMap["mediaType"])
			return
		}
	}

	// Starting DB transaction to insert media and KYC detail into DB
	tx, err := database.Beginx()
	if err != nil {
		log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error starting transaction,err: %v,  UserID : %s", err, vKycDetails.UserID)
		return
	}
	defer tx.Rollback()

	// Insert into media table and coapplicant kyc detail table
	for _, docMap := range uploadReq {
		data := media.UploadMediaDataStruct{
			MediaID:    docMap["mediaID"].(string),
			MediaType:  docMap["mediaType"].(string),
			Path:       docMap["mediaKey"].(string),
			UserID:     vKycDetails.UserID,
			DocumentID: docMap["documntID"].(string),
		}

		err = media.Insert(tx, context.Background(), data, vKycDetails.UserID)
		if err != nil {
			log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error inserting media %s file to s3, err: %v", docMap["mediaType"], err)
			return
		}
		if isUserCoApplicant {
			err = coapplicantkycdetails.Insert(tx, coapplicantkycdetails.StructForSet{
				ID:                general.GetUUID(),
				UserID:            vKycDetails.UserID,
				LoanApplicationID: sql.NullString{String: vKycDetails.LoanApplicationID, Valid: vKycDetails.LoanApplicationID != ""},
				MediaID:           docMap["mediaID"].(string),
				DocType:           docMap["documentType"].(string),
				DocumentID:        docMap["documntID"].(string),
				Status:            constants.KYCDocStatusUploaded,
				ReviewStatus:      0,
			})
			if err != nil {
				log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error inserting co applicant kyc details for VKYC, UserID : %s, err: %v", vKycDetails.UserID, err)
				return
			}
		}

		if isUserMainApplicant {

			userdashboarddoc := dashboarddocs.DashboardDoc{
				DocID:             general.GetUUID(),
				DocumentID:        sql.NullString{String: docMap["documntID"].(string), Valid: true},
				MediaID:           docMap["mediaID"].(string),
				LoanApplicationID: vKycDetails.LoanApplicationID,
				CreatedBy:         vKycDetails.UserID,
				Status:            true,
				ReviewStatus:      0,
				EntityType:        constants.EntityTypeCustomer,
			}
			err = userdashboarddoc.Insert(tx)

			if err != nil {
				log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error inserting Main applicant kyc details for VKYC, UserID : %s, err: %v", vKycDetails.UserID, err)
				return
			}
		}

		if docMap["documentType"].(string) == constants.DocTypeVKYCAadharXML {
			kycData, err := hyperverge.GetXMLStructFromS3(docMap["mediaKey"].(string))
			if err != nil {
				log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error Not able to get Aadhar XML data from s3, UserID : %s, err: %v", vKycDetails.UserID, err)
				return
			}

			bytes, err := json.Marshal(kycData)
			if err != nil {
				log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error in mershaling Aadhar XML data from s3, UserID : %s, err: %v", vKycDetails.UserID, err)
				return
			}
			err = mediaocrdata.Insert(docMap["mediaID"].(string), string(bytes), constants.DocTypeAadhaarXML, false)
			if err != nil {
				log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error inserting into media_ocr_data, UserID : %s, err: %v", vKycDetails.UserID, err)
				return
			}
		}

	}

	err = tx.Commit()
	if err != nil {
		log.WithContext(ctx).Errorf("[InsertMediaRecoreds] error commit transaction,err: %v,  UserID : %s", err, vKycDetails.UserID)
		return
	}
}

// VKycInvokeCont for invoking vkyc
func VKycInvokeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		defer errorHandler.Recovery(w, r, http.StatusConflict)
		ctx := r.Context()

		attributes := r.Context().Value("attributes").(map[string]interface{})
		req := attributes["req"].(dashboardModel.KycInvokeRequest)
		userDashboard := r.Context().Value("user")
		var userEmail string
		switch v := userDashboard.(type) {
		case authentication.LenderUserStruct:
			userEmail = v.Email
		case authentication.MasterDashboardUserStruct:
			userEmail = v.Email
		default:
			logger.WithContext(ctx).Errorf("[VKycInvokeCont] invalid user type, userType: %T, loanApplicationID:%s", v, req.LoanApplicationID)
			errorHandler.CustomError(w, http.StatusBadRequest, constants.GenericInvalidRequestMessage)
		}

		loanApplicationID := req.LoanApplicationID
		userID := req.UserID
		vKycID := general.GetUUID()

		loanApplication, err := loanapplication.Get(ctx, loanApplicationID)
		if err == sql.ErrNoRows {
			logger.Log.WithContext(ctx).Errorf("[VKycInvokeCont] error in fetching loan details err: %s", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "no active loan application found")

		} else if err != nil {
			logger.Log.WithContext(ctx).Errorf("[VKycInvokeCont] error getting loan details for err: %s", err)
			errorHandler.ReportToSentry(r, err)
			errorHandler.CustomError(w, http.StatusBadRequest, "loan details doesn't exist")
			return
		}

		userData, err := users.Get(userID)
		if err != nil {
			switch err {
			case sql.ErrNoRows:
				log.WithContext(ctx).Errorf("[VKycInvokeCont] no data found for user by userID. err: %v, req: %+v", err, req)
				errorHandler.CustomError(w, http.StatusBadRequest, dashboardModel.UserDetailsNotFound)
			default:
				log.WithContext(ctx).Errorf("[VKycInvokeCont] failed to fetch userlist by userID. err: %v, req: %+v", err, req)
				errorHandler.ReportToSentryV2(ctx, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			}
			return
		}

		vkycResponse, _, err := InvokeVKyc(ctx, userData, vKycID, loanApplication.LenderID)
		if err != nil {
			logger.Log.WithContext(ctx).Errorf("[VKycInvokeCont] error response from octopus loanApplicationID: %s", loanApplicationID)
			errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
			return
		}

		vkycResponseData := vkycResponseMapper(vkycResponse.Data)
		go func() {
			contextBG := context.Background()
			err = vkycPostProcessor(contextBG, vKycID, vkycResponseData, loanApplication, userData, loanApplicationID, userEmail)
			if err != nil {
				logger.Log.WithContext(contextBG).Errorf("[VKycInvokeCont] failed to vkyc postProcessor. err: %+v, loanID: %s", err, loanApplicationID)
				return
			}
		}()

		ctx = context.WithValue(ctx, "resData", vkycResponseData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func vkycPostProcessor(ctx context.Context,
	vKycID string,
	vkycResponseData dashboardModel.KycInvokeResponse,
	loanApplication *loanapplication.LoanApplication,
	userData users.User,
	loanApplicationID string, userEmail string) error {
	metaData := map[string]interface{}{"response": vkycResponseData}

	metaBytesData, err := json.Marshal(metaData)
	if err != nil {
		return fmt.Errorf("[vkycPostProcessor] failed to marshal metadata with error: %v", err)
	}

	// Insert VKYC Logs
	dBVKYCLogs := usersql.DBVKYCLogs{
		VKycID:            vKycID,
		LoanApplicationID: loanApplicationID,
		SourceEntityID:    loanApplication.SourceEntityID,
		UserID:            userData.ID,
		Status:            constants.VKycInitiated,
		MetadataInBytes:   metaBytesData,
		CreatedBy:         userEmail,
	}

	err = usersql.DBInsertVKYCLogs(ctx, dBVKYCLogs)
	if err != nil {
		errorHandler.ReportToSentryWithoutRequest(err)
		return fmt.Errorf("[vkycPostProcessor] error inserting logs to vkyc details with error: %v", err)
	}

	// Log Activity
	activity.ActivityLogger(userData.ID, loanApplication.SourceEntityID, userEmail, constants.EntityTypeLenderUser, constants.VKycInitiated, vkycResponseData.CaptureLink, loanApplicationID, general.GetTimeStampString(), false)

	// Prepare and send email
	emailData := map[string]interface{}{
		"Name":           userData.Name,
		"VKycLinkString": vkycResponseData.CaptureLink,
	}

	var (
		templateString = emaillib.VKycVerificationHTML
		subject        = emaillib.VKycVerificationSubject
	)

	if loanApplication.LenderID == constants.MFLBLID {
		templateString = emaillib.MFLVKycVerificationHTML
		subject = emaillib.MflVKycVerificationSubject
	}

	htmlBody := general.GetStringFromTemplate(templateString, emailData)
	if htmlBody == "" {
		logger.Log.WithContext(ctx).Errorf("[vkycPostProcessor] error in generating template for email with vkyc captured link")
	} else {
		success, errStr := emaillib.SendMail([]string{userData.Email}, []string{userData.Name}, subject, htmlBody, []emaillib.EmailAttachment{}, false)
		if !success {
			logger.Log.WithContext(ctx).Errorf("[vkycPostProcessor] error in sending email with vkyc captured link with error: %v", errStr)
		}
	}

	//shorten the url
	shortVkycCapturedLink, err := short.ShortenURL(vkycResponseData.CaptureLink, userData.ID, "")
	if err != nil {
		return fmt.Errorf("[vkycPostProcessor] error generating short url for vkyc captured link with error: %v", err)
	}

	// Send message on mobile
	linkSentToMobile := false
	if userData.Mobile != "" {
		retryCount := constants.VkycMobileMessageRetryCount
		text := fmt.Sprintf("Please open this link %s to complete your Udyog Plus Video KYC Verification ", shortVkycCapturedLink)
		template := sms.ABFLVkycVerification
		if loanApplication.LenderID == constants.MFLBLID {
			text = fmt.Sprintf("Hi Customer, Complete your Video KYC and Video PD now to process your Loan for quick disbursal. Click here: %s  *T&C Apply. Muthoot FinCorp LTD", shortVkycCapturedLink)
			template = sms.MFLVkycVerification
		}
		for retryCount > 0 && !linkSentToMobile {
			linkSentToMobile = sms.SendSMSKarix(userData.ID, userData.Mobile, text, template, false)
			if !linkSentToMobile {
				linkSentToMobile = sms.SendSMSMSG91(userData.ID, userData.Mobile, text, template)
			}
			retryCount--
		}
	}

	if !linkSentToMobile {
		return fmt.Errorf("[vkycPostProcessor] error in sending mobile message with vkyc captured link")
	}

	return nil
}

func vkycResponseMapper(resp dashboardModel.VKycResponseData) dashboardModel.KycInvokeResponse {
	return dashboardModel.KycInvokeResponse{
		CaptureExpiresAt: resp.CaptureExpiresAt,
		CaptureLink:      resp.CaptureLink,
		ProfileID:        resp.ProfileID,
	}
}

// InvokeVKyc invokes request body to call vkyc api at octopus
func InvokeVKyc(ctx context.Context, userData users.User, vKycID string, lenderID string) (resp dashboardModel.OctopusVKYCResponse, rawResponseJSON string, err error) {
	var (
		serviceType string
		serviceName string
	)
	switch lenderID {
	case constants.ABFLID:
		serviceType = constants.ServiceTypeVKYCABFL
		serviceName = constants.ServiceABFLVKyc
	case constants.MFLBLID:
		serviceType = constants.ServiceTypeVKYCMFL
		serviceName = constants.ServiceMFLVkyc
	default:
		logger.WithContext(ctx).Errorf("[InvokeVKyc] invalid lender ID %s", lenderID)
	}
	serviceID, err := conf.GetServiceID(serviceType)
	if err != nil {
		logger.Log.WithContext(ctx).Errorf("[InvokeVKyc] service does not exist err: %s", err)
		return resp, rawResponseJSON, errors.New(constants.ErrServiceIDDoesNotExist)
	}

	kycPayload, err := getVkycPayload(userData, vKycID)
	if err != nil {
		logger.Log.WithContext(ctx).Errorf("[InvokeVKyc] Error constructing kycPayload err: %s", err)
		return resp, rawResponseJSON, err
	}

	rand.New(rand.NewSource(time.Now().UnixNano()))
	custRefID := rand.Intn(9)

	payload := map[string]interface{}{
		"serviceID":   serviceID,
		"callbackURL": conf.BaseURL + "/v1/services/octopusHook",
		"dynamicHeaders": []map[string]string{
			{
				"key":   "cust-ref-id",
				"value": fmt.Sprintf("%d", custRefID),
			},
		},
		"data":      kycPayload,
		"requestID": vKycID,
	}

	invokeResp, rawResponseJSON, _, err := octopus.InvokeV2(ctx, userData.ID, serviceName, payload)
	if err != nil {
		logger.Log.WithContext(ctx).Errorf("[InvokeVKyc] received failure response from octopus err: %s", err)
		return resp, rawResponseJSON, errors.New("failure response from octopus")
	}

	if invokeResp.Message != constants.VkycInvokedStatus {
		logger.Log.WithContext(ctx).Errorf("[InvokeVKyc] Error returned from octopus err: %s", invokeResp.Message)
		return resp, rawResponseJSON, errors.New("error " + invokeResp.Message)
	}

	var vKycResponse dashboardModel.OctopusVKYCResponse
	err = json.Unmarshal([]byte(rawResponseJSON), &vKycResponse)
	if err != nil {
		logger.Log.WithContext(ctx).Errorf("[InvokeVKyc] error in unmarshal of octopus response err: %s", err)
		return
	}

	if vKycResponse.Data.CaptureLink == "" {
		logger.Log.WithContext(ctx).Errorf("[InvokeVKyc] error response from octopus")
		return resp, rawResponseJSON, errors.New("failure response from octopus")
	}

	return vKycResponse, rawResponseJSON, nil
}

func getVkycPayload(userData users.User, vKycID string) (kycPayLoad dashboardModel.KYCPayLoad, err error) {
	dynamicUserInfo := make(map[string]interface{})

	if userData.DynamicUserInfo != "" {
		err := json.Unmarshal([]byte(userData.DynamicUserInfo), &dynamicUserInfo)
		if err != nil {
			logger.WithUser(userData.ID).Errorln(err)
			return kycPayLoad, err
		}
	}
	userAddressMap := make(map[string]interface{})

	if dynamicUserInfo["currentAddress"] != nil {
		userAddressMap = dynamicUserInfo["currentAddress"].(map[string]interface{})
	}

	firstName, middleName, lastName := general.GetFirstMiddleLastName(userData.Name, false)
	gender := constants.GenderNumToStr[*userData.Gender]

	mediaResp, err := mediasql.DBGetMediaByParams(context.Background(), &mediasql.DBGetMediaParam{
		UserID:    userData.ID,
		MediaType: "AADHAAR_PROFILE_PHOTO",
	})
	if err != nil {
		logger.WithUser(userData.ID).Errorln(err)
		return dashboardModel.KYCPayLoad{}, err
	}

	mediaLink := s3.GetPresignedURLS3(mediaResp.Path, 60)
	return dashboardModel.KYCPayLoad{
			ReferenceID: vKycID,
			Address: dashboardModel.UserAddress{
				City:          getStringFromMap(userAddressMap, "city"),
				Country:       "India",
				CountryCode:   "Ind",
				District:      getStringFromMap(userAddressMap, "city"),
				HouseNumber:   getStringFromMap(userAddressMap, "line2"),
				Pincode:       getStringFromMap(userAddressMap, "pincode"),
				State:         getStringFromMap(userAddressMap, "state"),
				StreetAddress: getStringFromMap(userAddressMap, "line1"),
				Type:          getStringFromMap(userAddressMap, "residentType"),
			},
			Dob:    userData.DOB,
			Gender: gender,
			Pan:    userData.PAN,
			Name: dashboardModel.UserName{
				FirstName:  firstName,
				LastName:   lastName,
				MiddleName: middleName,
			},
			SecurityQuestions: []dashboardModel.SecurityQuestion{
				{
					Question: "Please confirm your DOB",
					Answer:   userData.DOB,
				},
				{
					Question: "Please confirm your Pincode",
					Answer:   getStringFromMap(userAddressMap, "pincode"),
				},
			},
			Documents: []dashboardModel.Document{
				{
					Type: "AADHAAR_PHOTO",
					URL:  mediaLink,
				},
			},
		},
		nil
}

func getStringFromMap(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

func isMainApplicant(userID string) bool {
	query := `select user_id
			    from loan_application
				where user_id = $1`
	var result string
	params := []interface{}{userID}

	err := database.Get(&result, query, params...)
	if err != nil {
		if err == sql.ErrNoRows {
			return false
		}
		logger.WithUser(userID).Errorln(err)
		return false
	}
	return true
}
