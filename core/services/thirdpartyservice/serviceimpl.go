package thirdpartyservice

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/core/models"
	"finbox/go-api/core/repository/auditrepository"
	"finbox/go-api/core/repository/thirdpartyrepository"
	"finbox/go-api/core/services/thirdpartyservice/emudhra"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/octopus"
	"finbox/go-api/functions/octopus/octocibil"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/services/pincodeapi"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/internal/fbxerrors"
	"finbox/go-api/models/lenderpayments"
	"finbox/go-api/models/users"
	clientrepository "finbox/go-api/studio/repository/client"
	"finbox/go-api/utils/general"
	panUtils "finbox/go-api/utils/pan"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/finbox-in/octoclient"
	"github.com/go-playground/validator/v10"
)

type ThirdPartyService struct {
	AuditRepository      auditrepository.IAuditRepository
	ThirdPartyRepository thirdpartyrepository.IThirdPartyRepository
	ClientRepository     clientrepository.IClientRepository
	OctoClient           tracer.IOctoClient
	EmudhraService       *emudhra.Service
}

func New(a auditrepository.IAuditRepository, oClient tracer.IOctoClient, t thirdpartyrepository.IThirdPartyRepository, c clientrepository.IClientRepository) *ThirdPartyService {
	return &ThirdPartyService{
		AuditRepository:      a,
		OctoClient:           oClient,
		ClientRepository:     c,
		ThirdPartyRepository: t,
		EmudhraService:       emudhra.NewService(),
	}
}

func (t *ThirdPartyService) HypervergePANExtendedAPI(ctx context.Context, req PANExtendedAPIRequest, opts Options) (panUtils.PANDetailedAPI, string, error) {

	logObject := models.ExternalSerivceLog{
		ID:          general.GetUUID(),
		ServiceName: constants.ServicePANDetailsHyperverge,
		UserID:      req.UserID,
		URL:         conf.OctopusBaseURL,
	}

	serviceID, _ := conf.GetServiceID(constants.ServiceTypePANDetailsHyperverge)
	// can be removed
	// if err != nil {
	// 	logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
	// 	return panUtils.PANDetailedAPI{}, "", err
	// }

	var payload = octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"pan": req.PAN,
		},
	}

	payloadString, _ := json.Marshal(payload)
	// can be removed
	// if err != nil {
	// 	logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
	// 	return panUtils.PANDetailedAPI{}, "", err
	// }
	logObject.RequestBody = string(payloadString)

	var jsonBytes []byte

	var apiResp panUtils.PANDetailedAPI

	err := retry.CustomRetry(opts.CustomRetryAttempts, opts.SleepBetweenRetries, func() error {

		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), req.UserID, logObject.ServiceName, t.OctoClient, payload)
		if err != nil {
			logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
			return retry.NewStop(err.Error())
		}

		// add test case
		jsonBytes, err = json.Marshal(response.Data)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in marshaling response into struct for user: %s, response: %v", req.UserID, response.Data))
			return errors.New("marshaling fail")
		}

		err = json.Unmarshal(jsonBytes, &apiResp)
		if err != nil {
			logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshaling response into struct for user: %s, response: %v", req.UserID, string(jsonBytes)))
			return errors.New("unmarshaling fail")
		}

		if apiResp.StatusCode != "200" {
			logger.WithUser(req.UserID).WithContext(ctx).Errorln("Errors returned in response:", apiResp)
			switch apiResp.StatusCode {
			case "429":
				return fbxerrors.ErrVendorRateLimitExceeded
			default:
				return fbxerrors.ErrExternalAPIFailed
			}
		}

		return nil
	})

	logObject.ResponseBody = string(jsonBytes)
	// TODO: Log headers and status code as well

	if err != nil {
		logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
		logObject.Success = 0
		t.AuditRepository.LogExternalService(ctx, nil, logObject)
		return panUtils.PANDetailedAPI{}, logObject.ID, err
	}

	logObject.Success = 1
	t.AuditRepository.LogExternalService(ctx, nil, logObject)

	if apiResp.StatusCode == "200" {
		if apiResp.Result.Data.Message == constants.MessageWrongPAN {
			return apiResp, logObject.ID, errors.New(constants.VendorMessageWrongPAN)
		}
	} else if apiResp.StatusCode == "429" {
		return apiResp, logObject.ID, fbxerrors.ErrVendorRateLimitExceeded
	}

	return apiResp, logObject.ID, nil

}

func (t *ThirdPartyService) GridlinesPANExtendedAPI(ctx context.Context, req PANExtendedAPIRequest, opts Options) (panUtils.PANDetailedAPI, string, error) {

	logObject := models.ExternalSerivceLog{
		ID:          general.GetUUID(),
		ServiceName: constants.ServicePANDetailsGridline,
		UserID:      req.UserID,
		URL:         conf.OctopusBaseURL,
	}

	serviceID, err := conf.GetServiceID(constants.ServiceTypePANDetailsGridline)
	if err != nil {
		logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
		return panUtils.PANDetailedAPI{}, "", err
	}

	serviceName := constants.ServicePANDetailsGridline

	var payload = octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"pan_number": req.PAN,
			"consent":    "Y",
		},
	}

	payloadString, err := json.Marshal(payload)
	if err != nil {
		logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
		return panUtils.PANDetailedAPI{}, "", err
	}

	logObject.RequestBody = string(payloadString)

	var jsonBytes []byte

	var apiResp GridLinePANDetailedAPI

	err = retry.CustomRetry(opts.CustomRetryAttempts, opts.SleepBetweenRetries, func() error {
		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), req.UserID, serviceName, t.OctoClient, payload)
		if err != nil {
			logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
			return retry.NewStop(err.Error())
		}

		jsonBytes, err = json.Marshal(response.Data)

		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in marshaling response into struct for user: %s, response: %v", req.UserID, response.Data))
			return errors.New("marshaling fail")

		}

		err = json.Unmarshal(jsonBytes, &apiResp)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshaling response into struct for user: %s, response: %v", req.UserID, string(jsonBytes)))
			logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
			return errors.New("unmarshaling fail")
		}

		if apiResp.Data.Code == "1004" || strings.Contains(strings.ToLower(apiResp.Data.Message), "does not exist") {
			return retry.NewStop(constants.VendorMessageWrongPAN)
		} else if apiResp.Status == 429 {
			return errors.New(constants.VendorRateLimitExceeded) // retry
		} else if apiResp.Status == 400 && apiResp.Data.Code == "INVALID_PAN" {
			return retry.NewStop(constants.VendorMessageWrongPAN)
		} else if apiResp.Status == 200 {
			return nil
		} else {
			logger.WithUser(req.UserID).WithContext(ctx).Errorln("Errors returned in response:", apiResp)
			return fbxerrors.ErrExternalAPIFailed
		}
	})

	logObject.ResponseBody = string(jsonBytes)
	// TODO: Log headers and status code as well

	if err != nil {
		logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
		logObject.Success = 0
		if err.Error() == constants.VendorRateLimitExceeded {
			// point to single error type
			err = fbxerrors.ErrVendorRateLimitExceeded
		} else if err.Error() == constants.VendorMessageWrongPAN {
			logObject.Success = 1 // log as a successful hit
		}
		t.AuditRepository.LogExternalService(ctx, nil, logObject)
		return panUtils.PANDetailedAPI{}, logObject.ID, err
	}

	standardAPIResp := gridlinePANToStandardPANResponseTransformer(apiResp)

	logObject.Success = 1
	t.AuditRepository.LogExternalService(ctx, nil, logObject)

	return standardAPIResp, logObject.ID, nil
}

func gridlinePANToStandardPANResponseTransformer(gridlineResp GridLinePANDetailedAPI) (standardPANResp panUtils.PANDetailedAPI) {

	// Gridline has multiple sources, which might yield state sometimes, while sometimes not.
	// Used state from gridline whenever provided, otherwise map it manually
	var state string
	gridlineSentState := gridlineResp.Data.PanData.Address.State
	state = gridlineSentState
	if state == "" {
		_, state = pincodeapi.GetCityState(gridlineResp.Data.PanData.Address.Pincode)
	}

	/*
		JQ Query for the same - the struct approach was already done, hence not used jq
			jq '{ status:"success", statusCode:"\(.status)", result:{ data:{ message:"\(.data.message)", panData:{ name: "\(.data.pan_data.first_name) \(.data.pan_data.last_name)", gender: "\(.data.pan_data.gender)", pan: "\(.data.pan_data.document_id)", firstName: "\(.data.pan_data.first_name)", middleName: null, lastName: "\(.data.pan_data.last_name)", dateOfBirth: "\(.data.pan_data.date_of_birth)", maskedAadhaarNumber: "\(.data.pan_data.masked_aadhaar_number)", address: { city: "\(.data.pan_data.address_data.city)", state: null, pincode: "\(.data.pan_data.address_data.pincode)", line1: "\(.data.pan_data.address_data.line_1)", line2: "\(.data.pan_data.address_data.line_2)" }, aadhaarLinked: .data.pan_data.aadhaar_linked } } } }'
	*/
	name := gridlineResp.Data.PanData.Name
	if len(name) < 3 {
		name = gridlineResp.Data.PanData.FirstName + gridlineResp.Data.PanData.MiddleName + gridlineResp.Data.PanData.LastName
	}
	standardAPIResp := panUtils.PANDetailedAPI{
		StatusCode: strconv.Itoa(gridlineResp.Status),
		Result: struct {
			Data panUtils.PANDetailStruct "json:\"data\""
		}{

			Data: panUtils.PANDetailStruct{

				Message: gridlineResp.Data.Message,
				PanData: panUtils.PANDetailDataStruct{
					Phone:               gridlineResp.Data.PanData.Phone,
					MiddleName:          gridlineResp.Data.PanData.MiddleName,
					FirstName:           gridlineResp.Data.PanData.FirstName,
					LastName:            gridlineResp.Data.PanData.LastName,
					Name:                name,
					Gender:              gridlineResp.Data.PanData.Gender,
					PAN:                 gridlineResp.Data.PanData.DocumentID,
					DateOfBirth:         gridlineResp.Data.PanData.DateOfBirth,
					MaskedAadhaarNumber: gridlineResp.Data.PanData.MaskedAadhaarNumber,
					AadhaarLinked:       gridlineResp.Data.PanData.AadhaarLinked,
					Address: panUtils.PANDetailAddressStruct{
						City:    gridlineResp.Data.PanData.Address.City,
						Line1:   gridlineResp.Data.PanData.Address.Line1,
						Line2:   gridlineResp.Data.PanData.Address.Line2,
						Pincode: gridlineResp.Data.PanData.Address.Pincode,
						State:   state,
					},
				},
			},
		},
	}
	if gridlineResp.Status == 200 {
		standardAPIResp.Status = "success"
	} else {
		standardAPIResp.Status = "failure"
	}
	return standardAPIResp
}

func (t *ThirdPartyService) FetchTransUnionCIBILV2Report(ctx context.Context, req TransUnionCIBILV2Req, opts Options) (resp octopus.CIBILResponseStruct, rawResponseJSON string, err error) {

	if conf.ENV != conf.ENV_PROD {
		userObj, err := users.Get(req.UserID)
		if err != nil {
			logger.WithUser(req.UserID).Errorln(err)
			return resp, octocibil.MockCIBILResponse, err
		}
		mockOctopusResponse := fmt.Sprintf(`
		{
			"data":%s,
			"status":true,
			"error":""
		}`, octocibil.MockCIBILResponse)

		err = json.Unmarshal([]byte(octocibil.MockCIBILResponse), &resp)
		if err != nil {
			logger.WithUser(req.UserID).Errorln(err)
			return resp, mockOctopusResponse, err
		}
		resp.ConsumerCreditData[0].Names[0].Name = userObj.Name
		resp.ConsumerCreditData[0].Scores[0].Score = "00794"
		return resp, mockOctopusResponse, nil
	}

	serviceID, err := conf.GetServiceID(constants.ServiceTypeCIBIL)
	if err != nil {
		return resp, rawResponseJSON, errors.New(constants.ErrServiceIDDoesNotExist)
	}

	payload := map[string]interface{}{
		"serviceID":   serviceID,
		"callbackURL": conf.BaseURL + "/v1/services/octopusHook",
		"data":        req.Payload,
	}

	_, invokeResp, rawResponseJSON, err := octopus.Invoke(req.UserID, constants.ServiceCIBIL, payload)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		return
	}
	if !invokeResp.Data.CIBILResponseStruct.ControlData.Success {
		logger.WithUser(req.UserID).Errorln("Errors returned in response:", invokeResp.Data.CIBILResponseStruct.ControlData.ErrorResponseArray)
		errorBytes, _ := json.Marshal(invokeResp.Data.CIBILResponseStruct.ControlData.ErrorResponseArray)
		return resp, rawResponseJSON, errors.New("error returned in api response: " + string(errorBytes))
	}
	if invokeResp.Message != "invoked" {
		logger.WithUser(req.UserID).Errorln("Error returned from octopus:", invokeResp.Message)
		return resp, rawResponseJSON, errors.New("error " + invokeResp.Message)
	}
	resp = invokeResp.Data.CIBILResponseStruct
	return resp, rawResponseJSON, nil
}

func (t *ThirdPartyService) FetchExperianReport(ctx context.Context, req TransUnionExperianReq, opts Options) (experianResp octopus.InvokeRespStructV2, rawResponseJSON string, externalServiceID string, err error) {
	experianResp, rawResponseJSON, externalServiceID, err = octopus.InvokeV2(ctx, req.UserID, constants.ServiceTypeExperian, req.Payload)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		return
	}
	return experianResp, rawResponseJSON, externalServiceID, nil
}

func (t *ThirdPartyService) NSDLPANVeriyAPI(ctx context.Context, req PANVerifyAPIRequest, opts Options) (bool, bool, bool, string, string, error) {

	dobObj, err := time.Parse(constants.DateFormat, req.DOB)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		return false, false, false, "", err.Error(), err
	}

	logObject := models.ExternalSerivceLog{
		ID:          general.GetUUID(),
		ServiceName: constants.ServiceTypeVerifyPANNSDLHyperverge,
		UserID:      req.UserID,
		URL:         conf.OctopusBaseURL,
	}

	serviceID, _ := conf.GetServiceID(constants.ServiceTypeVerifyPANNSDLHyperverge)
	// if err != nil {
	// 	logger.WithUser(userID).Errorln(err)
	// 	return false, false, false, externalServiceID, err.Error(), err
	// }

	payload := octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"pan":         req.PAN,
			"nameOnCard":  req.Name,
			"dateOfBirth": dobObj.Format("02-01-2006"),
		},
	}

	payloadString, _ := json.Marshal(payload)
	// if err != nil {
	// 	logger.WithUser(userID).Errorln(err)
	// 	return false, false, false, "", err.Error(), err
	// }

	logObject.RequestBody = string(payloadString)

	type responseStruct struct {
		Status     string `json:"status"`
		StatusCode int    `json:"statusCode"`
		Result     struct {
			AadhaarSeedingStatus string `json:"aadhaarSeedingStatus"`
			DateOfBirth          string `json:"dateOfBirth"`
			Name                 string `json:"name"`
			PAN                  string `json:"pan"`
			PANStatus            string `json:"panStatus"`
		} `json:"result"`
		Error string `json:"error"`
	}
	var resp responseStruct
	var jsonBytes []byte
	err = retry.CustomRetry(opts.CustomRetryAttempts, opts.SleepBetweenRetries, func() error {

		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), req.UserID, constants.ServiceTypeVerifyPANNSDLHyperverge, t.OctoClient, payload)
		if err != nil {
			logger.WithUser(req.UserID).Errorln(err)
			return retry.NewStop(err.Error())
		}

		jsonBytes, err = json.Marshal(response.Data)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in marshalling response into struct for user: %s, response: %v", req.UserID, response.Data))
			return errors.New("marshalling fail")
		}

		err = json.Unmarshal(jsonBytes, &resp)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshalling response into struct for user: %s, response: %v", req.UserID, string(jsonBytes)))
			return errors.New("unmarshalling fail")
		}

		switch resp.StatusCode {
		case 200:
			return nil
		case 422, 404:
			return retry.NewStop(resp.Status)
		default:
			return retry.NewStop(resp.Status)
		}
	})

	nameMatch := true
	dobMatch := true
	statusActive := true
	var errorString string
	if err != nil {
		switch resp.StatusCode {
		case 422:
			if strings.Contains(resp.Error, "valid Pan") {
				errorString = constants.ErrPANNotFound
				err = errors.New(errorString)
			} else {
				errorString = constants.ErrPANDOBMismatch
				dobMatch = false
				err = errors.New(errorString)
			}
		case 404:
			errorString = constants.ErrPANNotFound
			err = errors.New(errorString)
		default:
			errorString = "pan service is down, please try after sometime"
			err = errors.New(errorString)
		}
	} else {
		if resp.Result.Name != "MATCHING" {
			nameMatch = false
			errorString = constants.ErrPANNameMismatch
			err = errors.New(errorString)
		} else if resp.Result.DateOfBirth != "MATCHING" {
			dobMatch = false
			errorString = constants.ErrPANDOBMismatch
			err = errors.New(errorString)
		} else if resp.Result.PANStatus != "EXISTING AND VALID" {
			statusActive = false
			errorString = constants.ErrPANInactive
			err = errors.New(errorString)
		}
	}
	logObject.ResponseBody = string(jsonBytes)

	if err != nil {
		logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
		logObject.Success = 0
		t.AuditRepository.LogExternalService(ctx, nil, logObject)
		return nameMatch, dobMatch, statusActive, logObject.ID, errorString, err
	}

	logObject.Success = 1
	t.AuditRepository.LogExternalService(ctx, nil, logObject)

	return nameMatch, dobMatch, statusActive, logObject.ID, "", nil

}

func (t *ThirdPartyService) PayUCreatePaymentLink(ctx context.Context, req CreatePaymentPayURequest, opts Options) (PayUCreatePaymentLinkRespStruct, error) {

	logObject := models.ExternalSerivceLog{
		ID:          general.GetUUID(),
		ServiceName: constants.ServiceTypeCreatePaymentLinkPayU,
		UserID:      req.UserID,
		URL:         conf.OctopusBaseURL,
	}

	serviceID, _ := conf.GetServiceID(constants.ServiceTypeCreatePaymentLinkPayU)
	// if err != nil {
	// 	logger.WithUser(userID).Errorln(err)
	// 	return false, false, false, externalServiceID, err.Error(), err
	// }

	payload := octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"description":             "paymentLink for testing",
			"isPartialPaymentAllowed": false,
			"source":                  "API",
			"transactionId":           req.RequestID,
			"subAmount":               req.SubAmount,
			"enforcePayMethod":        "netbanking|upi",
			"successURL":              fmt.Sprintf("%s/v1/services/payURedirectHTML?txnID=%s", conf.BaseURL, req.RequestID),
			"failureURL":              fmt.Sprintf("%s/v1/services/payURedirectHTML?txnID=%s", conf.BaseURL, req.RequestID),
		},
		RequestID: req.RequestID,
	}

	payloadString, _ := json.Marshal(payload)
	// if err != nil {
	// 	logger.WithUser(userID).Errorln(err)
	// 	return false, false, false, "", err.Error(), err
	// }

	logObject.RequestBody = string(payloadString)

	var resp PayUCreatePaymentLinkRespStruct
	var jsonBytes []byte

	err := retry.CustomRetry(opts.CustomRetryAttempts, opts.SleepBetweenRetries, func() error {
		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), req.UserID, constants.ServiceTypeCreatePaymentLinkPayU, t.OctoClient, payload)
		if err != nil {
			logger.WithUser(req.UserID).Errorln(err)
			return retry.NewStop(err.Error())
		}

		jsonBytes, err = json.Marshal(response.Data)
		if err != nil {
			logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": req.UserID,
			}, fmt.Errorf("error received in marshaling response into struct, err: %v", err))
			return errors.New("marshaling fail")
		}

		err = json.Unmarshal(jsonBytes, &resp)
		if err != nil {
			logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": req.UserID,
			}, fmt.Errorf("error received in unmarshalling response into struct, err: %v", err))
			return errors.New("unmarshalling fail")
		}

		// Validate required fields
		validate := validator.New()
		if err := validate.Struct(resp); err != nil {
			logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": req.UserID,
			}, fmt.Errorf("validation failed for response struct, err: %v", err))
			return errors.New("response validation failed")
		}
		switch resp.Status {
		case 0:
			return nil
		case -1:
			return retry.NewStop(resp.Message)
		default:
			return retry.NewStop(resp.Message)
		}
	})

	logObject.ResponseBody = string(jsonBytes)

	if err != nil {
		logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
		logObject.Success = 0
		t.AuditRepository.LogExternalService(ctx, nil, logObject)
		return resp, err
	}

	logObject.Success = 1
	t.AuditRepository.LogExternalService(ctx, nil, logObject)

	return resp, nil

}

func (t *ThirdPartyService) PoonawallaNSDLPANVeriyAPI(ctx context.Context, req PANVerifyAPIRequest, opts Options) (bool, bool, bool, string, PFLNSDLPANVerifyRespStruct, string, string, error) {

	dobObj, err := time.Parse(constants.DateFormat, req.DOB)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		return false, false, false, "", PFLNSDLPANVerifyRespStruct{}, "", "", err
	}

	logObject := models.ExternalSerivceLog{
		ID:          general.GetUUID(),
		ServiceName: constants.ServiceNSDLPANVerifyPoonawalla,
		UserID:      req.UserID,
		URL:         conf.OctopusBaseURL,
	}

	serviceID, _ := conf.GetServiceID(constants.ServiceTypeNSDLPANVerifyPoonawalla)

	loc, _ := time.LoadLocation("Asia/Calcutta")
	requestTime := time.Now().In(loc).Format("2006-01-02T15:04:05")

	var resp PFLNSDLPANVerifyRespStruct
	var jsonBytes []byte

	err = retry.CustomRetry(opts.CustomRetryAttempts, opts.SleepBetweenRetries, func() error {

		payload := octoclient.OctoPayload{
			ServiceID: serviceID,
			Data: map[string]interface{}{
				"pan":            req.PAN,
				"name":           req.Name,
				"fathername":     req.Fathername,
				"dob":            dobObj.Format("02/01/2006"),
				"request-time":   requestTime,
				"transaction-id": "FBX_" + general.GenerateRandomString(20),
			},
		}

		payloadString, _ := json.Marshal(payload)
		if err != nil {
			logger.WithUser(req.UserID).Errorln(err)
			return errors.New("error in api response")
		}
		logObject.RequestBody = string(payloadString)

		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), req.UserID, logObject.ServiceName, t.OctoClient, payload)
		if err != nil {
			logger.WithUser(req.UserID).Errorln(err)
			return retry.NewStop(err.Error())
		}

		jsonBytes, err = json.Marshal(response.Data)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in marshaling response into struct for user: %s, response: %v", req.UserID, response.Data))
			return errors.New("error in api response")
		}

		err = json.Unmarshal(jsonBytes, &resp)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshaling response into struct for user: %s, response: %v", req.UserID, string(jsonBytes)))
			return errors.New("error in api response")
		}

		if resp.ResponseCode == "1" {
			return nil
		} else {
			logger.WithUser(req.UserID).Errorln("Errors returned in response:", resp)
			return errors.New("error in api response")
		}

	})

	logObject.ResponseBody = string(jsonBytes)

	nameMatch := true
	dobMatch := true
	statusActive := true
	var errorString string

	if len(resp.OutputData) > 0 && resp.ResponseCode == "1" {

		if resp.OutputData[0].PANStatus != "E" {
			statusActive = false
		}
		if resp.OutputData[0].Name != "Y" {
			nameMatch = false
		}
		if resp.OutputData[0].DOB != "Y" {
			dobMatch = false
		}

		if !statusActive || !nameMatch || !dobMatch {
			errorString = "The details provided are not matching. Please enter name, DOB and PAN number EXACTLY as per your PAN Card"
			err = errors.New(errorString)
		}
	} else {
		errorString = "pan service is down, please try after sometime"
		err = errors.New(errorString)
	}

	if err != nil {
		logger.WithUser(req.UserID).WithContext(ctx).Errorln(err)
		logObject.Success = 0
		t.AuditRepository.LogExternalService(ctx, nil, logObject)
		return nameMatch, dobMatch, statusActive, logObject.ID, PFLNSDLPANVerifyRespStruct{}, logObject.RequestBody, errorString, err
	}

	logObject.Success = 1
	t.AuditRepository.LogExternalService(ctx, nil, logObject)

	return nameMatch, dobMatch, statusActive, logObject.ID, resp, logObject.RequestBody, errorString, err
}

func (t *ThirdPartyService) GetAndSaveCreatePaymentLinkPayU(ctx context.Context, userID, loanApplicationID string, amount float64) (string, error) {
	txnID := general.GetUUID()

	// add gst to processing fee
	amount += (amount * 18) / 100

	resp, err := t.PayUCreatePaymentLink(ctx, CreatePaymentPayURequest{
		UserID:    userID,
		SubAmount: amount,
		RequestID: txnID,
	}, Options{
		CustomRetryAttempts: 4,
		SleepBetweenRetries: 10 * time.Millisecond,
	})

	if err != nil {
		logger.WithContextV2(ctx).Errorln(err)
		return "", err
	}

	if resp.Result.PaymentLink == "" {
		err = fmt.Errorf("invalid payment link")
		logger.WithContextV2(ctx).Errorln(err)
		return "", err
	}

	metadata := map[string]interface{}{
		"customerName":  resp.Result.CustomerName,
		"invoiceNumber": resp.Result.InvoiceNumber,
	}

	err = lenderpayments.Insert(nil, general.GetUUID(), loanApplicationID, userID, txnID, lenderpayments.StatusInitiated, amount, sql.NullString{Valid: false}, metadata)
	if err != nil {
		logger.WithContextV2(ctx).Errorln(err)
		return resp.Result.PaymentLink, err
	}

	return resp.Result.PaymentLink, err
}

func (t *ThirdPartyService) CheckDeviceConnectDataExistsForThreshold(ctx context.Context, userID string, interval int, intervalUnit string) (bool, error) {

	count, err := t.ThirdPartyRepository.GetDeviceConnectDetailsCountForThreshold(ctx, thirdpartyrepository.GetDeviceConnectDetailsParams{
		UserID:       userID,
		Status:       constants.DeviceConnectStatusCompleted,
		Interval:     interval,
		IntervalUnit: intervalUnit,
	})
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return false, err
	}
	return count > 0, nil
}

func (t *ThirdPartyService) PollForDeviceConnectPreds(ctx context.Context, params DeviceConnectTriggerParams) (DeviceConnectResponse, error) {

	logger.Log.Debugln("deviceConnectDetailsID:", params.DeviceConnectDetailsID)
	deviceConnectCreds, err := t.ClientRepository.GetDeviceConnectCredentials(ctx, params.KeyID)
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return DeviceConnectResponse{}, err
	}
	isFreshTriggerAttempt := (params.DeviceConnectDetailsID == "" || params.DeviceConnectDetailsID == "null")
	externalServiceID, httpStatusCode, response, err := callDeviceConnectAPI(ctx, params, deviceConnectCreds, t)
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return DeviceConnectResponse{}, err
	}
	responseStrBytes, _ := json.Marshal(response)
	responseStr := string(responseStrBytes)
	deviceConnectFetchStatus := constants.DeviceConnectStatusCompleted
	if isFreshTriggerAttempt {
		params.DeviceConnectDetailsID = general.GetUUID()
	}
	switch {
	case response.Status == "complete":
		if !isFreshTriggerAttempt {
			err = t.ThirdPartyRepository.UpdateDeviceConnectDetails(ctx, nil, params.DeviceConnectDetailsID, thirdpartyrepository.UpdateDeviceConnectDetailsParams{
				RequestID:         &response.Request_id,
				ExternalServiceID: &externalServiceID,
				Data:              &responseStr,
				Status:            &deviceConnectFetchStatus,
			})
		} else {
			err = t.ThirdPartyRepository.InsertDeviceConnectDetails(ctx, nil, models.DeviceConnectDetails{
				ID:                        params.DeviceConnectDetailsID,
				UserID:                    params.UserID,
				Status:                    deviceConnectFetchStatus,
				CreatedAt:                 general.GetTimeStampString(),
				CreatedByNullable:         sql.NullString{Valid: true, String: params.UserID},
				ExternalServiceIDNullable: sql.NullString{Valid: true, String: externalServiceID},
				RequestIDNullable:         sql.NullString{Valid: true, String: response.Request_id},
				DeviceConnectDetails:      sql.NullString{Valid: true, String: responseStr},
			})
		}
	case response.Status == "not_found", strings.Contains(strings.ToLower(response.Message), "incorrect api key"), (httpStatusCode != http.StatusOK && response.Status != "in_progress"):
		deviceConnectFetchStatus = constants.DeviceConnectStatusFailed
		if !isFreshTriggerAttempt {
			err = t.ThirdPartyRepository.UpdateDeviceConnectDetails(ctx, nil, params.DeviceConnectDetailsID, thirdpartyrepository.UpdateDeviceConnectDetailsParams{
				RequestID:         &response.Request_id,
				ExternalServiceID: &externalServiceID,
				Data:              &responseStr,
				Status:            &deviceConnectFetchStatus,
			})
		} else {
			err = t.ThirdPartyRepository.InsertDeviceConnectDetails(ctx, nil, models.DeviceConnectDetails{
				ID:                        params.DeviceConnectDetailsID,
				UserID:                    params.UserID,
				Status:                    deviceConnectFetchStatus,
				CreatedAt:                 general.GetTimeStampString(),
				CreatedByNullable:         sql.NullString{Valid: true, String: params.UserID},
				ExternalServiceIDNullable: sql.NullString{Valid: true, String: externalServiceID},
				RequestIDNullable:         sql.NullString{Valid: true, String: response.Request_id},
				DeviceConnectDetails:      sql.NullString{Valid: true, String: responseStr},
			})
		}
	default:
		deviceConnectFetchStatus = constants.DeviceConnectStatusInProgress
		if isFreshTriggerAttempt {
			err = t.ThirdPartyRepository.InsertDeviceConnectDetails(ctx, nil, models.DeviceConnectDetails{
				ID:                params.DeviceConnectDetailsID,
				UserID:            params.UserID,
				Status:            deviceConnectFetchStatus,
				CreatedAt:         general.GetTimeStampString(),
				CreatedByNullable: sql.NullString{Valid: true, String: params.UserID},
			})
		}
	}
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return DeviceConnectResponse{}, err
	}
	return DeviceConnectResponse{
		DeviceConnectDetailsID: params.DeviceConnectDetailsID,
		HTTPStatusCode:         httpStatusCode,
		Status:                 constants.DeviceConnectStatusToStrMapping[deviceConnectFetchStatus],
		ErrorMessage:           response.Message,
		Poll:                   !(response.Status == "complete" || response.Status == "not_found" || strings.Contains(strings.ToLower(response.Message), "incorrect api key")),
	}, nil
}

func (t *ThirdPartyService) ProcessDeviceConnectPredictors(ctx context.Context, userID, deviceConnectDetailsID, predictorVersion string, opts DeviceConnectPredictorsFetchOptions) error {

	predictors := []string{}
	if opts.AndroidID {
		predictors = append(predictors, "android_ids")
	}
	if opts.DeviceID {
		predictors = append(predictors, "latest_device_fingerprint")
		predictors = append(predictors, "first_android_id")
	}
	preds, err := t.ThirdPartyRepository.GetDeviceConnectPredictors(ctx, deviceConnectDetailsID, predictors)
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return err
	}
	var (
		predAndroidID sql.NullString
		predDeviceID  sql.NullString
	)
	for idx := range preds {
		if preds[idx].Name == "android_ids" {
			val, _ := preds[idx].Value.(string)
			if val != "" {
				predAndroidID = sql.NullString{Valid: true, String: val}
			}
		}
		if preds[idx].Name == "latest_device_fingerprint" {
			val, _ := preds[idx].Value.(string)
			if val != "" && predictorVersion == constants.SDKVersionWeb {
				predDeviceID = sql.NullString{Valid: true, String: val}
			}
		}
		if preds[idx].Name == "first_android_id" {
			val, _ := preds[idx].Value.(string)
			if val != "" && predictorVersion != constants.SDKVersionWeb {
				predDeviceID = sql.NullString{Valid: true, String: val}
			}
		}
	}
	if predAndroidID.Valid {
		err = t.ThirdPartyRepository.InsertAndroidID(ctx, nil, userID, predAndroidID.String)
		if err != nil {
			logger.Log.WithContext(ctx).Errorln(err)
			return err
		}
	}
	if predDeviceID.Valid {
		err = t.ThirdPartyRepository.InsertDeviceID(ctx, nil, userID, deviceConnectDetailsID, predDeviceID.String)
		if err != nil {
			logger.Log.WithContext(ctx).Errorln(err)
			return err
		}
	}
	return nil
}

// EmudhraSigningWithoutEStamp implements the Emudhra signing service for documents without e-stamp
func (t *ThirdPartyService) EmudhraSigningWithoutEStamp(ctx context.Context, req emudhra.SigningWithoutEStampRequest) (emudhra.SigningWithoutEStampResponse, error) {
	return t.EmudhraService.SignWithoutEStamp(ctx, req)
}

// EmudhraEmbeddedSigning implements the Emudhra embedded signing service for documents with e-stamp
func (t *ThirdPartyService) EmudhraEmbeddedSigning(ctx context.Context, req emudhra.EmbeddedSigningRequest) (emudhra.EmbeddedSigningResponse, error) {
	return t.EmudhraService.EmbeddedSigning(ctx, req)
}

// EmudhraDownloadDocument implements the Emudhra document download service
func (t *ThirdPartyService) EmudhraDownloadDocument(ctx context.Context, req emudhra.DownloadDocumentRequest) (emudhra.DownloadDocumentResponse, error) {
	return t.EmudhraService.DownloadDocument(ctx, req)
}
