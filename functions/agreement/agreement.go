package agreement

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/functions/agreementutils"
	"finbox/go-api/functions/downloadables"
	"finbox/go-api/functions/paymentutils"
	"finbox/go-api/functions/repayment"
	"finbox/go-api/models/deviceconnectdetails"
	"finbox/go-api/models/insurance"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/loankycdetails"
	"finbox/go-api/models/media"
	"finbox/go-api/models/multiuserloanrelations"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/userbankdetails"
	"finbox/go-api/models/userbusiness"
	"finbox/go-api/models/userloandetails"
	"finbox/go-api/models/userlocation"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"
	"finbox/go-api/thirdparty/karix"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	errorspkg "errors"

	"github.com/jmoiron/sqlx"
	excelize "github.com/xuri/excelize/v2"

	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/emaillib"
	"finbox/go-api/functions/helpers"
	"finbox/go-api/functions/insuranceutils"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/kfs"
	"finbox/go-api/functions/lenders/mintifi"
	"finbox/go-api/functions/lenders/tatacapital"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/loanprogram"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/partner"
	"finbox/go-api/functions/services/ekyc"
	"finbox/go-api/functions/services/pincodeapi"
	"finbox/go-api/functions/services/short"
	"finbox/go-api/functions/services/sms"
	"finbox/go-api/functions/structs"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/functions/zipper"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/lender"
	sourceentitymodel "finbox/go-api/models/sourceentity"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/convert"
	"finbox/go-api/utils/general"
)

var database = db.GetDB()

func getOccupation(sourceEntityID string) string {
	switch sourceEntityID {
	case constants.GeniusID:
		return "Sales Executive"
	case constants.LetsTransportID:
		return "Driver Partner"
	case constants.ArzoooID:
		return "Retailer"
	}
	return "Merchant"
}

func getFacilityTypePurpose(loanType string, purpose string, sourceEntityID string, lenderID string) (string, string) {
	facilityType := "Personal Loan"
	if lenderID == constants.ArthanLenderID {
		purpose = "Business/Personal use"
		facilityType = "Loan"
	}
	if loanType == constants.LoanTypeOverDraft {
		facilityType = "Overdraft"
		purpose = "Credit Line"
	}
	if loanType == constants.LoanTypeCreditLine {
		facilityType = "Credit Line"
		purpose = "Credit Line"
	}

	if sourceEntityID == constants.ArzoooID {
		purpose = "Invoice Financing"
	}
	return facilityType, purpose
}

func getMandateInfo(loanApplicationID string, sourceEntityID string) (string, string) {
	// check in cashfree first
	type dbStruct struct {
		MandateNo   string
		MandateDate string
	}
	var dbObj dbStruct
	query := `SELECT sub_reference_id as mandateno, to_char(created_at, 'DD-MM-YYYY') as mandatedate
				FROM enach_subscription where status != 'INITIALIZED' and loan_application_id = $1
				order by created_at desc limit 1`
	err := database.Get(&dbObj, query, loanApplicationID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	return dbObj.MandateNo, dbObj.MandateDate
}

func GetBusinessUANDetails(userID string) BusinessDetails {
	var dbObj struct {
		CompanyName         string    `db:"company_name"`
		CompanyType         string    `db:"company_type"`
		UAN                 string    `db:"uan"`
		Constitution        string    `db:"constitution"`
		Address             string    `db:"address"`
		DateOfIncorporation time.Time `db:"date_of_incorporation"`
		RegistrationDate    time.Time `db:"registration_date"`
		UserID              string    `db:"user_id"`
	}

	query := `
	SELECT company_name, company_type, uan, constitution, address, date_of_incorporation, registration_date, user_id
	FROM user_business_uan
	WHERE user_id = $1
	ORDER BY created_at DESC
	LIMIT 1
	`

	if err := database.Get(&dbObj, query, userID); err != nil {
		logger.WithUser(userID).Errorln(err)
	}

	var gstin string
	var gstins []string
	query = `SELECT gstin from user_business_gst where user_id = $1`
	err := database.Select(&gstins, query, userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
	}
	if len(gstins) > 0 {
		gstin = strings.Join(gstins, ", ")
	}

	businessUANDetails := BusinessDetails{
		BusinessName:        dbObj.CompanyName,
		BusinessAddresss:    dbObj.Address,
		Constitution:        dbObj.Constitution,
		DateOfIncorporation: dbObj.DateOfIncorporation.Format("02-01-2006"),
		Gstin:               gstin,
		UAN:                 dbObj.UAN,
	}

	return businessUANDetails

}

// GetBusinessDetails returns business name, business address, gstin, date of incorporation, nature of entity and business relation
func GetBusinessDetails(userID string, preLoanData map[string]interface{}) BusinessDetails {
	type dbStruct struct {
		FirmName             string
		Constitution         string
		BusinessAddress      string
		DateOfIncorporation  string
		CommunicationAddress string
	}
	var dbObj dbStruct
	businessDetails := BusinessDetails{}
	query := `SELECT coalesce(firm_name, '') as firmname,
				coalesce(constitution, '') as constitution,
				coalesce(business_address::TEXT, '') as businessaddress,
				coalesce(to_char(date_of_incorporation, 'DD-MM-YYYY'), '') as dateofincorporation,
				coalesce(communication_address::TEXT, '') as communicationaddress
			from user_business where user_id = $1`
	err := database.Get(&dbObj, query, userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
	}

	var gstin string
	var gstins []string
	query = `SELECT gstin from user_business_gst where user_id = $1`
	err = database.Select(&gstins, query, userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
	}
	if len(gstins) > 0 {
		gstin = strings.Join(gstins, ", ")
	}

	if dbObj.FirmName == "" {
		dbObj.FirmName, _ = preLoanData["firmName"].(string)
	}
	if dbObj.BusinessAddress == "" {
		line1, _ := preLoanData["storeAddressLine1"].(string)
		line2, _ := preLoanData["storeAddressLine2"].(string)
		city, _ := preLoanData["storeAddressCity"].(string)
		state, _ := preLoanData["storeAddressState"].(string)
		pincode, _ := preLoanData["storeAddressPincode"].(string)
		if line1 != "" || line2 != "" || city != "" || state != "" || pincode != "" {
			dbObj.BusinessAddress = fmt.Sprintf("%s, %s, %s, %s - %s", line1, line2, city, state, pincode)
			businessDetails.City = city
			businessDetails.Address = fmt.Sprintf("%s, %s", line1, line2)
			businessDetails.State = state
			businessDetails.Pincode = pincode
			businessDetails.BusinessAddresss = dbObj.BusinessAddress
		}
	} else {
		addressMap := make(map[string]any)
		err = json.Unmarshal([]byte(dbObj.BusinessAddress), &addressMap)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
		} else {
			line1, _ := addressMap["line1"].(string)
			line2, _ := addressMap["line2"].(string)
			city, _ := addressMap["city"].(string)
			state, _ := addressMap["state"].(string)
			pincode, _ := addressMap["pincode"].(string)
			dbObj.BusinessAddress = line1 + ", " + line2 + ", " + city + ", " + state + " " + pincode
			businessDetails.City = addressMap["city"].(string)
			businessDetails.Address = fmt.Sprintf("%s, %s", addressMap["line1"].(string), addressMap["line2"].(string))
			businessDetails.State = addressMap["state"].(string)
			businessDetails.Pincode = addressMap["pincode"].(string)
			businessDetails.BusinessAddresss = dbObj.BusinessAddress
		}
	}
	if dbObj.Constitution == "" {
		dbObj.Constitution, _ = preLoanData["constitution"].(string)
		if dbObj.Constitution == "" {
			dbObj.Constitution = "Individual"
		}
	}
	if dbObj.CommunicationAddress != "" {

		addressMap := make(map[string]interface{})
		err = json.Unmarshal([]byte(dbObj.CommunicationAddress), &addressMap)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
		} else {
			line1, _ := addressMap["line1"].(string)
			line2, _ := addressMap["line2"].(string)
			city, _ := addressMap["city"].(string)
			state, _ := addressMap["state"].(string)
			pincode, _ := addressMap["pincode"].(string)
			dbObj.CommunicationAddress = line1 + ", " + line2 + ", " + city + ", " + state + " " + pincode
		}

	}
	if dbObj.DateOfIncorporation == "" {
		dbObj.DateOfIncorporation, _ = preLoanData["dtIncorporation"].(string)
	}
	if gstin == "" {
		gstin, _ = preLoanData["gstin"].(string)
	}
	var businessRelation string

	switch dbObj.Constitution {
	case constants.Proprietorship:
		businessRelation = "Proprietor"
	case constants.Partnership:
		businessRelation = "Partner"
	case constants.PrivateLimited:
		businessRelation = "Director"
	default:
		businessRelation = "Others"
	}

	businessDetails.BusinessName = dbObj.FirmName
	businessDetails.BusinessAddresss = dbObj.BusinessAddress
	businessDetails.Gstin = gstin
	businessDetails.DateOfIncorporation = dbObj.DateOfIncorporation
	businessDetails.Constitution = dbObj.Constitution
	businessDetails.BusinessRelation = businessRelation
	businessDetails.CommunicationAddress = dbObj.CommunicationAddress

	return businessDetails
}

func GetPreAppovedJourneyBusinessDetails(sourceEntityID string, partnerData map[string]interface{}) BusinessDetails {
	var businessName, businessAddresss, gstin, dateOfIncorporation, constitution, businessRelation, communicationAddress string

	if journey.IsABFLBLSourcing(sourceEntityID) {
		businessName, _ = partnerData["firmName"].(string)
		//Business address and communication address are same.
		businessAddresss, _ = partnerData["businessAdress"].(string)
		communicationAddress = businessAddresss

		gstin, _ = partnerData["gstin"].(string)
		dateOfIncorporation, _ = partnerData["dateOfIncorporation"].(string)

	}

	businessDetails := BusinessDetails{
		BusinessName:         businessName,
		BusinessAddresss:     businessAddresss,
		Gstin:                gstin,
		DateOfIncorporation:  dateOfIncorporation,
		Constitution:         constitution,
		BusinessRelation:     businessRelation,
		CommunicationAddress: communicationAddress,
	}

	return businessDetails
}

/*
GetPaymentScheduleXLSX Returns excelize file object for payment schedule xlsx file
*/
func GetPaymentScheduleXLSX(loanApplicationNum string, name string, emi float64, emiDates []time.Time) *excelize.File {
	f := excelize.NewFile()
	// add top fields
	f.SetCellValue("Sheet1", "A1", "Loan Application Number")
	f.SetCellValue("Sheet1", "B1", loanApplicationNum)
	f.SetCellValue("Sheet1", "A2", "Customer Name")
	f.SetCellValue("Sheet1", "B2", name)
	// add installment headings
	f.SetCellValue("Sheet1", "A5", "Installment Number")
	f.SetCellValue("Sheet1", "B5", "Due Date")
	f.SetCellValue("Sheet1", "C5", "Installment Amount")
	// add installments
	for index, currentDate := range emiDates {
		rowNum := index + 6
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", rowNum), index+1)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", rowNum), currentDate.Format("2 Jan 2006"))
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", rowNum), emi)
	}
	return f
}

/*
GenerateKYCZip generates a zip file containing all approved and uploaded images
and uploads to s3 and updates its path in loan application
*/
func GenerateKYCZip(loanApplicationID string) {
	type loanDBResp struct {
		UserID            string `db:"user_id"`
		LoanApplicationNo string `db:"loan_application_no"`
		LenderID          string `db:"lender_id"`
	}
	var loanObj loanDBResp
	query := `select user_id, loan_application_no, lender_id
					from loan_application
				where kyc_status >= $1 and loan_application_id = $2`
	err := database.Get(&loanObj, query, constants.LoanKYCStatusDocApproved, loanApplicationID)
	if err != nil {
		err := fmt.Errorf("error in generating KYC ZIP for loanApplicationID: %s, err: %s", loanApplicationID, err.Error())
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}
	query = `select media_id as front_media_id,
					coalesce(back_media_id ,'') as back_media_id, doc_type
					from loan_kyc_details
				where loan_id = $1 and status = $2;`
	type kycResp struct {
		FrontMediaID string `db:"front_media_id"`
		BackMediaID  string `db:"back_media_id"`
		DocType      string `db:"doc_type"`
	}
	kycDetails := []kycResp{}
	err = database.Select(&kycDetails, query, loanApplicationID, constants.KYCDocStatusUploaded)
	if err != nil {
		err := fmt.Errorf("error in generating KYC ZIP for loanApplicationID: %s, err: %s", loanApplicationID, err.Error())
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}
	mediaIDArr := []string{}
	for _, kycSingleObj := range kycDetails {
		if kycSingleObj.FrontMediaID != "" {
			mediaIDArr = append(mediaIDArr, kycSingleObj.FrontMediaID)
		}
		if kycSingleObj.BackMediaID != "" {
			mediaIDArr = append(mediaIDArr, kycSingleObj.BackMediaID)
		}
	}
	mediaIDMap := make(map[string]string)    // stores URL for each media id
	mediaIDExtMap := make(map[string]string) // stores extension for each media id
	if len(mediaIDArr) > 0 {
		type mediaStruct struct {
			Path    string
			MediaID string `db:"media_id"`
		}
		mObj := []mediaStruct{}
		tempMediaQuery := fmt.Sprintf("select path, media_id from media where status = %d and media_id in (?); ", constants.MediaStatusActive)
		mediaQuery, args, err := sqlx.In(tempMediaQuery, mediaIDArr)
		if err != nil {
			err := fmt.Errorf("error in generating KYC ZIP for loanApplicationID: %s, err: %s", loanApplicationID, err.Error())
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return
		}
		mediaQuery = database.Rebind(mediaQuery)
		err = database.Select(&mObj, mediaQuery, args...)
		if err != nil {
			err := fmt.Errorf("error in generating KYC ZIP for loanApplicationID: %s, err: %s", loanApplicationID, err.Error())
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return
		}
		for _, mediaObj := range mObj {
			mediaIDMap[mediaObj.MediaID] = s3.GetPresignedURLS3(mediaObj.Path, 300)
			mediaIDExtMap[mediaObj.MediaID] = strings.Split(mediaObj.Path, ".")[1]
		}
	} else {
		err := fmt.Errorf("no media found, cannot generate KYC ZIP for loanApplicationID: %s :(", loanApplicationID)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}
	filesToZip := []zipper.ZipFileStruct{}
	for _, kycSingleObj := range kycDetails {
		if kycSingleObj.FrontMediaID != "" {
			fileName := fmt.Sprintf("%s_%s.%s", kycSingleObj.DocType, "Front", mediaIDExtMap[kycSingleObj.FrontMediaID])
			filesToZip = append(filesToZip, zipper.ZipFileStruct{Name: fileName, URL: mediaIDMap[kycSingleObj.FrontMediaID]})
		}
		if kycSingleObj.BackMediaID != "" {
			fileName := fmt.Sprintf("%s_%s.%s", kycSingleObj.DocType, "Back", mediaIDExtMap[kycSingleObj.BackMediaID])
			filesToZip = append(filesToZip, zipper.ZipFileStruct{Name: fileName, URL: mediaIDMap[kycSingleObj.BackMediaID]})
		}
	}

	// now check for ekyc
	var ekycResp ekyc.EKYCFileResp
	ekycResp, err = ekyc.GetEKYCFilePaths(loanApplicationID)
	addEKYC := true
	if err != nil {
		if err == sql.ErrNoRows {
			addEKYC = false
		} else {
			err := fmt.Errorf("error in generating zip file for loanApplicationID: %s, err: %s", loanApplicationID, err.Error())
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return
		}
	}
	if addEKYC && ekycResp.EKYCPath != "" {
		fileName := fmt.Sprintf("ekyc.%s", strings.Split(ekycResp.EKYCPath, ".")[1])
		filesToZip = append(filesToZip, zipper.ZipFileStruct{Name: fileName, URL: s3.GetPresignedURLS3(ekycResp.EKYCPath, 300)})
		if ekycResp.XMLPath != "" {
			// if xml file available add it as well
			xmlFileName := strings.Split(ekycResp.XMLPath, "/")[1]
			if ekycResp.ShareKey != "" {
				key := general.GenerateHashedKey(conf.AesEncryptionPassword, conf.AesEncryptionKey)
				decShareKey := general.AESDecrypt(ekycResp.ShareKey, key)
				xmlFileName = decShareKey + "_" + xmlFileName
			}
			filesToZip = append(filesToZip, zipper.ZipFileStruct{Name: xmlFileName, URL: s3.GetPresignedURLS3(ekycResp.XMLPath, 300)})
		}

	} else {
		err := fmt.Errorf("no E-KYC to add in zip file for loanApplicationID: %s, %v, %s", loanApplicationID, addEKYC, ekycResp.EKYCPath)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}

	outputFile := fmt.Sprintf("%s/kyc_%s.zip", loanObj.UserID, loanObj.LoanApplicationNo)
	// password is <last 4 digit of application number>_<lender initial>_finbox
	var last4ApplicationNo string
	sizeLoanApplicationNo := len(loanObj.LoanApplicationNo)
	if sizeLoanApplicationNo < 4 {
		last4ApplicationNo = loanObj.LoanApplicationNo
	} else {
		last4ApplicationNo = loanObj.LoanApplicationNo[len(loanObj.LoanApplicationNo)-4:]
	}

	lenderInitial := "xy"
	switch loanObj.LenderID {
	case constants.ArthanLenderID:
		lenderInitial = "af"
	case constants.TrustLenderID:
		lenderInitial = "tl"
	case constants.IIFLID:
		lenderInitial = "il"
	case constants.MintifiID:
		lenderInitial = "mf"
	}

	password := fmt.Sprintf("%s_%s_finbox", last4ApplicationNo, lenderInitial)
	err = zipper.CreateAndUploadZipFile(filesToZip, outputFile, password)
	if err != nil {
		err := fmt.Errorf("cannot generate KYC ZIP, zipper failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}
	// now update the object key in db
	query = `update loan_application set
					 updated_at = current_timestamp,
					 kyc_zip_path = $1
					where loan_application_id = $2 `

	_, err = database.Exec(query, outputFile, loanApplicationID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}
}

/*
EmailLenderKYCApproved sends email to lender with user information and kyc zip file
*/
func EmailLenderKYCApproved(loanApplicationID string, approvedDate time.Time) {
	defer errorHandler.RecoveryNoResponse()
	// fetch data from DB
	type dbResStruct struct {
		UserID            string `db:"user_id"`
		Name              string
		DOB               string
		PAN               string
		Email             string
		CurrentAddress    string `db:"current_address"`
		Mobile            string
		FathersName       string `db:"fathers_name"`
		Gender            int
		LoanApplicationNo string `db:"loan_application_no"`
		LenderID          string `db:"lender_id"`
		SourceEntityID    string `db:"source_entity_id"`
		KYCZipPath        string `db:"kyc_zip_path"`
		UniqueID          string `db:"unique_id"`
		LoanType          string `db:"loan_type"`
	}
	obj := dbResStruct{}

	query := `select u.user_id, u.name, to_char(u.dob, 'yyyy-mm-dd') as dob, u.mobile, u.pan,
						u.gender, coalesce(u.email, '') as email, u.unique_id,
						a.loan_application_no, a.lender_id, a.source_entity_id, a.kyc_zip_path,
						coalesce(d.permanent_address, d.current_address) as current_address, a.loan_type
					from users u, loan_application a, user_loan_details d
					where u.user_id = a.user_id
						and d.user_id = a.user_id
						and d.loan_application_id = a.loan_application_id
						and a.loan_application_id = $1`
	err := database.Get(&obj, query, loanApplicationID)

	if err != nil {
		err := fmt.Errorf("send KYC Lender Mail for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}
	// calculate required values
	dateOfBirth, err := time.Parse("2006-01-02", obj.DOB)
	if err != nil {
		err := fmt.Errorf("DOB parsing failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}
	ageValue := general.AgeAt(dateOfBirth, time.Now())

	// check for available offers
	// TODO: add support for multiple offers
	type offerTemplateStruct struct {
		Method            string
		Interest          float64
		Tenure            int
		ProcessingFee     float64
		ProcessingFeeType string
		GST               float64
	}
	offerTemplate := offerTemplateStruct{}
	query = `SELECT method, interest, tenure, processing_fee as processingfee,
				processing_fee_type as processingfeetype, gst
				FROM loan_offer_template where source_entity_id = $1 and lender_id = $2 limit 1`
	err = database.Get(&offerTemplate, query, obj.SourceEntityID, obj.LenderID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		panic(err)
	}
	var amount float64
	eligibleObj := loanprogram.GetUpdatedLoanOfferTemplate(obj.UserID, obj.SourceEntityID, offerTemplate.Tenure)
	amount = eligibleObj.EligibleAmount
	if eligibleObj.Interest != -1 {
		offerTemplate.Interest = eligibleObj.Interest
	}
	if eligibleObj.ProcessingFee != -1 {
		offerTemplate.ProcessingFee = eligibleObj.ProcessingFee
	}
	if eligibleObj.ProcessingFeeType != "" {
		offerTemplate.ProcessingFeeType = eligibleObj.ProcessingFeeType
	}
	if eligibleObj.Tenure != 0 {
		offerTemplate.Tenure = eligibleObj.Tenure
	}
	offerTemplate.ProcessingFee = calc.CalculateProcessingFee(amount, offerTemplate.ProcessingFee, offerTemplate.ProcessingFeeType)

	var iHTMLBuilder strings.Builder
	var emiHTMLBuilder strings.Builder

	// calculate emi if not credit line
	emi, advanceEMI, _ := calc.GetEMI(offerTemplate.Method, amount, offerTemplate.Tenure, offerTemplate.Interest, approvedDate, obj.SourceEntityID, obj.LenderID, obj.UserID)
	emiDates := calc.GetEMIDates(approvedDate, offerTemplate.Tenure, obj.SourceEntityID, obj.LenderID, offerTemplate.Method, obj.UserID, false)
	if obj.SourceEntityID == constants.PagarBookID {
		emiDates = []time.Time{partner.GetPBDueDate(obj.UserID, approvedDate)}
	}
	for index, currentDate := range emiDates {
		fmt.Fprintf(&iHTMLBuilder, "<tr><td>Installment %d</td><td>&#8377; %.2f</td></tr>", index+1, emi)
		fmt.Fprintf(&emiHTMLBuilder, "<tr><td>%s</td><td>&#8377; %.2f</td></tr>", currentDate.Format("2 Jan 2006"), emi)
	}

	addressMap := make(map[string]string)
	err = json.Unmarshal([]byte(obj.CurrentAddress), &addressMap)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}
	currentAddress := addressMap["line1"] + ", " + addressMap["line2"] + ", " + addressMap["city"] + ", " + addressMap["state"] + " " + addressMap["pincode"]

	// now fetch kyc documents
	type kycDocStruct struct {
		Doctype string
		Docname string
	}
	kycObjs := []kycDocStruct{}
	query = `select k.doc_type as doctype, d.document_name as docname
					from loan_kyc_details k, documents d
				where k.document_id = d.document_id
					and k.loan_id = $1
					and k.status = $2
			`
	err = database.Select(&kycObjs, query, loanApplicationID, constants.KYCDocStatusUploaded)
	if err != nil {
		err := fmt.Errorf("send KYC Lender Mail failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	// calculate required values
	var kycHTMLBuilder strings.Builder
	for _, kycObj := range kycObjs {
		docType := strings.ReplaceAll(kycObj.Doctype, "_", " ")
		docName := strings.ReplaceAll(kycObj.Docname, "_", " ")
		fmt.Fprintf(&kycHTMLBuilder, "<tr><td>%s</td><td>%s</td></tr>", docType, docName)
	}

	emailSubjectTemplate := emaillib.LenderKitEmailSubject
	paths, _ := ekyc.GetEKYCFilePaths(loanApplicationID)
	if paths.XMLPath != "" {
		emailSubjectTemplate = "E-KYC, " + emailSubjectTemplate
		fmt.Fprint(&kycHTMLBuilder, "<tr><td>Address Proof</td><td>AADHAAR XML</td></tr>")
	}

	// now fetch lender information
	lenderObj, err := lender.Get(obj.LenderID)
	if err != nil {
		err := fmt.Errorf("send KYC Lender Mail failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}

	// now fetch source entity information
	type sourceEntityResp struct {
		SourceEntityName string
		LegalName        string
		Emails           string
	}
	var sourceEntityObj sourceEntityResp
	query = `select source_entity_name as sourceentityname,
				coalesce(emails, '') as emails,
				legal_name as legalname
			from source_entity where source_entity_id = $1`
	err = database.Get(&sourceEntityObj, query, obj.SourceEntityID)
	if err != nil {
		err := fmt.Errorf("send KYC Lender Mail failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return
	}

	// now send email to lender (lender kit) along with kyc zip file

	// calculate values required
	// _, premium, _ := insuranceutils.GetUnpaidInsurance(loanApplicationID)
	premium, _ := insuranceutils.GetUnpaidPremium(loanApplicationID)
	insuranceType, _ := insuranceutils.GetUnpaidInsuranceType(loanApplicationID)
	disbursalAmount := calc.CalculateDisbursalAmount(amount, float64(offerTemplate.ProcessingFee), offerTemplate.GST, advanceEMI, premium, commonutils.OtherCharges(obj.LenderID, obj.UserID), insuranceType)
	var underwritingHTMLBuilder strings.Builder
	underwritingRules := underwriting.GetUnderWritingRules()
	for _, underwritingRule := range underwritingRules {
		fmt.Fprintf(&underwritingHTMLBuilder, `<tr style="border: 1px solid black;"><td style="text-align:left;border: 1px solid black;">%s</td><td>%s</td></tr>`, underwritingRule, "PASS")
	}

	emailData := map[string]interface{}{
		"UserName":           obj.Name,
		"PanNumber":          obj.PAN,
		"CurrentAddress":     currentAddress,
		"PinCode":            addressMap["pincode"],
		"Mobile":             obj.Mobile,
		"Email":              obj.Email,
		"DOB":                dateOfBirth.Format("2 Jan 2006"),
		"Age":                ageValue,
		"KycDocsHTML":        kycHTMLBuilder.String(),
		"LoanID":             obj.LoanApplicationNo,
		"DisbursalAmount":    disbursalAmount,
		"InstallmentRowHTML": iHTMLBuilder.String(),
		"LoanType":           obj.LoanType,
		"UnderwritingHTML":   underwritingHTMLBuilder.String(),
	}
	if general.InArr(obj.LoanType, []string{constants.LoanTypeCreditLine, constants.LoanTypeOverDraft}) {
		emailData["DisbursalAmount"] = "NA"
		emailData["InstallmentRowHTML"] = ""
	}
	attachments := []emaillib.EmailAttachment{
		{
			Path:     s3.GetPresignedURLS3(obj.KYCZipPath, 300),
			FileName: fmt.Sprintf("KYC_%s.zip", obj.LoanApplicationNo),
		},
	}
	emailSubject := general.GetStringFromTemplate(emailSubjectTemplate, map[string]interface{}{
		"LoanID":   obj.LoanApplicationNo,
		"UserName": obj.Name,
	})
	emailBody := general.GetStringFromTemplate(emaillib.LenderKitEmailHTML, emailData)
	lenderEmails := strings.Split(lenderObj.Emails, ",")
	lenderNames := []string{}
	for i := 0; i < len(lenderEmails); i++ {
		lenderNames = append(lenderNames, lenderObj.LenderName)
	}
	emaillib.SendMail(lenderEmails, lenderNames, emailSubject, emailBody, attachments, true)
}

/*
GenerateUnsignedAgreement generates an unsigned loan agreement for given loan application
and uploads to S3 and returns the object key and html content for it
*/
func GenerateUnsignedAgreement(loanApplicationID string, approvedDate time.Time, forceUpdateNameInAgreement bool, name string) (string, string) {
	// fetch data from DB
	obj, err := agreementutils.GetAgreementDataValues(loanApplicationID, forceUpdateNameInAgreement, name)
	if err != nil {
		err := fmt.Errorf("unsigned agreement failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	var isABFLPreApprovedJourney bool

	if journey.IsABFLBLSourcing(obj.SourceEntityID) {
		isABFLPreApprovedJourney, _ = journey.IsPreApprovedJourney(obj.SourceEntityID)
	}

	documentChargesPlusGST := obj.DocumentCharges + (obj.DocumentCharges * obj.GST / 100.0)
	//update emi calculation method for some lender and source entity combinations
	obj.Method = journey.GetEMIMethodForAgreement(obj.LoanType, obj.LenderID, obj.Method)
	// now fetch source entity information
	sourceEntityObj, err := sourceentitymodel.FetchSourceEntityInfo(obj.SourceEntityID)
	if err != nil {
		err := fmt.Errorf("unsigned agreement failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}
	// now fetch lender information
	lenderObj, err := lender.Get(obj.LenderID)
	if err != nil {
		err := fmt.Errorf("unsigned agreement failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	// get pre loan data
	preLoanData := make(map[string]interface{})
	if obj.PreLoanData != "" {
		err = json.Unmarshal([]byte(obj.PreLoanData), &preLoanData)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}
	}

	//get Partner Data
	partnerData := make(map[string]interface{})
	if obj.PartnerData != "" {
		err = json.Unmarshal([]byte(obj.PartnerData), &partnerData)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln("Error in unmarshalling partner data", err)
		}
	}

	var businessDetails BusinessDetails
	if isABFLPreApprovedJourney {
		businessDetails = GetPreAppovedJourneyBusinessDetails(obj.SourceEntityID, partnerData)
	} else {
		businessDetails = GetBusinessDetails(obj.UserID, preLoanData)
	}
	// calculate required values
	dateOfBirth, err := time.Parse("2006-01-02", obj.DOB)
	if err != nil {
		err := fmt.Errorf("DOB parsing failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}
	ageValue := general.AgeAt(dateOfBirth, time.Now())

	var (
		emi        float64
		advanceEMI float64
		emiDates   []time.Time
		totalEmi   []float64

		ediTenure int
		ediAmount float64
		ediDates  []time.Time

		totalInterestAmount float64
		amountToBePaid      float64

		installmentFrequency string
	)

	installmentFrequency, err = journey.GetInstallmentFrequency(obj.UserID, obj.SourceEntityID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return "", ""
	}

	emi, advanceEMI, _ = calc.GetEMI(obj.Method, obj.Amount, obj.Tenure, obj.Interest, approvedDate, obj.SourceEntityID, obj.LenderID, obj.UserID)
	emiDates = calc.GetEMIDates(approvedDate, obj.Tenure, obj.SourceEntityID, obj.LenderID, obj.Method, obj.UserID, false)

	switch installmentFrequency {
	case constants.InstallmentProgrammeDaily:
		//ceil for mflbl
		ediAmount = math.Ceil(paymentutils.CalculateEDIAmount(obj.Amount, (obj.Interest / 365), obj.Tenure))
		ediDates = calc.GetEDIDates(approvedDate, obj.Tenure, obj.SourceEntityID, obj.LenderID)
		advanceEMI = 0
	}

	if obj.SourceEntityID == constants.PagarBookID {
		emiDates = []time.Time{partner.GetPBDueDate(obj.UserID, approvedDate)}
	}

	//variables for rps tables based on installment frequency
	var (
		iHTMLBuilder                          strings.Builder
		amortScheduleHTMLBuilder              strings.Builder
		emiHTMLBuilder                        strings.Builder
		emiWithPricipalAndInterestHTMLBuilder strings.Builder

		amortSchedule                                     []structs.AmortSchedule
		ediWithOutstandingPrincipalAndInterestHTMLBuilder strings.Builder

		ediAmortSchedule            []structs.EDIAmortSchedule
		ediAmortScheduleHTMLBuilder strings.Builder

		CoApplicantNames string
	)
	loc, _ := time.LoadLocation("Asia/Calcutta")

	// calculate values required
	// _, premium, _ := insuranceutils.GetUnpaidInsurance(loanApplicationID)
	premium, _ := insuranceutils.GetUnpaidPremium(loanApplicationID)
	insuranceType, _ := insuranceutils.GetUnpaidInsuranceType(loanApplicationID)
	ihoInsurancePremium, _ := insuranceutils.InsurancePremiumByInsuranceType(loanApplicationID, constants.IHO)

	insuranceDetails, err := insurance.GetByLoanApplicationID(loanApplicationID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return "", ""
	}

	var (
		isLifeInsuranceApplicable    bool
		lifeInsurancePremium         float64
		lifeInsurancePremiumWithGST  float64
		lifeInsuranceNomineeRelation string
		lifeInsuranceNomineeDOB      string
		lifeInsuranceNomineeName     string
	)
	for _, insuranceDetail := range insuranceDetails {
		if insuranceDetail.InsuranceType == constants.InsuranceTypeLifeInsurance {
			lifeInsurancePremium = insuranceDetail.Premium
			// 18% premium for LifeInsurance
			lifeInsurancePremiumWithGST = calc.CalculateAmountWithGST(lifeInsurancePremium, 18)
			var lifeInsuranceNominee structs.InsuranceNominee
			err = json.Unmarshal([]byte(insuranceDetail.Nominee), &lifeInsuranceNominee)
			if err != nil && err != sql.ErrNoRows {
				logger.WithLoanApplication(loanApplicationID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return "", ""
			}

			lifeInsuranceNomineeDOB = lifeInsuranceNominee.DOB
			lifeInsuranceNomineeRelation = lifeInsuranceNominee.Relation
			lifeInsuranceNomineeName = lifeInsuranceNominee.Name
			isLifeInsuranceApplicable = true
		}
	}

	otherCharges := commonutils.OtherCharges(obj.LenderID, obj.UserID)
	if journey.IsTopUpJourney(obj.UserID, obj.SourceEntityID) {
		// don't deduct outstanding loan amount and interest charge from disbursal amount. APR will be calculated on total loan amount
		otherCharges[constants.PFLChargeNameOutstandingPrincipleAmount] = 0
		otherCharges[constants.PFLChargeNameOutstandingPrincipleInterest] = 0
	}

	disbursalAmount := calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, advanceEMI, premium, otherCharges, insuranceType, obj.SourceEntityID, obj.UserID)
	totalEmi = append(totalEmi, -disbursalAmount)
	if obj.LenderID == constants.PoonawallaFincorpID {
		// calculating disbural amount without adding any additional charge except ProcessingFee to minimise APR
		aprApplicableDisbAmount := calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, advanceEMI, 0, otherCharges, insuranceType, obj.SourceEntityID, obj.UserID)
		if isPoonawallaTopUp := journey.IsTopUpJourney(obj.UserID, obj.SourceEntityID); isPoonawallaTopUp {
			aprApplicableDisbAmount = calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, 0, 0, otherCharges, insuranceType, obj.SourceEntityID, obj.UserID)
		}
		totalEmi[0] = -aprApplicableDisbAmount
	}

	// return math.Round((amount-processingFee-(gst*processingFee/100.0)-insurancePremium-(gst*insurancePremium/100.0)-advanceEMI-float64(otherChargesAmount))*100) / 100
	amortSchedule = calc.GetAmortSchedule(obj.Amount, obj.Interest, obj.Tenure, obj.Method, obj.SourceEntityID, obj.LenderID, obj.UserID)
	amountToBePaid = obj.Amount

	for index, currentDate := range emiDates {
		curValues := amortSchedule[index]
		curEMI := curValues.EMI
		if index == 0 && obj.LenderID == constants.PoonawallaFincorpID {
			curEMI += advanceEMI
		}
		totalEmi = append(totalEmi, curEMI)
		totalInterestAmount += curValues.InterestAmount
		amountToBePaid += curValues.InterestAmount
		fmt.Fprintf(&iHTMLBuilder, "<tr><td>Installment %d</td><td>&#8377; %.2f</td></tr>", index+1, curEMI)
		fmt.Fprintf(&emiHTMLBuilder, "<tr><td>%s</td><td>&#8377; %.2f</td></tr>", currentDate.Format("2 Jan 2006"), curEMI)
		if obj.LenderID == constants.PoonawallaFincorpID {
			// STODO: Change
			if index == 0 {
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> </tr>",
					index+1,
					currentDate.Format("2 Jan 2006"),
					journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal),
					journey.AmountToString(obj.LenderID, curEMI),
					journey.AmountToString(obj.LenderID, curValues.InterestAmount+advanceEMI),
					journey.AmountToString(obj.LenderID, curValues.PrincipalAmount),
					journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
			} else {
				if index%40 == 0 {
					fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr class='page-break-before'><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> </tr>",
						index+1, currentDate.Format("2 Jan 2006"),
						journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal),
						journey.AmountToString(obj.LenderID, curEMI),
						journey.AmountToString(obj.LenderID, curValues.InterestAmount),
						journey.AmountToString(obj.LenderID, curValues.PrincipalAmount), journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
				} else {
					fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> </tr>",
						index+1, currentDate.Format("2 Jan 2006"),
						journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal),
						journey.AmountToString(obj.LenderID, curEMI),
						journey.AmountToString(obj.LenderID, curValues.InterestAmount),
						journey.AmountToString(obj.LenderID, curValues.PrincipalAmount), journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
				}
			}
		} else if general.InArr(obj.LenderID, []string{constants.ABFLPLID}) {
			if index == 0 {
				// add advanceEMI and EMI for the first installment
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount+advanceEMI)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI+advanceEMI)))
			} else {
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI)))
			}
		} else if general.InArr(obj.LenderID, []string{constants.ABFLID}) {
			if index == 0 {
				// add advanceEMI and EMI for the first installment
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount+advanceEMI)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI+advanceEMI)))
			} else {
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI)))
			}
		} else if general.InArr(obj.LenderID, []string{constants.MFLBLID}) {
			fmt.Fprintf(&amortScheduleHTMLBuilder,
				"<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>",
				index+1,
				currentDate.Format("02/01/2006"),
				general.FormatCurrency(curValues.OpeningPrincipal, true),
				general.FormatCurrency(math.Ceil(curEMI), true),
				general.FormatCurrency(curValues.PrincipalAmount, true),
				general.FormatCurrency(curValues.InterestAmount, true),
			)
		} else {
			fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>", index+1, currentDate.Format("2 Jan 2006"), journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, curEMI), journey.AmountToString(obj.LenderID, curValues.InterestAmount), journey.AmountToString(obj.LenderID, curValues.PrincipalAmount), journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
		}
		fmt.Fprintf(&emiWithPricipalAndInterestHTMLBuilder, "<tr><td>%d</td><td>%s</td><td>&#8377; %.2f</td> <td>&#8377; %.2f</td> <td>&#8377; %.2f</td> </tr>", index+1, currentDate.Format("2 Jan 2006"), curEMI, curValues.PrincipalAmount, curValues.InterestAmount)
	}

	//EDI calculations and ,RPS table builder
	switch installmentFrequency {
	case constants.InstallmentProgrammeDaily:
		disbursalAmount = calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, 0, 0, map[string]int{}, "", obj.SourceEntityID, obj.UserID)
		ediAmortSchedule = calc.GetEDIAmortSchedule(obj.Amount, obj.Interest, ediAmount, obj.Tenure, obj.Method, obj.SourceEntityID, obj.LenderID)
		amountToBePaid = obj.Amount
		totalInterestAmount = 0
		for index, currentDate := range ediDates {
			if index >= len(ediAmortSchedule) {
				break
			}
			curValues := ediAmortSchedule[index]
			curEDI := curValues.EDI
			totalInterestAmount += curValues.InterestAmount
			amountToBePaid += curValues.InterestAmount
			if general.InArr(obj.LenderID, []string{constants.MFLBLID}) {
				fmt.Fprintf(&ediAmortScheduleHTMLBuilder,
					"<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>",
					index+1,
					currentDate.Format("02/01/2006"),
					general.FormatCurrency(curValues.OpeningPrincipal, true),
					general.FormatCurrency(curEDI, true),
					general.FormatCurrency(curValues.PrincipalAmount, true),
					general.FormatCurrency(curValues.InterestAmount, true),
				)
			}
		}
	}

	if obj.Method == constants.MethodDailyReducing || (obj.Method == constants.MethodReducingBalance && journey.IsMuthootEDIPartner(obj.SourceEntityID)) {

		if journey.IsMuthootEDIPartner(obj.SourceEntityID) {

			obj.Tenure = 30 * obj.Tenure
			count := 1
			lastClosing := obj.Amount
			currEDIAmount := math.Ceil(paymentutils.CalculateEDIAmount(obj.Amount, obj.Interest/360, obj.Tenure))
			openingPrincipal := obj.Amount
			lastRow := false

			for count <= obj.Tenure {

				currInterestBeforeRound := (lastClosing * obj.Interest / 365) / 100
				currInterest := math.Round(currInterestBeforeRound*100) / 100

				if currInterest < 0 {
					currInterest = 0.0
				}

				if openingPrincipal < currEDIAmount {
					lastRow = true
				}

				if lastRow {
					currEDIAmount = float64(int(openingPrincipal))
				}

				principalAmount := currEDIAmount - currInterest
				closingPrincipal := openingPrincipal - principalAmount
				lastClosing = closingPrincipal

				if lastRow {
					closingPrincipal = 0
				}

				fmt.Fprintf(&ediWithOutstandingPrincipalAndInterestHTMLBuilder, ""+
					"<tr>"+
					"<td>%d</td>"+
					"<td>&#8377; %.0f </td>"+
					"<td>&#8377; %.2f </td>"+
					"<td>&#8377; %.2f </td>"+
					"<td>&#8377; %.2f </td>"+
					"<td>&#8377; %.0f </td>"+
					"</tr>",

					count, openingPrincipal, currEDIAmount, math.Round(principalAmount*100)/100, currInterest, closingPrincipal)

				openingPrincipal = closingPrincipal
				count++

				if lastRow {
					break
				}
			}

		} else {
			ediTenure = obj.Tenure
			ediAmount = math.Ceil((emi*float64(len(emiDates)) + advanceEMI) / float64(ediTenure))
			totalInterestAmount = 0
			dailyInterestRate := obj.Interest / 365
			count := 1
			var principalAmount = obj.Amount
			previousEdiAmount := 0.0
			for ediTenure > 0 {
				interestPerDay := (principalAmount * dailyInterestRate) / 100
				totalInterestAmount += interestPerDay
				fmt.Fprintf(&ediWithOutstandingPrincipalAndInterestHTMLBuilder, "<tr><td>%d</td><td>&#8377; %.2f </td><td>&#8377; %.2f </td><td>&#8377; %.2f </td><td>&#8377; %.2f </td></tr>",
					count, principalAmount, ediAmount-interestPerDay, interestPerDay, ediAmount)
				previousEdiAmount = ediAmount - interestPerDay
				principalAmount -= previousEdiAmount
				if count == ediTenure {
					break
				}
				count++
			}
			amountToBePaid = obj.Amount + totalInterestAmount
		}
	}

	// Computing current and permanent address
	addressMap := make(map[string]any)
	currentAddress := ""
	if obj.CurrentAddress != "" {
		currentAddress, err = populateAddress(loanApplicationID, obj.CurrentAddress, addressMap)
	}

	permanentAddress := ""
	if obj.PermanentAddress != "" {
		permanentAddress, err = populateAddress(loanApplicationID, obj.PermanentAddress, addressMap)
	}
	if journey.IsABFLBLSourcing(obj.SourceEntityID) {
		if journey.IsMultiUserConstitutionJourney(obj.UserID, obj.SourceEntityID) {
			permanentAddress = businessDetails.CommunicationAddress
		} else if obj.AdditionalPermanentAddress != "" {
			permanentAddress, err = populateAddress(loanApplicationID, obj.AdditionalPermanentAddress, addressMap)
		}
	}

	if currentAddress == "" && permanentAddress == "" {
		err := fmt.Errorf("both CurrentAddress and PermanentAddress cannot be empty")
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	signedURL := s3.GetPresignedURLS3(obj.SignPath, 300)

	// now fetch kyc documents
	type kycDocStruct struct {
		Doctype    string
		Docname    string
		Name       string
		Identifier string
	}
	kycObjs := []kycDocStruct{}
	query := `select k.doc_type as doctype, d.document_name as docname, coalesce(k.name, '') as name,
       			coalesce(k.identifier, '') as identifier
					from loan_kyc_details k, documents d
				where k.document_id = d.document_id
					and k.loan_id = $1
					and k.status = $2
			`
	err = database.Select(&kycObjs, query, loanApplicationID, constants.KYCDocStatusUploaded)
	if err != nil {
		err := fmt.Errorf("unsigned agreement failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	aadhaarName := ""
	aadhaarIdentifier := ""
	// calculate required values
	proofOfAddress := "AADHAAR E-KYC"
	var kycHTMLBuilder strings.Builder
	for _, kycObj := range kycObjs {
		if kycObj.Doctype == constants.DocTypeDigilockerAadhaar {
			aadhaarName = strings.ToUpper(kycObj.Name)
		} else if kycObj.Doctype == constants.DocumentNameAadhaarPhoto {
			aadhaarIdentifier = kycObj.Identifier
		}
		docType := strings.ReplaceAll(kycObj.Doctype, "_", " ")
		docName := strings.ReplaceAll(kycObj.Docname, "_", " ")
		if docType == "Address Proof" {
			proofOfAddress = docName
		}
		fmt.Fprintf(&kycHTMLBuilder, "<tr><td>%s</td><td>%s</td></tr>", docType, docName)
	}

	if journey.IsPFLSourcing(obj.SourceEntityID) && aadhaarName != "" {
		obj.Name = aadhaarName
	}

	// now fetch bank details
	type bankStruct struct {
		Accountnumber string
		Ifsc          string
		Bank          string
		HolderName    string
		AccountType   string
	}
	bankObj := bankStruct{}
	query = `select ubd.account_number as accountnumber, ubd.ifsc_code as ifsc,
					ubd.bank_name as bank,
					coalesce(ubd.name, '') as holdername,
					coalesce(ubd.account_type, '') as accounttype
					from user_bank_details ubd
					where user_bank_details_id in
						(select user_bank_details_id from loan_application
							where loan_application_id = $1 )
					and ubd.status = $2;`
	err = database.Get(&bankObj, query, loanApplicationID, constants.UserBankStatusApproved)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	if bankObj.AccountType == "" {
		bankObj.AccountType = "savings"
	}
	unsignedAgreementTemplatePath := obj.UnsignedAgreementTemplate

	var coBorrowers []CoBorrower

	if journey.IsABFLBLSourcing(obj.SourceEntityID) && (journey.IsProprietorshipConstitutionJourney(obj.UserID, obj.SourceEntityID) || isABFLPreApprovedJourney) {
		coBorrowers = append(coBorrowers, CoBorrower{
			UserName:               obj.Name,
			Phone:                  obj.Mobile,
			CurrentAddress:         currentAddress,
			PanCard:                obj.PAN,
			Email:                  obj.Email,
			ShareHoldingPercentage: "100",
			Relation:               "Proprietor",
		})
	}

	if journey.IsABFLBLSourcing(obj.SourceEntityID) && journey.IsCoApplicantJourney(obj.UserID, obj.SourceEntityID) {
		coApplicantUsersDetails, err := multiuserloanrelations.GetCoApplicantUserDetails(obj.UserID, true)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}

		var coApplicantDetails multiuserloanrelations.CoApplicantUserDetails
		if len(coApplicantUsersDetails) > 0 {
			coApplicantDetails = coApplicantUsersDetails[0]
		} else {
			err := fmt.Errorf("ALERT: user in co-applicant journey without any co-applicants for user_id: %s", obj.UserID)
			logger.WithUser(obj.UserID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return "", ""
		}

		coApplicantAddressMap := make(map[string]string)
		coApplicantCurrentAddress := ""
		if coApplicantDetails.CurrentAddress != "" {

			err = json.Unmarshal([]byte(coApplicantDetails.CurrentAddress), &coApplicantAddressMap)
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Errorln(err)
				return "", ""
			}
			coApplicantCurrentAddress = coApplicantAddressMap["line1"] + ", " + coApplicantAddressMap["line2"] + ", " + coApplicantAddressMap["city"] + ", " + coApplicantAddressMap["state"] + " " + coApplicantDetails.Pincode

		}
		coBorrowers = append(coBorrowers, CoBorrower{
			UserName:               coApplicantDetails.Name,
			Phone:                  coApplicantDetails.Mobile,
			PanCard:                coApplicantDetails.PAN,
			Email:                  coApplicantDetails.Email,
			CurrentAddress:         coApplicantCurrentAddress,
			Relation:               coApplicantDetails.Relation,
			ShareHoldingPercentage: "NA",
		})

	} else if journey.IsABFLBLSourcing(obj.SourceEntityID) && journey.IsMultiUserConstitutionJourney(obj.UserID, obj.SourceEntityID) {
		active := true
		coApplicantUsersDetails, err := multiuserloanrelations.GetDirectorsDetails(multiuserloanrelations.DirectorsInfoFilter{
			ParentUserID: obj.UserID,
			Active:       &active,
		})
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}

		for _, coApplicantDetails := range coApplicantUsersDetails {

			var coApplicantCurrentAddress string

			var dynamicUserInfo multiuserloanrelations.DynamicUserInfo
			err = json.Unmarshal([]byte(coApplicantDetails.DynamicUserInfo), &dynamicUserInfo)
			if err != nil {

				logger.WithLoanApplication(loanApplicationID).Errorln(err)
				return "", ""

			}

			coApplicantCurrentAddress = dynamicUserInfo.CurrentAddress.Line1 + ", " + dynamicUserInfo.CurrentAddress.Line2 + ", " + dynamicUserInfo.CurrentAddress.City + ", " + dynamicUserInfo.CurrentAddress.State + " ," + coApplicantDetails.Pincode

			coBorrowers = append(coBorrowers, CoBorrower{
				UserName:               coApplicantDetails.Name,
				Phone:                  coApplicantDetails.Mobile,
				PanCard:                coApplicantDetails.PAN,
				Email:                  coApplicantDetails.Email,
				CurrentAddress:         coApplicantCurrentAddress,
				Relation:               coApplicantDetails.Relation,
				ShareHoldingPercentage: strconv.FormatFloat(coApplicantDetails.ShareHoldingPercentage, 'f', 2, 64),
			})

		}

	}

	// Names of all the co-applicants
	var coApplicantsList []string
	for _, coBorrower := range coBorrowers {
		coApplicantsList = append(coApplicantsList, coBorrower.UserName)

	}
	CoApplicantNames = strings.Join(coApplicantsList, ", ")

	occupation := getOccupation(obj.SourceEntityID)
	if general.InArr(obj.SourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) || journey.IIFLAgg(obj.SourceEntityID, false).IsAgg {
		occupation, _ = preLoanData["occupationType"].(string)

		if journey.IsTemporalFlow(obj.UserID, obj.SourceEntityID, usermodulemapping.PersonalInfo) {
			occupation, _ = preLoanData["employmentStatus"].(string)
			loanType, _, _ := journey.GetLoanType(obj.SourceEntityID)
			if loanType == constants.LoanTypePersonalLoan {
				occupation = constants.OccupationTypeMapForAgreement[occupation]
			} else if loanType == constants.LoanTypeBusinessLoan {
				occupation = "Business"
			}
		}
	}
	facilityType, purpose := getFacilityTypePurpose(obj.LoanType, obj.LoanPurpose, obj.SourceEntityID, obj.LenderID)

	advanceEMINo := 0
	if advanceEMI > 0 {
		advanceEMINo = 1
	}

	mandateNo, mandateDate := getMandateInfo(loanApplicationID, obj.SourceEntityID)

	loanAmountWords := general.AmountInWords(obj.Amount)

	// GST on insurance premium
	insuranceGst := obj.GST
	if insuranceType == constants.InsuranceTypeHealthWellness {
		insuranceGst = 0
	}
	insurancePremiumPlusGST := calc.CalculateAmountWithGST(premium, insuranceGst)
	// rounding up in case of ABFL
	if journey.IsABFLBLSourcing(obj.SourceEntityID) {
		insurancePremiumPlusGST = math.Ceil(insurancePremiumPlusGST)
	}

	// GST on processing fee
	gstAmount := obj.ProcessingFee * obj.GST / 100
	processingFeePlusGST := obj.ProcessingFee + gstAmount
	processingFeePlusGST = math.Round(processingFeePlusGST*100) / 100
	if obj.LenderID != constants.IIFLID && journey.IsRoundOffRequired(obj.SourceEntityID, obj.LenderID) {
		processingFeePlusGST = math.Round(processingFeePlusGST)
	}

	buf := new(bytes.Buffer)
	ioRPointer := s3.GetFileStream(unsignedAgreementTemplatePath)
	if ioRPointer == nil {
		err := fmt.Errorf("file not found in s3 for loanApplicationID: %s and path: %s", loanApplicationID, unsignedAgreementTemplatePath)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}
	ioR := *ioRPointer
	buf.ReadFrom(ioR)
	templateString := buf.String()
	var RepaymentFrequency string
	var EMINos int
	switch obj.Method {
	case constants.MethodDailyReducing:
		RepaymentFrequency = "Daily"
		EMINos = ediTenure
		emi = ediAmount
	default:
		RepaymentFrequency = "Monthly"
		EMINos = len(emiDates)
	}
	kfsOtherDisclosures := journey.GetKFSOtherDisclosures(obj.LenderID, obj.SourceEntityID, EMINos, obj.LoanType)
	apr, err := calc.GetAPR(totalEmi, obj.Method)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}

	otherCharges = commonutils.OtherCharges(obj.LenderID, obj.UserID)
	if journey.IsTopUpJourney(obj.UserID, obj.SourceEntityID) {
		// don't deduct outstanding loan amount and interest charge from disbursal amount. APR will be calculated on total loan amount
		otherCharges[constants.PFLChargeNameOutstandingPrincipleAmount] = 0
		otherCharges[constants.PFLChargeNameOutstandingPrincipleInterest] = 0
	}
	stampDuty := 0
	if _, ok := otherCharges["stampDuty"]; ok {
		stampDuty = otherCharges["stampDuty"]
	}
	otherChargesAmount := 0
	for _, charge := range otherCharges {
		otherChargesAmount += charge
	}

	employerDetails, _ := users.GetEmployerDetails(obj.UserID)
	LateFeeRate, LateFeeCharge, BounceCharge, ForeclosureCharge := calc.GetContingentCharges(obj.LenderID)
	aprPercentage := ""
	if journey.IsOneMuthootPartner(obj.SourceEntityID) {
		aprPercentage = fmt.Sprintf("%.2f", obj.Interest+2.5)
	}
	docOnlinePremium, _ := insuranceutils.GetUnpaidHealthInsurance(loanApplicationID)

	var otherChargesAmountFloat float64
	var outstandingPrincipal float64
	isTopUp, _, _, _ := journey.IsTopUp(obj.SourceEntityID)
	if isTopUp {
		outstandingPrincipal, _, err = underwriting.GetForeclosureDetails(obj.UserID)
		if err != nil {
			logger.WithUser(obj.UserID).Errorln(err)
			return "", ""
		}

		amountToBePaid += outstandingPrincipal
		disbursalAmount -= outstandingPrincipal
		otherChargesAmountFloat += outstandingPrincipal
	}

	if general.InArr(obj.LenderID, []string{constants.ABFLPLID, constants.PoonawallaFincorpID}) {
		totalInterestAmount += advanceEMI
	}
	if general.InArr(obj.LenderID, []string{constants.ABFLID, constants.ABFLPLID}) || obj.LenderID == constants.PoonawallaFincorpID {
		otherChargesAmountFloat += float64(otherChargesAmount) + processingFeePlusGST + insurancePremiumPlusGST
	} else {
		otherChargesAmountFloat += float64(otherChargesAmount) + processingFeePlusGST + insurancePremiumPlusGST + advanceEMI
	}

	amountToBePaid = obj.Amount + totalInterestAmount

	placeholderData := map[string]interface{}{
		"ApplicationType":                      "New",
		"DisbursedDate":                        obj.DisbursedDate,
		"LoanType":                             obj.LoanType,
		"BusinessName":                         businessDetails.BusinessName,
		"UserName":                             obj.Name,
		"FathersName":                          obj.FathersName,
		"DOB":                                  dateOfBirth.Format("2 Jan 2006"),
		"Gender":                               constants.GenderNumToStr[obj.Gender],
		"MaritalStatus":                        constants.MaritalNumToStr[obj.MaritalStatus],
		"Occupation":                           occupation,
		"Nationality":                          "Indian",
		"PanNumber":                            obj.PAN,
		"ProofOfAddress":                       proofOfAddress,
		"AddressType":                          constants.ResidenceNumToStr[obj.ResidenceType],
		"CurrentAddress":                       currentAddress,
		"PermanentAddress":                     permanentAddress,
		"BusinessAddress":                      businessDetails.BusinessAddresss,
		"Phone":                                obj.Mobile,
		"Email":                                obj.Email,
		"BankName":                             bankObj.Bank,
		"AccountNumber":                        bankObj.Accountnumber,
		"IFSC":                                 bankObj.Ifsc,
		"AccountType":                          bankObj.AccountType,
		"BeneficiaryName":                      bankObj.HolderName,
		"LoanID":                               obj.LoanApplicationNo,
		"City":                                 addressMap["city"],
		"State":                                addressMap["state"],
		"LoanAmount":                           journey.AmountToString(obj.LenderID, obj.Amount),
		"OtherCharges":                         journey.AmountToString(obj.LenderID, otherChargesAmountFloat),
		"LoanAmountWords":                      loanAmountWords,
		"InterestRate":                         obj.InterestText,
		"Purpose":                              purpose,
		"KycDocsHTML":                          kycHTMLBuilder.String(),
		"FacilityType":                         facilityType,
		"Tenure":                               obj.Tenure,
		"EMITableHTML":                         emiHTMLBuilder.String(),
		"AmortScheduleHtml":                    amortScheduleHTMLBuilder.String(),
		"EMIWithPrincipalAndInterestTableHtml": emiWithPricipalAndInterestHTMLBuilder.String(),
		"Age":                                  ageValue,
		"LenderName":                           lenderObj.LenderName,
		"InstallmentRowHTML":                   iHTMLBuilder.String(),
		"ProcessingFee":                        journey.AmountToString(obj.LenderID, obj.ProcessingFee),
		"GSTAmount":                            journey.AmountToString(obj.LenderID, gstAmount),
		"ProcessingFeePlusGST":                 journey.AmountToString(obj.LenderID, processingFeePlusGST),
		"SignImagePath":                        signedURL,
		"SignDate":                             approvedDate.Format("2 Jan 2006"),
		"SignDateTime":                         approvedDate.In(loc).Format("2 Jan 2006 15:04:05"),
		"SignDateDay":                          approvedDate.In(loc).Day(),
		"SignDateMonth":                        approvedDate.In(loc).Month().String(),
		"SignDateYear":                         approvedDate.In(loc).Year(),
		"SourceEntityName":                     sourceEntityObj.LegalName,
		"EmiNos":                               EMINos,
		"EmiAmount":                            journey.AmountToString(obj.LenderID, emi),
		"EmiStartDate":                         emiDates[0].Format("2 Jan 2006"),
		"EmiEndDate":                           emiDates[len(emiDates)-1].Format("2 Jan 2006"),
		"AdvanceEmiAmount":                     journey.AmountToString(obj.LenderID, advanceEMI),
		"AdvanceEmiNos":                        advanceEMINo,
		"BounceCharge":                         fmt.Sprintf("%.2f", BounceCharge),
		"LateCharge":                           fmt.Sprintf("%.2f", LateFeeCharge),
		"LateFeeRate":                          fmt.Sprintf("%.2f perc", LateFeeRate),
		"ForeClosureCharge":                    fmt.Sprintf("%.2f", ForeclosureCharge),
		"NatureOfEntity":                       businessDetails.Constitution,
		"BusinessRelation":                     businessDetails.BusinessRelation,
		"GST":                                  fmt.Sprintf("%.0f", obj.GST),
		"DisbursalAmount":                      journey.AmountToString(obj.LenderID, disbursalAmount),
		"GSTIN":                                businessDetails.Gstin,
		"DateOfInc":                            businessDetails.DateOfIncorporation,
		"Pincode":                              addressMap["pincode"],
		"MandateNo":                            mandateNo,
		"MandateDate":                          mandateDate,
		"InsurancePremiumPlusGST":              insurancePremiumPlusGST,
		"UniqueID":                             obj.UniqueID,
		"RepaymentFrequency":                   RepaymentFrequency,
		"AmountToBePaid":                       journey.AmountToString(obj.LenderID, amountToBePaid),
		"Interest":                             obj.Interest,
		"TotalInterestAmount":                  journey.AmountToString(obj.LenderID, totalInterestAmount),
		"APR":                                  fmt.Sprintf("%.2f", apr),
		"APRPercentage":                        aprPercentage,
		"StampDuty":                            stampDuty,
		"EDIWithOutstandingPrincipalAndInterestHTMLBuilder": ediWithOutstandingPrincipalAndInterestHTMLBuilder.String(),
		"CoolOffPeriod":                    kfsOtherDisclosures.CoolOffPeriod,
		"LSPAgent":                         kfsOtherDisclosures.LSPAgent,
		"LSPAgentCustomer":                 kfsOtherDisclosures.LSPAgentCustomer,
		"NodalOfficer":                     kfsOtherDisclosures.NodalOfficer,
		"LSPLink":                          kfsOtherDisclosures.LSPLink,
		"RELink":                           kfsOtherDisclosures.RELink,
		"LockInPeriod":                     kfsOtherDisclosures.LockInPeriod,
		"EmployerName":                     employerDetails.Name,
		"EmployerPincode":                  employerDetails.Pincode,
		"EmployerCity":                     employerDetails.City,
		"EmployerState":                    employerDetails.State,
		"EmiValueWords":                    fmt.Sprintf("%s (%s)", journey.AmountToString(obj.LenderID, emi), general.AmountInWords(emi)),
		"DisbursalAmountWithoutAdvanceEMI": math.Ceil(obj.Amount - processingFeePlusGST - insurancePremiumPlusGST),
		"DocOnlinePremium":                 docOnlinePremium,
		"DocumentCharges":                  documentChargesPlusGST,
		"OutstandingPrincipal":             outstandingPrincipal,
		"isTopUp":                          isTopUp,
		"CoBorrowers":                      coBorrowers,
		"CoApplicantNames":                 CoApplicantNames,
		"lifeInsurancePremium":             lifeInsurancePremium,
		"lifeInsurancePremiumWithGST":      lifeInsurancePremiumWithGST,
		"lifeInsuranceNomineeRelation":     lifeInsuranceNomineeRelation,
		"lifeInsuranceNomineeDOB":          lifeInsuranceNomineeDOB,
		"lifeInsuranceNomineeName":         lifeInsuranceNomineeName,
		"isLifeInsuranceApplicable":        isLifeInsuranceApplicable,
		"TenureInYears":                    int(math.Ceil(float64(obj.Tenure) / 12)),
	}

	if journey.IsMuthootEDIPartner(obj.SourceEntityID) {

		businessDetails = GetBusinessUANDetails(obj.UserID)

		if businessDetails.UAN != "" {
			placeholderData["businessProofSubmitted"] = "Udyam Aadhar" // TODO: Confirm
			placeholderData["businessProofDetails"] = businessDetails.UAN
			placeholderData["typeOfBusiness"] = businessDetails.BusinessRelation
			placeholderData["constituionOfBusiness"] = businessDetails.Constitution
		}

		placeholderData["timeStamp"] = approvedDate.In(loc).Format("02/01/2006 15:04:05")

		rps, err := repayment.GetPreDisbursalRPS(obj.UserID, loanApplicationID)
		if err != nil {
			logger.WithUser(obj.UserID).Errorln(err)
			return "", ""
		}

		values := []float64{}

		values = append(values, -disbursalAmount)

		totalAmountToBePaid := obj.Amount
		totalInterestAmount := 0.0
		for _, item := range *rps {
			totalInterestAmount += item.Components.Interest
			totalAmountToBePaid += item.Components.Interest // Not using item.Amount as it is rounded off and the precision is lost

			values = append(values, item.Components.Principle+item.Components.Interest)
		}

		totalInstallments := obj.Tenure // Total number of installments, tenure already in days
		placeholderData["NumberOfInstallments"] = totalInstallments

		aprPercentage, err := calc.GetAPR(values, obj.Method)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}

		placeholderData["LoanAmount"] = fmt.Sprintf("%s/- (%s)", journey.AmountToString(obj.LenderID, obj.Amount), general.AmountInWords(obj.Amount))

		placeholderData["TotalInterestAmount"] = fmt.Sprintf("%s/- (%s)", journey.AmountToString(obj.LenderID, totalInterestAmount), general.AmountInWords(totalInterestAmount))

		placeholderData["APRPercentage"] = journey.AmountToString(obj.LenderID, aprPercentage)

		// Get daily EDI amount. Pick the first one as it is the same for all (except for last)
		if len(*rps) > 0 {
			dailyAmount := (*rps)[0].Amount
			placeholderData["DailyEDIAmount"] = fmt.Sprintf("%s/- (%s)", journey.AmountToString(obj.LenderID, dailyAmount), general.AmountInWords(dailyAmount))
		}

		placeholderData["Tenure"] = obj.Tenure / 30 // Convert days to months

		placeholderData["ProcessingFee"] = fmt.Sprintf("%s/- (%s)", journey.AmountToString(obj.LenderID, obj.ProcessingFee), general.AmountInWords(obj.ProcessingFee))
		placeholderData["DisbursalAmount"] = fmt.Sprintf("%s/- (%s)", journey.AmountToString(obj.LenderID, disbursalAmount), general.AmountInWords(disbursalAmount))

		placeholderData["AmountToBePaid"] = fmt.Sprintf("%s/- (%s)", journey.AmountToString(obj.LenderID, totalAmountToBePaid), general.AmountInWords(totalAmountToBePaid))

		var repaymentScheduleHTMLBuilder strings.Builder

		for _, repayment := range *rps {
			repaymentScheduleHTMLBuilder.WriteString("<tr>")

			// Installment Number
			repaymentScheduleHTMLBuilder.WriteString(fmt.Sprintf("<td>%d</td>", repayment.Index))

			// Opening Principal
			repaymentScheduleHTMLBuilder.WriteString(fmt.Sprintf("<td>%v</td>", journey.AmountToString(obj.LenderID, repayment.OpeningPrincipal)))

			// Installment
			repaymentScheduleHTMLBuilder.WriteString(fmt.Sprintf("<td>%v</td>", journey.AmountToString(obj.LenderID, repayment.Amount)))

			// Principal
			repaymentScheduleHTMLBuilder.WriteString(fmt.Sprintf("<td>%v</td>", journey.AmountToString(obj.LenderID, repayment.Components.Principle)))

			// Interest
			repaymentScheduleHTMLBuilder.WriteString(fmt.Sprintf("<td>%v</td>", journey.AmountToString(obj.LenderID, repayment.Components.Interest)))

			// Closing Principal
			repaymentScheduleHTMLBuilder.WriteString(fmt.Sprintf("<td>%v</td>", journey.AmountToString(obj.LenderID, repayment.ClosingPrinciple)))

			repaymentScheduleHTMLBuilder.WriteString("</tr>")
		}

		placeholderData["RepaymentScheduleHTML"] = repaymentScheduleHTMLBuilder.String()

	}

	if obj.LenderID == constants.IIFLBLID || journey.IsIIFLBLSourcing(obj.SourceEntityID) {
		loanAmount := math.Round(obj.Amount*100) / 100
		totalInterestRounded := math.Round(totalInterestAmount*100) / 100
		placeholderData["DocumentCharges"] = fmt.Sprintf("%.2f", documentChargesPlusGST)
		placeholderData["AmountToBePaid"] = journey.AmountToString(obj.LenderID, loanAmount+totalInterestRounded)
		placeholderData["daysTillFirstEMI"] = int(emiDates[0].Sub(approvedDate).Hours() / 24) //difference between EMI commencement date and aggreement generation date
		placeholderData["payableToTheRe"] = journey.AmountToString(obj.LenderID, processingFeePlusGST+advanceEMI+documentChargesPlusGST+float64(stampDuty))
		placeholderData["chargesPayable"] = journey.AmountToString(obj.LenderID, processingFeePlusGST+advanceEMI+documentChargesPlusGST+float64(stampDuty)+insurancePremiumPlusGST)
		placeholderData["DisbursalAmount"] = journey.AmountToString(obj.LenderID, loanAmount-(processingFeePlusGST+advanceEMI+documentChargesPlusGST+float64(stampDuty)+insurancePremiumPlusGST))
	}

	if journey.IsABFLBLSourcing(obj.SourceEntityID) || obj.LenderID == constants.ABFLID {
		placeholderData["IHOCharges"] = math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst))
		placeholderData["InsurancePremiumPlusGST"] = calc.CalculateAmountWithGST(premium-ihoInsurancePremium, insuranceGst)
		placeholderData["insuranceAndIHOCharges"] = math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst)) + calc.CalculateAmountWithGST(premium-ihoInsurancePremium, insuranceGst)
		placeholderData["sanctionedLoanAmount"] = general.FormatCurrency(obj.Amount, true)
		placeholderData["interestRateType"] = "Fixed"
		placeholderData["rateOfInterest"] = journey.AmountToString(obj.LenderID, obj.Interest) + "%"
		placeholderData["totalInterestAmount"] = general.FormatCurrency(totalInterestAmount+advanceEMI, true)
		placeholderData["typeOfEpi"] = "Monthly"
		placeholderData["epi"] = general.FormatCurrency(emi, true)
		placeholderData["numberOfEpIs"] = EMINos
		placeholderData["netDisbursedAmount"] = general.FormatCurrency(disbursalAmount, true)
		placeholderData["totalAmountToBePaidByTheBorrower"] = general.FormatCurrency(totalInterestAmount+obj.Amount+advanceEMI, true)
		placeholderData["dueDateOfPayment"] = emiDates[0].Format("2") + "nd of every month"
		placeholderData["payableToTheRe"] = general.FormatCurrency(processingFeePlusGST, true)
		placeholderData["payableToTheReAndCAC"] = general.FormatCurrency(processingFeePlusGST, true)
		placeholderData["payableToThirdParty"] = general.FormatCurrency(math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst))+calc.CalculateAmountWithGST(premium-ihoInsurancePremium, insuranceGst), true)
		placeholderData["FeePayable"] = general.FormatCurrency(processingFeePlusGST+insurancePremiumPlusGST, true)
		placeholderData["loanID"] = obj.LoanApplicationNo
		placeholderData["accountNo"] = obj.LoanApplicationNo
		placeholderData["loanTerm"] = "Months"
		placeholderData["tenureMonths"] = obj.Tenure
		if obj.AdditionalCharges != nil {
			for _, charge := range obj.AdditionalCharges {
				if charge.ChargeName == constants.CreditAdminChargeName {
					cacCharge := calc.CalculatePercent(obj.Amount, charge.ChargeVal)
					placeholderData["CreditAdminCharges"] = general.FormatCurrency(cacCharge, true)
					placeholderData["payableToTheReAndCAC"] = general.FormatCurrency(processingFeePlusGST+cacCharge, true)
					placeholderData["FeePayable"] = general.FormatCurrency(processingFeePlusGST+insurancePremiumPlusGST+cacCharge, true)
				}
			}
		}
	}
	if obj.LenderID == constants.ABFLPLID {
		placeholderData["IHOCharges"] = math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst))
		placeholderData["payableToThirdParty"] = general.FormatCurrency(math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst))+calc.CalculateAmountWithGST(premium-ihoInsurancePremium, insuranceGst), true)
		placeholderData["insuranceAndIHOCharges"] = math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst)) + calc.CalculateAmountWithGST(premium-ihoInsurancePremium, insuranceGst)
		placeholderData["sanctionedLoanAmount"] = general.FormatCurrency(obj.Amount, true)
		placeholderData["SignDateTime"] = ""
		placeholderData["interestRateType"] = "Fixed"
		placeholderData["rateOfInterest"] = journey.AmountToString(obj.LenderID, obj.Interest) + "%"
		placeholderData["totalInterestAmount"] = general.FormatCurrency(totalInterestAmount, true)
		placeholderData["typeOfEpi"] = "Monthly"
		placeholderData["epi"] = general.FormatCurrency(emi, true)
		placeholderData["numberOfEpIs"] = EMINos
		placeholderData["totalInterestAmount"] = general.FormatCurrency(totalInterestAmount, true)
		placeholderData["netDisbursedAmount"] = general.FormatCurrency(disbursalAmount, true)
		placeholderData["totalAmountToBePaidByTheBorrower"] = general.FormatCurrency(totalInterestAmount+obj.Amount, true)
		placeholderData["dueDateOfPayment"] = emiDates[0].Format("2") + "th of every month"
		placeholderData["payableToTheRe"] = general.FormatCurrency(processingFeePlusGST, true)
		placeholderData["loanID"] = obj.LoanApplicationNo
		placeholderData["accountNo"] = obj.LoanApplicationNo
		placeholderData["loanTerm"] = "Months"
		placeholderData["tenureMonths"] = obj.Tenure
		placeholderData["PayableThirdParty"] = math.Ceil(insurancePremiumPlusGST)
		placeholderData["FeePayable"] = general.FormatCurrency(processingFeePlusGST+math.Ceil(insurancePremiumPlusGST), true)
		placeholderData["APR"] = journey.AmountToString(obj.LenderID, apr) + "%"
		placeholderData["applicantName"] = obj.Name
		placeholderData["date"] = approvedDate.Format("2 Jan 2006")
		placeholderData["commencementOfRepayment"] = int(emiDates[0].Sub(approvedDate).Hours() / 24) //difference between EMI commencement date and aggreement generation date
	}

	if journey.IsPFLSourcing(obj.SourceEntityID) {
		agreementDetails := AgreementDetails{
			EMINos:               EMINos,
			Apr:                  apr,
			ProcessingFeePlusGST: processingFeePlusGST,
			StampDuty:            stampDuty,
			TotalInterestAmount:  totalInterestAmount,
			DisbursalAmount:      disbursalAmount,
			AdvanceEMI:           advanceEMI,
			DocOnlinePremium:     docOnlinePremium,
			EMIDates:             emiDates,
			EMI:                  emi,
		}

		GeneratePlaceholderData(obj, loanApplicationID, loc, agreementDetails, placeholderData)
	}

	// TODO: avoid using following source entity check
	// we can add variables in above map and if that variable is not available in html template then it will anyway not create any issue.
	if journey.IsMuthootCLPartner(obj.SourceEntityID) {
		userBusinessObj, err := userbusiness.Get(context.Background(), obj.UserID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}
		placeholderData["PrivacyPolicyURL"] = fmt.Sprintf("%s - %s", sourceEntityObj.SourceEntityName, sourceEntityObj.PrivacyPolicyURL)
		placeholderData["SignYearInWord"] = general.AmountInWords(float64(approvedDate.In(loc).Year()))
		placeholderData["ApplicationInitiationDate"] = approvedDate.Format("02-01-2006")
		businessProofDocName, _ := getDocumentName(obj.UserID, constants.DocCatBusinessProof)
		if err != nil {
			err := fmt.Errorf("error finding correct documentName for loanApplicationID: %s, err: %s", loanApplicationID, err)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}
		placeholderData["BusinessProof"] = strings.ReplaceAll(businessProofDocName, "_", " ")
		placeholderData["BusinessCategory"] = userBusinessObj.BusinessCategory
		placeholderData["AnchorName"] = strings.ToUpper(sourceEntityObj.LegalName)
		placeholderData["EmiAmount"] = "NA" // credit line (limit) product so no installments ...
		preloanData, err := userloandetails.GetPreloandata(context.Background(), obj.UserID)
		if err != nil {
			err := fmt.Errorf("error finding preloan data for user for loanApplicationID: %s, err: %s", loanApplicationID, err)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}
		mothersName, ok := preloanData["mothersName"]
		if ok {
			mName, ok := mothersName.(string)
			if ok {
				placeholderData["FathersName"] = mName
			}
		}
		// get placeholder values for kfs
		// TODO : Muthoot CL optimize following way of getting kfs values because this function GetKFSPlaceholderValues queries loan_application and users table
		kfsVal, err := kfs.GetKFSPlaceholderValues(obj.UserID, loanApplicationID, obj.LenderID)
		if err != nil {
			err := fmt.Errorf("error getting kfs placeHolderValues for loanApplicationID: %s, err: %s", loanApplicationID, err)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}
		for k, v := range kfsVal {
			placeholderData[k] = v
		}
	}

	if journey.IsMFLBLSourcing(obj.SourceEntityID) {

		userlocationObj, err := userlocation.Get(obj.UserID, userlocation.LocationTypeSelfieCapture)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}

		var uan string
		query := `select coalesce(uan, '') as uan from user_business_uan where user_id = $1 order by created_at desc limit 1`
		err = database.Get(&uan, query, obj.UserID)
		if err != nil {
			err := fmt.Errorf("no uan details present for loanApplicationID: %s, err: %s", loanApplicationID, err)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}

		maritalStatus := constants.MaritalNumToStr[obj.MaritalStatus]
		if maritalStatus == "" {
			maritalStatus, err = users.GetDynamicUserInfoKey(obj.UserID, "maritalStatus", "")
			if err != nil {
				maritalStatus = "Not Applicable"
			}
		}

		placeholderData["location"] = userlocationObj.Address
		placeholderData["stock"] = "Not Applicable"
		placeholderData["total"] = "Not Applicable"
		placeholderData["security"] = "Not Applicable"
		placeholderData["date"] = time.Now().In(loc).Format("02/01/2006 15:04:05")
		placeholderData["DOB"] = dateOfBirth.Format("02/01/2006")
		placeholderData["applicantName"] = obj.Name
		placeholderData["applicationName"] = obj.Name
		placeholderData["applicationNumber"] = obj.LoanApplicationNo
		placeholderData["MaritalStatus"] = maritalStatus
		placeholderData["borrowerName"] = obj.Name
		placeholderData["MobileNoforOTP"] = obj.Mobile
		placeholderData["platform"] = "Digital Journey"
		placeholderData["ipAddress"] = ""
		placeholderData["timeStamp"] = approvedDate.In(loc).Format("02/01/2006 15:04:05")
		placeholderData["loanID"] = obj.LoanApplicationNo
		placeholderData["accountNo"] = obj.LoanApplicationNo
		placeholderData["mobileNumber"] = obj.Mobile

		placeholderData["address"] = permanentAddress
		placeholderData["state"] = addressMap["state"]
		placeholderData["State"] = addressMap["state"]

		placeholderData["identityProofDetails"] = aadhaarIdentifier
		placeholderData["issueExpiryDate"] = ""
		placeholderData["issueExpiryDate"] = ""

		placeholderData["coApplicant"] = CoApplicantNames
		placeholderData["processingFees"] = journey.AmountToString(obj.LenderID, processingFeePlusGST)
		placeholderData["processingFeesPercentage"] = "2 %"
		placeholderData["businessProof"] = "Udyam Aadhar"
		placeholderData["BussinessShopName"] = businessDetails.BusinessName
		placeholderData["businessProofDetails"] = uan
		placeholderData["businessaddress"] = businessDetails.BusinessAddresss
		placeholderData["businesscity"] = businessDetails.City
		placeholderData["businessstate"] = businessDetails.State
		placeholderData["businesspincode"] = businessDetails.Pincode
		placeholderData["typeOfBusiness"] = businessDetails.BusinessRelation
		placeholderData["constitutionOfBusiness"] = businessDetails.Constitution

		placeholderData["typeOfLoan"] = "Unsecured Business Loan"
		placeholderData["loanAmount"] = journey.AmountToString(obj.LenderID, obj.Amount)
		placeholderData["sanctionedLoanAmount"] = journey.AmountToString(obj.LenderID, obj.Amount)
		placeholderData["esignNameIdentifier"] = "Name: " + obj.Name
		// placeholderData["esignNumberIdentifier"] = "Signed Using : " + obj.Mobile
		placeholderData["sanctionedLoanAmount"] = journey.AmountToString(obj.LenderID, obj.Amount)
		placeholderData["disbursalSchedule"] = "SINGLE"
		placeholderData["commencementOfRepayment"] = emiDates[0].Format("02/01/2006")
		placeholderData["dueDate"] = emiDates[0].Format("02/01/2006")
		placeholderData["commencementOfRepayments"] = emiDates[0].Format("02/01/2006")
		placeholderData["firstInstalmentDate"] = emiDates[0].Format("02/01/2006")
		placeholderData["loanTerm"] = "Months"
		placeholderData["tenureMonths"] = obj.Tenure
		placeholderData["replaymentMethod"] = "NACH"
		placeholderData["eachInstalment"] = journey.AmountToString(obj.LenderID, emi)
		placeholderData["typeOfInstalments"] = "EMI"
		placeholderData["numberOfEpIs"] = EMINos
		placeholderData["APR"] = fmt.Sprintf("%.2f", apr)
		placeholderData["ciNoOfInstalments"] = EMINos
		placeholderData["noOfInstalments"] = EMINos
		placeholderData["epi"] = journey.AmountToString(obj.LenderID, math.Ceil(emi))
		placeholderData["typeOfEpi"] = "Monthly"
		placeholderData["interestRate"] = journey.AmountToString(obj.LenderID, obj.Interest) + "%"
		placeholderData["interestRateSanctionPage"] = journey.AmountToString(obj.LenderID, obj.Interest) + "%"
		placeholderData["payableToTheRe"] = journey.AmountToString(obj.LenderID, processingFeePlusGST+obj.AdvanceEMI)
		placeholderData["totalInterestAmount"] = journey.AmountToString(obj.LenderID, totalInterestAmount)
		placeholderData["pfOneTimeRE"] = "One time"
		placeholderData["interestRateType"] = "Fixed"
		placeholderData["pfAmountRE"] = journey.AmountToString(obj.LenderID, processingFeePlusGST)
		placeholderData["annualPercentageRate"] = fmt.Sprintf("%.2f", apr) + "%"
		placeholderData["scheduleOfDisbursement"] = "Single"
		placeholderData["loanPurpose"] = obj.LoanPurpose
		placeholderData["purposeOfLoan"] = obj.LoanPurpose
		placeholderData["dueDateOfPayment"] = "5th of every month"
		placeholderData["netDisbursedAmount"] = journey.AmountToString(obj.LenderID, disbursalAmount)
		placeholderData["totalAmountToBePaidByTheBorrower"] = journey.AmountToString(obj.LenderID, totalInterestAmount+obj.Amount)
		placeholderData["mflSignHtml"] = fmt.Sprint("<img style=\"width: 100%; height: 100%;\" src=\"https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/Signature+sample.jpg\">")
		placeholderData["chargesPayable"] = journey.AmountToString(obj.LenderID, processingFeePlusGST+docOnlinePremium+float64(stampDuty))
		placeholderData["rateOfInterest"] = journey.AmountToString(obj.LenderID, obj.Interest) + " %"

		//in edi journey these keys have different values from emi journey
		placeholderData["schemeName"] = "Bandhan Plus"
		placeholderData["repaymentMethod"] = "Monthly"
		placeholderData["bounceCharges"] = "Rs. 500 +  GST per Bounce"
		placeholderData["product"] = "Bandhan Plus Loan"
		placeholderData["repaymentMandateCharges"] = `Rs 500/- + GST
		Bounce charges shall means charged for (i) dishonour of any payment instruments; or (ii) non-payment of installment (s) on their respective due dates due to dishonour of payment mandate or non-registration of the payment mandate or any other reason`
		placeholderData["loanPreClosureCharge"] = "3% + GST on overall Principal Outstanding"
		placeholderData["fieldVisitCollectionCharges"] = "Rs 50/- + GST"
		placeholderData["currentAgreement"] = "EMI"
		placeholderData["installmentTypeDaily"] = false
		placeholderData["repaymentMandateSwap"] = "Not Applicable"
		placeholderData["brokenPeriodInterest"] = journey.AmountToString(obj.LenderID, advanceEMI)
		if installmentFrequency == constants.InstallmentProgrammeDaily {
			placeholderData["payableToTheRe"] = journey.AmountToString(obj.LenderID, processingFeePlusGST)
			placeholderData["edi"] = ediAmount
			placeholderData["installmentTypeDaily"] = true
			placeholderData["typeOfInstalments"] = "EDI"
			placeholderData["epi"] = ediAmount
			placeholderData["typeOfEpi"] = "Daily"
			placeholderData["loanTerm"] = "Days"
			placeholderData["EdiAmortScheduleHTMLBuilder"] = ediAmortScheduleHTMLBuilder.String()
			placeholderData["schemeName"] = "Vyapar Mitra"
			placeholderData["repaymentMethod"] = "Daily"
			placeholderData["bounceCharges"] = "Not Applicable"
			placeholderData["product"] = "Vyapar Mitra Loan"
			placeholderData["repaymentMandateCharges"] = "NIL"
			placeholderData["repaymentMandateSwap"] = "NIL"
			placeholderData["fieldVisitCollectionCharges"] = "Rs 30/- + GST"
			placeholderData["currentAgreement"] = "EDI"
			placeholderData["typeOfInstalments"] = "EDI"
			placeholderData["numberOfEpIs"] = len(ediDates)
			placeholderData["ciNoOfInstalments"] = len(ediDates)
			placeholderData["noOfInstalments"] = len(ediDates)
			placeholderData["commencementOfRepayments"] = ediDates[0].Format("02/01/2006")
			placeholderData["commencementOfRepayment"] = ediDates[0].Format("02/01/2006")
			placeholderData["dueDateOfPayment"] = ediDates[0].Format("02/01/2006")
			placeholderData["eachInstalment"] = journey.AmountToString(obj.LenderID, ediAmount)
			placeholderData["dueDate"] = ediDates[0].Format("02/01/2006")
			placeholderData["loanPreClosureCharge"] = "NIL"
			placeholderData["AdvanceEmiAmount"] = 0
			placeholderData["processingFeesPercentage"] = "3 %"
			placeholderData["brokenPeriodInterest"] = "NA"
			placeholderData["netDisbursedAmount"] = journey.AmountToString(obj.LenderID, obj.Amount-math.Ceil(processingFeePlusGST))
		}

	}

	placeholderData, err = underwriting.AdditionalClauseFlags(loanApplicationID, placeholderData)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error(err)
		return "", ""
	}

	err = helpers.InsertOrUpdateJourneyDynamicEvaluations(context.TODO(), obj.UserID, obj.SourceEntityID, map[string]interface{}{}, map[string]interface{}{
		"agreementKeys": placeholderData,
	})
	if err != nil {
		logger.WithUser(obj.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": obj.UserID,
		}, err)
	}

	htmlBody := general.GetStringFromTemplate(string(templateString), placeholderData)
	// upload html to s3
	objectKey := obj.UserID + "/" + "unsigned_" + obj.LoanApplicationNo + ".html"
	_, flag := s3.UploadRawFileS3(strings.NewReader(htmlBody), objectKey)
	if !flag {
		err := fmt.Errorf("unable to upload unsigned agreement to s3 for loanApplicationID: %s", loanApplicationID)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	if journey.IsABFLBLSourcing(obj.SourceEntityID) {
		advanceEMIDays := calc.GetAdvanceEMIDays(obj.Amount, obj.Tenure, obj.Interest, time.Now(), obj.SourceEntityID, obj.LenderID, obj.UserID)
		query = `update lender_variables set dynamic_variables = jsonb_set(dynamic_variables::jsonb, '{"advanceEMIDays"}', to_json($1::int)::jsonb, true), updated_at = now()
							where user_id = $2 and lender_id = $3;`
		if _, err = database.Exec(query, math.Ceil(advanceEMIDays), obj.UserID, obj.LenderID); err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}
	}

	loanData := loanapplication.StructForSet{
		ID:        loanApplicationID,
		UnsignURL: objectKey,
	}
	if journey.IsABFLBLSourcing(obj.SourceEntityID) {
		loanData.AdvanceEMI = &advanceEMI
	}

	if err = loanapplication.Update(nil, loanData); err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	return objectKey, htmlBody
}

/*
GenerateUnsignedAgreementpfl generates an unsigned loan agreement for PFL.
DEPRECATED: This function is deprecated and maintained only for backward compatibility.
New code should use GenerateUnsignedAgreement instead.
*/
func GenerateUnsignedAgreementpfl(loanApplicationID string, approvedDate time.Time, forceUpdateNameInAgreement bool, name string) (string, string) {
	// fetch data from DB
	obj, err := agreementutils.GetAgreementDataValues(loanApplicationID, forceUpdateNameInAgreement, name)
	if err != nil {
		err := fmt.Errorf("unsigned agreement failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	var isABFLPreApprovedJourney bool

	documentChargesPlusGST := obj.DocumentCharges + (obj.DocumentCharges * obj.GST / 100.0)
	//update emi calculation method for some lender and source entity combinations
	obj.Method = journey.GetEMIMethodForAgreement(obj.LoanType, obj.LenderID, obj.Method)
	// now fetch source entity information
	sourceEntityObj, err := sourceentitymodel.FetchSourceEntityInfo(obj.SourceEntityID)
	if err != nil {
		err := fmt.Errorf("unsigned agreement failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}
	// now fetch lender information
	lenderObj, err := lender.Get(obj.LenderID)
	if err != nil {
		err := fmt.Errorf("unsigned agreement failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	// get pre loan data
	preLoanData := make(map[string]interface{})
	if obj.PreLoanData != "" {
		err = json.Unmarshal([]byte(obj.PreLoanData), &preLoanData)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}
	}

	//get Partner Data
	partnerData := make(map[string]interface{})
	if obj.PartnerData != "" {
		err = json.Unmarshal([]byte(obj.PartnerData), &partnerData)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln("Error in unmarshalling partner data", err)
		}
	}

	var businessDetails BusinessDetails
	if isABFLPreApprovedJourney {
		businessDetails = GetPreAppovedJourneyBusinessDetails(obj.SourceEntityID, partnerData)
	} else {
		businessDetails = GetBusinessDetails(obj.UserID, preLoanData)
	}
	// calculate required values
	dateOfBirth, err := time.Parse("2006-01-02", obj.DOB)
	if err != nil {
		err := fmt.Errorf("DOB parsing failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}
	ageValue := general.AgeAt(dateOfBirth, time.Now())

	var (
		emi        float64
		advanceEMI float64
		emiDates   []time.Time
		totalEmi   []float64

		ediTenure int
		ediAmount float64
		ediDates  []time.Time

		totalInterestAmount float64
		amountToBePaid      float64

		installmentFrequency string
	)

	installmentFrequency, err = journey.GetInstallmentFrequency(obj.UserID, obj.SourceEntityID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return "", ""
	}

	emi, advanceEMI, _ = calc.GetEMIPfl(obj.Method, obj.Amount, obj.Tenure, obj.Interest, approvedDate, obj.SourceEntityID, obj.LenderID)
	// emiDates = calc.GetEMIDates(approvedDate, obj.Tenure, obj.SourceEntityID, obj.LenderID, obj.Method)
	//
	emiDatess := []time.Time{}
	date := approvedDate.Day()
	loc, _ := time.LoadLocation("Asia/Calcutta")
	var emiStartDate time.Time

	dayOfMonth, dateThreshold := 5, 6

	for idx := 1; idx <= obj.Tenure; idx++ {
		switch obj.Method {
		case constants.MethodDailyReducing:
			if idx == 1 {
				emiStartDate = time.Date(approvedDate.Year(), approvedDate.Month(), approvedDate.Day(), 6, 30, 0, 0, approvedDate.Location()).In(loc) // in IST
			}
			emiStartDate = emiStartDate.AddDate(0, 0, 1)
		default:
			if idx == 1 {
				emiStartDate = time.Date(approvedDate.Year(), approvedDate.Month(), dayOfMonth, 6, 30, 0, 0, approvedDate.Location()).In(loc) // in IST
				if date < dateThreshold {
					emiStartDate = emiStartDate.AddDate(0, 1, 0)
				} else {
					emiStartDate = emiStartDate.AddDate(0, 2, 0)
				}
			} else {
				emiStartDate = time.Date(emiStartDate.Year(), emiStartDate.Month(), dayOfMonth, emiStartDate.Hour(), emiStartDate.Minute(), emiStartDate.Second(), emiStartDate.Nanosecond(), emiStartDate.Location())
				emiStartDate = emiStartDate.AddDate(0, 1, 0)
			}
		}

		for {
			if journey.SkipRepayment(emiStartDate.Weekday(), obj.LenderID) {
				emiStartDate = emiStartDate.AddDate(0, 0, 1)
			} else {
				break
			}
		}

		emiDatess = append(emiDatess, emiStartDate)
	}
	emiDates = emiDatess
	//

	switch installmentFrequency {
	case constants.InstallmentProgrammeDaily:
		ediAmount = math.Round(paymentutils.CalculateEDIAmount(obj.Amount, (obj.Interest / 360), obj.Tenure))
		ediDates = calc.GetEDIDates(approvedDate, obj.Tenure, obj.SourceEntityID, obj.LenderID)
		advanceEMI = 0
	}

	if obj.SourceEntityID == constants.PagarBookID {
		emiDates = []time.Time{partner.GetPBDueDate(obj.UserID, approvedDate)}
	}

	//variables for rps tables based on installment frequency
	var (
		iHTMLBuilder                          strings.Builder
		amortScheduleHTMLBuilder              strings.Builder
		emiHTMLBuilder                        strings.Builder
		emiWithPricipalAndInterestHTMLBuilder strings.Builder

		amortSchedule                                     []structs.AmortSchedule
		ediWithOutstandingPrincipalAndInterestHTMLBuilder strings.Builder

		ediAmortSchedule            []structs.EDIAmortSchedule
		ediAmortScheduleHTMLBuilder strings.Builder

		CoApplicantNames string
	)
	// loc, _ := time.LoadLocation("Asia/Calcutta")

	// calculate values required
	// _, premium, _ := insuranceutils.GetUnpaidInsurance(loanApplicationID)
	premium, _ := insuranceutils.GetUnpaidPremium(loanApplicationID)
	insuranceType, _ := insuranceutils.GetUnpaidInsuranceType(loanApplicationID)

	insuranceDetails, err := insurance.GetByLoanApplicationID(loanApplicationID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return "", ""
	}

	var (
		isLifeInsuranceApplicable    bool
		lifeInsurancePremium         float64
		lifeInsurancePremiumWithGST  float64
		lifeInsuranceNomineeRelation string
		lifeInsuranceNomineeDOB      string
		lifeInsuranceNomineeName     string
	)
	for _, insuranceDetail := range insuranceDetails {
		if insuranceDetail.InsuranceType == constants.InsuranceTypeLifeInsurance {
			lifeInsurancePremium = insuranceDetail.Premium
			// 18% premium for LifeInsurance
			lifeInsurancePremiumWithGST = calc.CalculateAmountWithGST(lifeInsurancePremium, 18)
			var lifeInsuranceNominee structs.InsuranceNominee
			err = json.Unmarshal([]byte(insuranceDetail.Nominee), &lifeInsuranceNominee)
			if err != nil && err != sql.ErrNoRows {
				logger.WithLoanApplication(loanApplicationID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return "", ""
			}

			lifeInsuranceNomineeDOB = lifeInsuranceNominee.DOB
			lifeInsuranceNomineeRelation = lifeInsuranceNominee.Relation
			lifeInsuranceNomineeName = lifeInsuranceNominee.Name
			isLifeInsuranceApplicable = true
		}
	}

	disbursalAmount := calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, advanceEMI, premium, commonutils.OtherCharges(obj.LenderID, obj.UserID), insuranceType, obj.SourceEntityID, obj.UserID)
	if journey.IsPFLSourcing(obj.SourceEntityID) {
		if isPoonawallaTopUp := journey.IsTopUpJourney(obj.UserID, obj.SourceEntityID); isPoonawallaTopUp {
			disbursalAmount = calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, 0, 0, commonutils.OtherCharges(obj.LenderID, obj.UserID), insuranceType, obj.SourceEntityID, obj.UserID)
		}
	}
	totalEmi = append(totalEmi, -disbursalAmount)

	// return math.Round((amount-processingFee-(gst*processingFee/100.0)-insurancePremium-(gst*insurancePremium/100.0)-advanceEMI-float64(otherChargesAmount))*100) / 100
	//

	emipfl, _, amountToBePaid := calc.GetEMIPfl(obj.Method, obj.Amount, obj.Tenure, obj.Interest, time.Now(), obj.SourceEntityID, obj.LenderID)
	var resp []structs.AmortSchedule
	var openingPrincipal float64
	closingPrincipal := obj.Amount

	for idx := 0; idx < obj.Tenure; idx++ {
		openingPrincipal = closingPrincipal
		var interestAmount float64
		if obj.Method == constants.MethodDailyReducing {
			interestAmount = (closingPrincipal * (obj.Interest / 365)) / 100
		} else {
			interestAmount = (closingPrincipal * (obj.Interest / 12)) / 100
		}

		if general.InArr(obj.LenderID, []string{constants.ABFLPLID, constants.MFLBLID}) {
			interestAmount = math.Ceil(interestAmount)
		} else if journey.IsRoundOffRequired(obj.SourceEntityID, obj.LenderID) {
			interestAmount = math.Round(interestAmount)
		}

		var principalAmount float64

		if idx != obj.Tenure-1 {
			principalAmount = emipfl - interestAmount
			if obj.Method != constants.MethodSimpleInterest {
				closingPrincipal -= principalAmount
			}
		} else {
			if general.InArr(obj.LenderID, []string{constants.ABFLPLID}) {
				emipfl = amountToBePaid - emipfl*float64(idx)
				principalAmount = emipfl - interestAmount
				differenceAmount := closingPrincipal - principalAmount
				emipfl += differenceAmount
				principalAmount += differenceAmount
			} else {
				// for last emipfl
				closingPrincipal = 0
				if obj.LenderID == constants.IIFLID || obj.LenderID == constants.PoonawallaFincorpID {
					principalAmount = openingPrincipal
					if obj.Method != constants.MethodSimpleInterest {
						// edge emipfl case for IIFL
						emipfl = math.Ceil(principalAmount + interestAmount)
					} else {
						// in SI, closing principal does not decrease so we calculate emipfl using amountToBePaid
						emipfl = amountToBePaid - emipfl*float64(idx)
						principalAmount = emipfl - interestAmount
					}
				} else {
					emipfl = amountToBePaid - emipfl*float64(idx)
					principalAmount = emipfl - interestAmount
				}
			}
		}
		resp = append(resp, structs.AmortSchedule{
			OpeningPrincipal: openingPrincipal,
			ClosingPrincipal: closingPrincipal,
			Index:            idx,
			InterestAmount:   interestAmount,
			PrincipalAmount:  principalAmount,
			EMI:              emi,
		})
	}
	//
	amortSchedule = resp
	amountToBePaid = obj.Amount + advanceEMI

	for index, currentDate := range emiDates {
		curValues := amortSchedule[index]
		curEMI := curValues.EMI
		if index == 0 && obj.LenderID == constants.PoonawallaFincorpID {
			curEMI += advanceEMI
		}
		totalEmi = append(totalEmi, curEMI)
		totalInterestAmount += curValues.InterestAmount
		amountToBePaid += curValues.InterestAmount
		fmt.Fprintf(&iHTMLBuilder, "<tr><td>Installment %d</td><td>&#8377; %.2f</td></tr>", index+1, curEMI)
		fmt.Fprintf(&emiHTMLBuilder, "<tr><td>%s</td><td>&#8377; %.2f</td></tr>", currentDate.Format("2 Jan 2006"), curEMI)
		if obj.LenderID == constants.PoonawallaFincorpID {
			// STODO: Change
			if index == 0 {
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> </tr>",
					index+1,
					currentDate.Format("2 Jan 2006"),
					journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal),
					journey.AmountToString(obj.LenderID, curEMI),
					journey.AmountToString(obj.LenderID, curValues.InterestAmount+advanceEMI),
					journey.AmountToString(obj.LenderID, curValues.PrincipalAmount),
					journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
			} else {
				if index%40 == 0 {
					fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr class='page-break-before'><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> </tr>",
						index+1, currentDate.Format("2 Jan 2006"),
						journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal),
						journey.AmountToString(obj.LenderID, curEMI),
						journey.AmountToString(obj.LenderID, curValues.InterestAmount),
						journey.AmountToString(obj.LenderID, curValues.PrincipalAmount), journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
				} else {
					fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> </tr>",
						index+1, currentDate.Format("2 Jan 2006"),
						journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal),
						journey.AmountToString(obj.LenderID, curEMI),
						journey.AmountToString(obj.LenderID, curValues.InterestAmount),
						journey.AmountToString(obj.LenderID, curValues.PrincipalAmount), journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
				}
			}
		} else if general.InArr(obj.LenderID, []string{constants.ABFLPLID}) {
			if index == 0 {
				// add advanceEMI and EMI for the first installment
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount+advanceEMI)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI+advanceEMI)))
			} else {
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI)))
			}
		} else if general.InArr(obj.LenderID, []string{constants.ABFLID}) {
			if index == 0 {
				// add advanceEMI and EMI for the first installment
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount+advanceEMI)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI+advanceEMI)))
			} else {
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI)))
			}
		} else if general.InArr(obj.LenderID, []string{constants.MFLBLID}) {
			fmt.Fprintf(&amortScheduleHTMLBuilder,
				"<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>",
				index+1,
				currentDate.Format("02/01/2006"),
				general.FormatCurrency(curValues.OpeningPrincipal, true),
				general.FormatCurrency(math.Ceil(curEMI), true),
				general.FormatCurrency(curValues.PrincipalAmount, true),
				general.FormatCurrency(curValues.InterestAmount, true),
			)
		} else {
			fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>", index+1, currentDate.Format("2 Jan 2006"), journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, curEMI), journey.AmountToString(obj.LenderID, curValues.InterestAmount), journey.AmountToString(obj.LenderID, curValues.PrincipalAmount), journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
		}
		fmt.Fprintf(&emiWithPricipalAndInterestHTMLBuilder, "<tr><td>%d</td><td>%s</td><td>&#8377; %.2f</td> <td>&#8377; %.2f</td> <td>&#8377; %.2f</td> </tr>", index+1, currentDate.Format("2 Jan 2006"), curEMI, curValues.PrincipalAmount, curValues.InterestAmount)
	}

	//EDI calculations and ,RPS table builder
	switch installmentFrequency {
	case constants.InstallmentProgrammeDaily:
		disbursalAmount = calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, 0, 0, map[string]int{}, "", obj.SourceEntityID, obj.UserID)
		ediAmortSchedule = calc.GetEDIAmortSchedule(obj.Amount, obj.Interest, ediAmount, obj.Tenure, obj.Method, obj.SourceEntityID, obj.LenderID)
		amountToBePaid = obj.Amount
		totalInterestAmount = 0
		for index, currentDate := range ediDates {
			if index >= len(ediAmortSchedule) {
				break
			}
			curValues := ediAmortSchedule[index]
			curEDI := curValues.EDI
			totalInterestAmount += curValues.InterestAmount
			amountToBePaid += curValues.InterestAmount
			if general.InArr(obj.LenderID, []string{constants.MFLBLID}) {
				fmt.Fprintf(&ediAmortScheduleHTMLBuilder,
					"<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>",
					index+1,
					currentDate.Format("02/01/2006"),
					general.FormatCurrency(curValues.OpeningPrincipal, true),
					general.FormatCurrency(curEDI, true),
					general.FormatCurrency(curValues.PrincipalAmount, true),
					general.FormatCurrency(curValues.InterestAmount, true),
				)
			}
		}
	}

	if obj.Method == constants.MethodDailyReducing || (obj.Method == constants.MethodReducingBalance && journey.IsMuthootEDIPartner(obj.SourceEntityID)) {

		if journey.IsMuthootEDIPartner(obj.SourceEntityID) {

			obj.Tenure = 30 * obj.Tenure
			count := 1
			lastClosing := obj.Amount
			currEDIAmount := math.Ceil(paymentutils.CalculateEDIAmount(obj.Amount, obj.Interest/360, obj.Tenure))
			openingPrincipal := obj.Amount
			lastRow := false

			for count <= obj.Tenure {

				currInterestBeforeRound := (lastClosing * obj.Interest / 365) / 100
				currInterest := math.Round(currInterestBeforeRound*100) / 100

				if currInterest < 0 {
					currInterest = 0.0
				}

				if openingPrincipal < currEDIAmount {
					lastRow = true
				}

				if lastRow {
					currEDIAmount = float64(int(openingPrincipal))
				}

				principalAmount := currEDIAmount - currInterest
				closingPrincipal := openingPrincipal - principalAmount
				lastClosing = closingPrincipal

				if lastRow {
					closingPrincipal = 0
				}

				fmt.Fprintf(&ediWithOutstandingPrincipalAndInterestHTMLBuilder, ""+
					"<tr>"+
					"<td>%d</td>"+
					"<td>&#8377; %.0f </td>"+
					"<td>&#8377; %.2f </td>"+
					"<td>&#8377; %.2f </td>"+
					"<td>&#8377; %.2f </td>"+
					"<td>&#8377; %.0f </td>"+
					"</tr>",

					count, openingPrincipal, currEDIAmount, math.Round(principalAmount*100)/100, currInterest, closingPrincipal)

				openingPrincipal = closingPrincipal
				count++

				if lastRow {
					break
				}
			}

		} else {
			ediTenure = obj.Tenure
			ediAmount = math.Ceil((emi*float64(len(emiDates)) + advanceEMI) / float64(ediTenure))
			totalInterestAmount = 0
			dailyInterestRate := obj.Interest / 365
			count := 1
			var principalAmount = obj.Amount
			previousEdiAmount := 0.0
			for ediTenure > 0 {
				interestPerDay := (principalAmount * dailyInterestRate) / 100
				totalInterestAmount += interestPerDay
				fmt.Fprintf(&ediWithOutstandingPrincipalAndInterestHTMLBuilder, "<tr><td>%d</td><td>&#8377; %.2f </td><td>&#8377; %.2f </td><td>&#8377; %.2f </td><td>&#8377; %.2f </td></tr>",
					count, principalAmount, ediAmount-interestPerDay, interestPerDay, ediAmount)
				previousEdiAmount = ediAmount - interestPerDay
				principalAmount -= previousEdiAmount
				if count == ediTenure {
					break
				}
				count++
			}
			amountToBePaid = obj.Amount + totalInterestAmount
		}
	}

	addressMap := make(map[string]any)
	var (
		line1          string
		line2          string
		city           string
		state          string
		pincode        string
		currentAddress string
	)
	// Computing current address
	if obj.CurrentAddress != "" {
		err = json.Unmarshal([]byte(obj.CurrentAddress), &addressMap)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}
		line1, _ = addressMap["line1"].(string)
		line2, _ = addressMap["line2"].(string)
		city, _ = addressMap["city"].(string)
		state, _ = addressMap["state"].(string)
		pincode, _ = addressMap["pincode"].(string)
		currentAddress = line1 + ", " + line2 + ", " + city + ", " + state + " " + pincode
	}

	permanentAddress := ""

	if journey.IsABFLBLSourcing(obj.SourceEntityID) && journey.IsMultiUserConstitutionJourney(obj.UserID, obj.SourceEntityID) {
		permanentAddress = businessDetails.CommunicationAddress
	} else if obj.PermanentAddress != "" {

		err = json.Unmarshal([]byte(obj.PermanentAddress), &addressMap)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", ""
		}
		line1, _ = addressMap["line1"].(string)
		line2, _ = addressMap["line2"].(string)
		city, _ = addressMap["city"].(string)
		state, _ = addressMap["state"].(string)
		pincode, _ = addressMap["pincode"].(string)
		permanentAddress = line1 + ", " + line2 + ", " + city + ", " + state + " " + pincode

	}

	if currentAddress == "" && permanentAddress == "" {
		err := fmt.Errorf("both CurrentAddress and PermanentAddress cannot be empty")
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	signedURL := s3.GetPresignedURLS3(obj.SignPath, 300)

	// now fetch kyc documents
	type kycDocStruct struct {
		Doctype    string
		Docname    string
		Name       string
		Identifier string
	}
	kycObjs := []kycDocStruct{}
	query := `select k.doc_type as doctype, d.document_name as docname, coalesce(k.name, '') as name,
       			coalesce(k.identifier, '') as identifier
					from loan_kyc_details k, documents d
				where k.document_id = d.document_id
					and k.loan_id = $1
					and k.status = $2
			`
	err = database.Select(&kycObjs, query, loanApplicationID, constants.KYCDocStatusUploaded)
	if err != nil {
		err := fmt.Errorf("unsigned agreement failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	aadhaarName := ""
	// calculate required values
	proofOfAddress := "AADHAAR E-KYC"
	var kycHTMLBuilder strings.Builder
	for _, kycObj := range kycObjs {
		if kycObj.Doctype == constants.DocTypeDigilockerAadhaar {
			aadhaarName = strings.ToUpper(kycObj.Name)
		}
		docType := strings.ReplaceAll(kycObj.Doctype, "_", " ")
		docName := strings.ReplaceAll(kycObj.Docname, "_", " ")
		if docType == "Address Proof" {
			proofOfAddress = docName
		}
		fmt.Fprintf(&kycHTMLBuilder, "<tr><td>%s</td><td>%s</td></tr>", docType, docName)
	}

	if obj.LenderID == constants.PoonawallaFincorpID && aadhaarName != "" {
		obj.Name = aadhaarName
	}

	// now fetch bank details
	type bankStruct struct {
		Accountnumber string
		Ifsc          string
		Bank          string
		HolderName    string
		AccountType   string
	}
	bankObj := bankStruct{}
	query = `select ubd.account_number as accountnumber, ubd.ifsc_code as ifsc,
					ubd.bank_name as bank,
					coalesce(ubd.name, '') as holdername,
					coalesce(ubd.account_type, '') as accounttype
					from user_bank_details ubd
					where user_bank_details_id in
						(select user_bank_details_id from loan_application
							where loan_application_id = $1 )
					and ubd.status = $2;`
	err = database.Get(&bankObj, query, loanApplicationID, constants.UserBankStatusApproved)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	if bankObj.AccountType == "" {
		bankObj.AccountType = "savings"
	}
	unsignedAgreementTemplatePath := obj.UnsignedAgreementTemplate

	var coBorrowers []CoBorrower

	// Names of all the co-applicants
	var coApplicantsList []string
	for _, coBorrower := range coBorrowers {
		coApplicantsList = append(coApplicantsList, coBorrower.UserName)

	}
	CoApplicantNames = strings.Join(coApplicantsList, ", ")

	occupation := getOccupation(obj.SourceEntityID)
	if general.InArr(obj.SourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) || journey.IIFLAgg(obj.SourceEntityID, false).IsAgg {
		occupation, _ = preLoanData["occupationType"].(string)

		if journey.IsTemporalFlow(obj.UserID, obj.SourceEntityID, usermodulemapping.PersonalInfo) {
			occupation, _ = preLoanData["employmentStatus"].(string)
			loanType, _, _ := journey.GetLoanType(obj.SourceEntityID)
			if loanType == constants.LoanTypePersonalLoan {
				occupation = constants.OccupationTypeMapForAgreement[occupation]
			} else if loanType == constants.LoanTypeBusinessLoan {
				occupation = "Business"
			}
		}
	}
	facilityType, purpose := getFacilityTypePurpose(obj.LoanType, obj.LoanPurpose, obj.SourceEntityID, obj.LenderID)

	advanceEMINo := 0
	if advanceEMI > 0 {
		advanceEMINo = 1
	}

	mandateNo, mandateDate := getMandateInfo(loanApplicationID, obj.SourceEntityID)

	loanAmountWords := general.AmountInWords(obj.Amount)

	// GST on insurance premium
	insuranceGst := obj.GST
	if insuranceType == constants.InsuranceTypeHealthWellness {
		insuranceGst = 0
	}
	insurancePremiumPlusGST := calc.CalculateAmountWithGST(premium, insuranceGst)
	// rounding up in case of ABFL
	if journey.IsABFLBLSourcing(obj.SourceEntityID) {
		insurancePremiumPlusGST = math.Ceil(insurancePremiumPlusGST)
	}

	// GST on processing fee
	gstAmount := obj.ProcessingFee * obj.GST / 100
	processingFeePlusGST := obj.ProcessingFee + gstAmount
	processingFeePlusGST = math.Round(processingFeePlusGST*100) / 100
	if obj.LenderID != constants.IIFLID && journey.IsRoundOffRequired(obj.SourceEntityID, obj.LenderID) {
		processingFeePlusGST = math.Round(processingFeePlusGST)
	}

	buf := new(bytes.Buffer)
	ioRPointer := s3.GetFileStream(unsignedAgreementTemplatePath)
	if ioRPointer == nil {
		err := fmt.Errorf("file not found in s3 for loanApplicationID: %s and path: %s", loanApplicationID, unsignedAgreementTemplatePath)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}
	ioR := *ioRPointer
	buf.ReadFrom(ioR)
	templateString := buf.String()
	var RepaymentFrequency string
	var EMINos int
	switch obj.Method {
	case constants.MethodDailyReducing:
		RepaymentFrequency = "Daily"
		EMINos = ediTenure
		emi = ediAmount
	default:
		RepaymentFrequency = "Monthly"
		EMINos = len(emiDates)
	}
	kfsOtherDisclosures := journey.GetKFSOtherDisclosures(obj.LenderID, obj.SourceEntityID, EMINos, obj.LoanType)
	apr, err := calc.GetAPR(totalEmi, obj.Method)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}

	otherCharges := commonutils.OtherCharges(obj.LenderID, obj.UserID)
	stampDuty := 0
	if _, ok := otherCharges["stampDuty"]; ok {
		stampDuty = otherCharges["stampDuty"]
	}
	otherChargesAmount := 0
	for _, charge := range otherCharges {
		otherChargesAmount += charge
	}

	employerDetails, _ := users.GetEmployerDetails(obj.UserID)
	LateFeeRate, LateFeeCharge, BounceCharge, ForeclosureCharge := calc.GetContingentCharges(obj.LenderID)
	aprPercentage := ""
	if journey.IsOneMuthootPartner(obj.SourceEntityID) {
		aprPercentage = fmt.Sprintf("%.2f", obj.Interest+2.5)
	}
	docOnlinePremium, _ := insuranceutils.GetUnpaidHealthInsurance(loanApplicationID)

	var otherChargesAmountFloat float64
	var outstandingPrincipal float64
	isTopUp, _, _, _ := journey.IsTopUp(obj.SourceEntityID)
	if isTopUp {
		outstandingPrincipal, _, err = underwriting.GetForeclosureDetails(obj.UserID)
		if err != nil {
			logger.WithUser(obj.UserID).Errorln(err)
			return "", ""
		}

		amountToBePaid += outstandingPrincipal
		disbursalAmount -= outstandingPrincipal
		otherChargesAmountFloat += outstandingPrincipal
	}

	if general.InArr(obj.LenderID, []string{constants.ABFLPLID, constants.PoonawallaFincorpID}) {
		totalInterestAmount += advanceEMI
	}
	if general.InArr(obj.LenderID, []string{constants.ABFLID, constants.ABFLPLID}) || obj.LenderID == constants.PoonawallaFincorpID {
		otherChargesAmountFloat += float64(otherChargesAmount) + processingFeePlusGST + insurancePremiumPlusGST
	} else {
		otherChargesAmountFloat += float64(otherChargesAmount) + processingFeePlusGST + insurancePremiumPlusGST + advanceEMI
	}

	if obj.LenderID == constants.PoonawallaFincorpID && journey.IsTopUpJourney(obj.UserID, obj.SourceEntityID) {
		// For Poonawalla top-up, set amountToBePaid to loan amount + total interest
		amountToBePaid = obj.Amount + totalInterestAmount
	} else {
		amountToBePaid += processingFeePlusGST + insurancePremiumPlusGST + float64(otherChargesAmount)
	}

	placeholderData := map[string]interface{}{
		"ApplicationType":                      "New",
		"DisbursedDate":                        obj.DisbursedDate,
		"LoanType":                             obj.LoanType,
		"BusinessName":                         businessDetails.BusinessName,
		"UserName":                             obj.Name,
		"FathersName":                          obj.FathersName,
		"DOB":                                  dateOfBirth.Format("2 Jan 2006"),
		"Gender":                               constants.GenderNumToStr[obj.Gender],
		"MaritalStatus":                        constants.MaritalNumToStr[obj.MaritalStatus],
		"Occupation":                           occupation,
		"Nationality":                          "Indian",
		"PanNumber":                            obj.PAN,
		"ProofOfAddress":                       proofOfAddress,
		"AddressType":                          constants.ResidenceNumToStr[obj.ResidenceType],
		"CurrentAddress":                       currentAddress,
		"PermanentAddress":                     permanentAddress,
		"BusinessAddress":                      businessDetails.BusinessAddresss,
		"Phone":                                obj.Mobile,
		"Email":                                obj.Email,
		"BankName":                             bankObj.Bank,
		"AccountNumber":                        bankObj.Accountnumber,
		"IFSC":                                 bankObj.Ifsc,
		"AccountType":                          bankObj.AccountType,
		"BeneficiaryName":                      bankObj.HolderName,
		"LoanID":                               obj.LoanApplicationNo,
		"City":                                 addressMap["city"],
		"State":                                addressMap["state"],
		"LoanAmount":                           journey.AmountToString(obj.LenderID, obj.Amount),
		"OtherCharges":                         journey.AmountToString(obj.LenderID, otherChargesAmountFloat),
		"LoanAmountWords":                      loanAmountWords,
		"InterestRate":                         obj.InterestText,
		"Purpose":                              purpose,
		"KycDocsHTML":                          kycHTMLBuilder.String(),
		"FacilityType":                         facilityType,
		"Tenure":                               obj.Tenure,
		"EMITableHTML":                         emiHTMLBuilder.String(),
		"AmortScheduleHtml":                    amortScheduleHTMLBuilder.String(),
		"EMIWithPrincipalAndInterestTableHtml": emiWithPricipalAndInterestHTMLBuilder.String(),
		"Age":                                  ageValue,
		"LenderName":                           lenderObj.LenderName,
		"InstallmentRowHTML":                   iHTMLBuilder.String(),
		"ProcessingFee":                        journey.AmountToString(obj.LenderID, obj.ProcessingFee),
		"GSTAmount":                            journey.AmountToString(obj.LenderID, gstAmount),
		"ProcessingFeePlusGST":                 journey.AmountToString(obj.LenderID, processingFeePlusGST),
		"SignImagePath":                        signedURL,
		"SignDate":                             approvedDate.Format("2 Jan 2006"),
		"SignDateTime":                         approvedDate.In(loc).Format("2 Jan 2006 15:04:05"),
		"SignDateDay":                          approvedDate.In(loc).Day(),
		"SignDateMonth":                        approvedDate.In(loc).Month().String(),
		"SignDateYear":                         approvedDate.In(loc).Year(),
		"SourceEntityName":                     sourceEntityObj.LegalName,
		"EmiNos":                               EMINos,
		"EmiAmount":                            journey.AmountToString(obj.LenderID, emi),
		"EmiStartDate":                         emiDates[0].Format("2 Jan 2006"),
		"EmiEndDate":                           emiDates[len(emiDates)-1].Format("2 Jan 2006"),
		"AdvanceEmiAmount":                     journey.AmountToString(obj.LenderID, advanceEMI),
		"AdvanceEmiNos":                        advanceEMINo,
		"BounceCharge":                         fmt.Sprintf("%.2f", BounceCharge),
		"LateCharge":                           fmt.Sprintf("%.2f", LateFeeCharge),
		"LateFeeRate":                          fmt.Sprintf("%.2f perc", LateFeeRate),
		"ForeClosureCharge":                    fmt.Sprintf("%.2f", ForeclosureCharge),
		"NatureOfEntity":                       businessDetails.Constitution,
		"BusinessRelation":                     businessDetails.BusinessRelation,
		"GST":                                  fmt.Sprintf("%.0f", obj.GST),
		"DisbursalAmount":                      journey.AmountToString(obj.LenderID, disbursalAmount),
		"GSTIN":                                businessDetails.Gstin,
		"DateOfInc":                            businessDetails.DateOfIncorporation,
		"Pincode":                              addressMap["pincode"],
		"MandateNo":                            mandateNo,
		"MandateDate":                          mandateDate,
		"InsurancePremiumPlusGST":              insurancePremiumPlusGST,
		"UniqueID":                             obj.UniqueID,
		"RepaymentFrequency":                   RepaymentFrequency,
		"AmountToBePaid":                       journey.AmountToString(obj.LenderID, amountToBePaid),
		"Interest":                             obj.Interest,
		"TotalInterestAmount":                  journey.AmountToString(obj.LenderID, totalInterestAmount),
		"APR":                                  fmt.Sprintf("%.2f", apr),
		"APRPercentage":                        aprPercentage,
		"StampDuty":                            stampDuty,
		"EDIWithOutstandingPrincipalAndInterestHTMLBuilder": ediWithOutstandingPrincipalAndInterestHTMLBuilder.String(),
		"CoolOffPeriod":                    kfsOtherDisclosures.CoolOffPeriod,
		"LSPAgent":                         kfsOtherDisclosures.LSPAgent,
		"LSPAgentCustomer":                 kfsOtherDisclosures.LSPAgentCustomer,
		"NodalOfficer":                     kfsOtherDisclosures.NodalOfficer,
		"LSPLink":                          kfsOtherDisclosures.LSPLink,
		"RELink":                           kfsOtherDisclosures.RELink,
		"LockInPeriod":                     kfsOtherDisclosures.LockInPeriod,
		"EmployerName":                     employerDetails.Name,
		"EmployerPincode":                  employerDetails.Pincode,
		"EmployerCity":                     employerDetails.City,
		"EmployerState":                    employerDetails.State,
		"EmiValueWords":                    fmt.Sprintf("%s (%s)", journey.AmountToString(obj.LenderID, emi), general.AmountInWords(emi)),
		"DisbursalAmountWithoutAdvanceEMI": math.Ceil(obj.Amount - processingFeePlusGST - insurancePremiumPlusGST),
		"DocOnlinePremium":                 docOnlinePremium,
		"DocumentCharges":                  documentChargesPlusGST,
		"OutstandingPrincipal":             outstandingPrincipal,
		"isTopUp":                          isTopUp,
		"CoBorrowers":                      coBorrowers,
		"CoApplicantNames":                 CoApplicantNames,
		"lifeInsurancePremium":             lifeInsurancePremium,
		"lifeInsurancePremiumWithGST":      lifeInsurancePremiumWithGST,
		"lifeInsuranceNomineeRelation":     lifeInsuranceNomineeRelation,
		"lifeInsuranceNomineeDOB":          lifeInsuranceNomineeDOB,
		"lifeInsuranceNomineeName":         lifeInsuranceNomineeName,
		"isLifeInsuranceApplicable":        isLifeInsuranceApplicable,
		"TenureInYears":                    int(math.Ceil(float64(obj.Tenure) / 12)),
	}
	if obj.LenderID == constants.PoonawallaFincorpID {
		agreementDetails := AgreementDetails{
			EMINos:               EMINos,
			Apr:                  apr,
			ProcessingFeePlusGST: processingFeePlusGST,
			StampDuty:            stampDuty,
			TotalInterestAmount:  totalInterestAmount,
			DisbursalAmount:      disbursalAmount,
			AdvanceEMI:           advanceEMI,
			DocOnlinePremium:     docOnlinePremium,
			EMIDates:             emiDates,
			EMI:                  emi,
		}

		GeneratePlaceholderDataPfl(obj, loanApplicationID, loc, agreementDetails, placeholderData)
	}

	placeholderData, err = underwriting.AdditionalClauseFlags(loanApplicationID, placeholderData)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error(err)
		return "", ""
	}

	err = helpers.InsertOrUpdateJourneyDynamicEvaluations(context.TODO(), obj.UserID, obj.SourceEntityID, map[string]interface{}{}, map[string]interface{}{
		"agreementKeys": placeholderData,
	})
	if err != nil {
		logger.WithUser(obj.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": obj.UserID,
		}, err)
	}

	htmlBody := general.GetStringFromTemplate(string(templateString), placeholderData)
	// upload html to s3
	objectKey := obj.UserID + "/" + "unsigned_" + obj.LoanApplicationNo + ".html"
	_, flag := s3.UploadRawFileS3(strings.NewReader(htmlBody), objectKey)
	if !flag {
		err := fmt.Errorf("unable to upload unsigned agreement to s3 for loanApplicationID: %s", loanApplicationID)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	loanData := loanapplication.StructForSet{
		ID:        loanApplicationID,
		UnsignURL: objectKey,
	}
	if journey.IsABFLBLSourcing(obj.SourceEntityID) {
		loanData.AdvanceEMI = &advanceEMI
	}

	if err = loanapplication.Update(nil, loanData); err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", ""
	}

	return objectKey, htmlBody
}

/*
GenerateAndEmailSignedAgreement generates an signed loan agreement
and authorization letter for given loan application in form of HTML,
put it to S3, then convert it to PDF and sends an email
to user, sourcing entity and lender with required attachments
It returns the object key for signed agreement
*/
func GenerateAndEmailSignedAgreement(loanApplicationID string, signDate time.Time, boostID string, forceUpdateNameInAgreement bool, name string) string {
	defer errorHandler.RecoveryNoResponse()
	// fetch data from DB
	obj, err := agreementutils.GetAgreementDataValues(loanApplicationID, forceUpdateNameInAgreement, name)
	if err != nil {
		err := fmt.Errorf("signed agreement failed for loanApplicationID: %s, err: %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}
	documentChargesPlusGST := obj.DocumentCharges + (obj.DocumentCharges * obj.GST / 100.0)
	//update emi calculation method for some lender and source entity combinations
	obj.Method = journey.GetEMIMethodForAgreement(obj.LoanType, obj.LenderID, obj.Method)
	// get pre loan data
	preLoanData := make(map[string]interface{})
	if obj.PreLoanData != "" {
		err = json.Unmarshal([]byte(obj.PreLoanData), &preLoanData)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}
	}
	businessDetails := GetBusinessDetails(obj.UserID, preLoanData)
	// calculate required values
	dateOfBirth, err := time.Parse("2006-01-02", obj.DOB)
	if err != nil {
		err := fmt.Errorf("DOB parsing failed for loanApplicationID: %s, err: %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}
	ageValue := general.AgeAt(dateOfBirth, time.Now())

	//variables for rps tables based on installment frequency
	var (
		iHTMLBuilder                          strings.Builder
		amortScheduleHTMLBuilder              strings.Builder
		emiHTMLBuilder                        strings.Builder
		emiWithPricipalAndInterestHTMLBuilder strings.Builder

		amortSchedule                                     []structs.AmortSchedule
		ediWithOutstandingPrincipalAndInterestHTMLBuilder strings.Builder

		ediAmortSchedule            []structs.EDIAmortSchedule
		ediAmortScheduleHTMLBuilder strings.Builder
	)

	var (
		emi        float64
		advanceEMI float64
		emiDates   []time.Time
		totalEmi   []float64

		ediTenure int
		ediAmount float64
		ediDates  []time.Time

		totalInterestAmount float64
		amountToBePaid      float64

		installmentFrequency string
	)

	installmentFrequency, err = journey.GetInstallmentFrequency(obj.UserID, obj.SourceEntityID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ""
	}

	emi, advanceEMI, _ = calc.GetEMI(obj.Method, obj.Amount, obj.Tenure, obj.Interest, signDate, obj.SourceEntityID, obj.LenderID, obj.UserID)
	emiDates = calc.GetEMIDates(signDate, obj.Tenure, obj.SourceEntityID, obj.LenderID, obj.Method, obj.UserID, false)

	switch installmentFrequency {
	case constants.InstallmentProgrammeDaily:
		advanceEMI = 0
		ediAmount = math.Ceil(paymentutils.CalculateEDIAmount(obj.Amount, (obj.Interest / 365), obj.Tenure))
		ediDates = calc.GetEDIDates(signDate, obj.Tenure, obj.SourceEntityID, obj.LenderID)
	}

	if obj.SourceEntityID == constants.PagarBookID {
		emiDates = []time.Time{partner.GetPBDueDate(obj.UserID, signDate)}
	}

	loc, _ := time.LoadLocation("Asia/Calcutta")

	// calculate values required
	// _, premium, _ := insuranceutils.GetUnpaidInsurance(loanApplicationID)
	premium, _ := insuranceutils.GetUnpaidPremium(loanApplicationID)
	insuranceType, _ := insuranceutils.GetUnpaidInsuranceType(loanApplicationID)
	ihoInsurancePremium, _ := insuranceutils.InsurancePremiumByInsuranceType(loanApplicationID, constants.IHO)

	insuranceGst := obj.GST
	if insuranceType == constants.InsuranceTypeHealthWellness {
		insuranceGst = 0
	}

	disbursalAmount := calc.CalculateDisbursalAmount(obj.Amount, obj.ProcessingFee, obj.GST, advanceEMI, premium, commonutils.OtherCharges(obj.LenderID, obj.UserID), insuranceType)
	totalEmi = append(totalEmi, -disbursalAmount)

	if obj.LenderID == constants.ABFLPLID {
		disbursalAmount = calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, advanceEMI, premium, commonutils.OtherCharges(obj.LenderID, obj.UserID), insuranceType, obj.SourceEntityID, obj.UserID)
	}

	amortSchedule = calc.GetAmortSchedule(obj.Amount, obj.Interest, obj.Tenure, obj.Method, obj.SourceEntityID, obj.LenderID, obj.UserID)
	amountToBePaid = obj.Amount

	//TODO: make factory for amortScheduleHTMLBuilder
	for index, currentDate := range emiDates {
		curValues := amortSchedule[index]
		curEMI := curValues.EMI
		totalEmi = append(totalEmi, curEMI)
		totalInterestAmount += curValues.InterestAmount
		amountToBePaid += curValues.InterestAmount
		fmt.Fprintf(&iHTMLBuilder, "<tr><td>Installment %d</td><td>&#8377; %.2f</td></tr>", index+1, curEMI)
		fmt.Fprintf(&emiHTMLBuilder, "<tr><td>%s</td><td>&#8377; %.2f</td></tr>", currentDate.Format("2 Jan 2006"), curEMI)
		if obj.LenderID == constants.PoonawallaFincorpID {
			fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, currentDate.Format("2 Jan 2006"), journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, curEMI), journey.AmountToString(obj.LenderID, curValues.InterestAmount), journey.AmountToString(obj.LenderID, curValues.PrincipalAmount), journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
		} else if general.InArr(obj.LenderID, []string{constants.ABFLPLID}) {
			if index == 0 {
				// add advanceEMI and EMI for the first installment
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount+advanceEMI)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI+advanceEMI)))
			} else {
				fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI)))
			}
		} else if journey.IsABFLBLSourcing(obj.SourceEntityID) {
			fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td> %s</td> <td> %s</td> <td> %s</td> </tr>", index+1, journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal), journey.AmountToString(obj.LenderID, math.Floor(curValues.PrincipalAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curValues.InterestAmount)), journey.AmountToString(obj.LenderID, math.Ceil(curEMI)))
		} else if general.InArr(obj.LenderID, []string{constants.MFLBLID}) {
			fmt.Fprintf(&amortScheduleHTMLBuilder,
				"<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>",
				index+1,
				currentDate.Format("02/01/2006"),
				general.FormatCurrency(curValues.OpeningPrincipal, true),
				general.FormatCurrency(curEMI, true),
				general.FormatCurrency(curValues.PrincipalAmount, true),
				general.FormatCurrency(curValues.InterestAmount, true),
			)
		} else {
			fmt.Fprintf(&amortScheduleHTMLBuilder, "<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>", index+1, currentDate.Format("2 Jan 2006"), journey.AmountToString(obj.LenderID, curValues.OpeningPrincipal), journey.AmountToString(obj.LenderID, curEMI), journey.AmountToString(obj.LenderID, curValues.InterestAmount), journey.AmountToString(obj.LenderID, curValues.PrincipalAmount), journey.AmountToString(obj.LenderID, curValues.ClosingPrincipal))
		}
		fmt.Fprintf(&emiWithPricipalAndInterestHTMLBuilder, "<tr><td>%d</td><td>%s</td><td>&#8377; %.2f</td> <td>&#8377; %.2f</td> <td>&#8377; %.2f</td> </tr>", index+1, currentDate.Format("2 Jan 2006"), curEMI, curValues.PrincipalAmount, curValues.InterestAmount)
	}

	//EMI calculations and ,RPS table builder
	switch installmentFrequency {
	case constants.InstallmentProgrammeDaily:
		disbursalAmount = calc.CalculateDisbursalAmountBySourceEntity(obj.Amount, obj.ProcessingFee, obj.GST, 0, 0, map[string]int{}, "", obj.SourceEntityID, obj.UserID)
		ediAmortSchedule = calc.GetEDIAmortSchedule(obj.Amount, obj.Interest, ediAmount, obj.Tenure, obj.Method, obj.SourceEntityID, obj.LenderID)
		amountToBePaid = obj.Amount
		totalInterestAmount = 0
		for index, currentDate := range ediDates {
			if index >= len(ediAmortSchedule) {
				break
			}
			curValues := ediAmortSchedule[index]
			curEDI := curValues.EDI
			totalInterestAmount += curValues.InterestAmount
			amountToBePaid += curValues.InterestAmount
			if general.InArr(obj.LenderID, []string{constants.MFLBLID}) {
				fmt.Fprintf(&ediAmortScheduleHTMLBuilder,
					"<tr><td>%d</td><td>%s</td><td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> <td>&#8377; %s</td> </tr>",
					index+1,
					currentDate.Format("02/01/2006"),
					general.FormatCurrency(curValues.OpeningPrincipal, true),
					general.FormatCurrency(curEDI, true),
					general.FormatCurrency(curValues.PrincipalAmount, true),
					general.FormatCurrency(curValues.InterestAmount, true),
				)
			}
		}
	}

	if obj.Method == constants.MethodDailyReducing {
		ediTenure = obj.Tenure
		ediAmount = math.Ceil((emi*float64(len(emiDates)) + advanceEMI) / float64(ediTenure))
		totalInterestAmount = 0
		dailyInterestRate := obj.Interest / 365
		count := 1
		var principalAmount = obj.Amount
		previousEdiAmount := 0.0
		for ediTenure > 0 {
			interestPerDay := (principalAmount * dailyInterestRate) / 100
			totalInterestAmount += interestPerDay
			fmt.Fprintf(&ediWithOutstandingPrincipalAndInterestHTMLBuilder, "<tr><td>%d</td><td>&#8377; %.2f </td><td>&#8377; %.2f </td><td>&#8377; %.2f </td><td>&#8377; %.2f </td></tr>",
				count, principalAmount, ediAmount-interestPerDay, interestPerDay, ediAmount)
			previousEdiAmount = ediAmount - interestPerDay
			principalAmount -= previousEdiAmount
			if count == ediTenure {
				break
			}
			count++
		}
		amountToBePaid = obj.Amount + totalInterestAmount
	}

	//supermoney address is not being saved as line1,line2, so not unmarshalling
	addressMap := make(map[string]string)
	var currentAddress, permanentAddress string

	if journey.IsSuperMoneyJourney(obj.UserID, obj.SourceEntityID) {
		currentAddress = obj.CurrentAddress
		permanentAddress = obj.PermanentAddress
	} else {

		if obj.CurrentAddress != "" {
			err = json.Unmarshal([]byte(obj.CurrentAddress), &addressMap)
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Errorln(err)
				return ""
			}
			currentAddress = addressMap["line1"] + ", " + addressMap["line2"] + ", " + addressMap["city"] + ", " + addressMap["state"] + " " + addressMap["pincode"]
		}

		if obj.PermanentAddress != "" {
			err = json.Unmarshal([]byte(obj.PermanentAddress), &addressMap)
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Errorln(err)
				return ""
			}
			permanentAddress = addressMap["line1"] + ", " + addressMap["line2"] + ", " + addressMap["city"] + ", " + addressMap["state"] + " " + addressMap["pincode"]
		}
	}

	if currentAddress == "" && permanentAddress == "" {
		err := fmt.Errorf("both CurrentAddress and PermanentAddress cannot be empty")
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}

	signedURL := s3.GetPresignedURLS3(obj.SignPath, 300)

	// now fetch kyc documents
	type kycDocStruct struct {
		Doctype    string
		Docname    string
		Name       string
		Identifier string
	}
	kycObjs := []kycDocStruct{}
	query := `select k.doc_type as doctype, d.document_name as docname, coalesce(k.name, '') as name,
       				coalesce(k.identifier, '') as identifier
					from loan_kyc_details k, documents d
				where k.document_id = d.document_id
					and k.loan_id = $1
					and k.status = $2
			`
	err = database.Select(&kycObjs, query, loanApplicationID, constants.KYCDocStatusUploaded)
	if err != nil {
		err := fmt.Errorf("signed agreement failed for loanApplicationID: %s, err: %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	// calculate required values
	aadhaarIdentifier := ""
	//aadhaarName := ""
	proofOfAddress := "AADHAAR E-KYC"
	var kycHTMLBuilder strings.Builder
	for _, kycObj := range kycObjs {
		docType := strings.ReplaceAll(kycObj.Doctype, "_", " ")
		docName := strings.ReplaceAll(kycObj.Docname, "_", " ")
		if docType == "Address Proof" {
			proofOfAddress = docName
		} else if kycObj.Doctype == constants.DocumentNameAadhaarPhoto {
			aadhaarIdentifier = kycObj.Identifier
		}

		fmt.Fprintf(&kycHTMLBuilder, "<tr><td>%s</td><td>%s</td></tr>", docType, docName)
	}

	// now fetch bank details
	type bankStruct struct {
		Accountnumber string
		Ifsc          string
		Bank          string
		HolderName    string
		AccountType   string
	}
	bankObj := bankStruct{}
	query = `select ubd.account_number as accountnumber, ubd.ifsc_code as ifsc,
					ubd.bank_name as bank,
					coalesce(ubd.name, '') as holdername,
					coalesce(ubd.account_type, '') as accounttype
					from user_bank_details ubd
					where user_bank_details_id in
						(select user_bank_details_id from loan_application
							where loan_application_id = $1 )
					and ubd.status = $2;`
	err = database.Get(&bankObj, query, loanApplicationID, constants.UserBankStatusApproved)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	if bankObj.AccountType == "" {
		bankObj.AccountType = "savings"
	}

	// now fetch lender information
	lenderObj, err := lender.Get(obj.LenderID)
	if err != nil {
		err := fmt.Errorf("signed agreement failed for loanApplicationID: %s, err: %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}

	// now fetch source entity information
	sourceEntityObj, err := sourceentitymodel.FetchSourceEntityInfo(obj.SourceEntityID)
	if err != nil {
		err := fmt.Errorf("signed agreement failed for loanApplicationID: %s, error: %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}

	occupation := getOccupation(obj.SourceEntityID)
	if general.InArr(obj.SourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) || journey.IIFLAgg(obj.SourceEntityID, false).IsAgg {
		occupation, _ = preLoanData["occupationType"].(string)
		if journey.IsTemporalFlow(obj.UserID, obj.SourceEntityID, usermodulemapping.PersonalInfo) {
			occupation, _ = preLoanData["employmentStatus"].(string)
			loanType, _, _ := journey.GetLoanType(obj.SourceEntityID)
			if loanType == constants.LoanTypePersonalLoan {
				occupation = constants.OccupationTypeMapForAgreement[occupation]
			} else if loanType == constants.LoanTypeBusinessLoan {
				occupation = "Business"
			}
		}
	}

	facilityType, purpose := getFacilityTypePurpose(obj.LoanType, obj.LoanPurpose, obj.SourceEntityID, obj.LenderID)

	advanceEMINo := 0
	if advanceEMI > 0 {
		advanceEMINo = 1
	}

	loanAmountWords := general.AmountInWords(obj.Amount)

	// GST on processing fee
	gstAmount := obj.ProcessingFee * obj.GST / 100
	processingFeePlusGST := obj.ProcessingFee + gstAmount
	processingFeePlusGST = math.Round(processingFeePlusGST*100) / 100
	if obj.LenderID != constants.IIFLID && journey.IsRoundOffRequired(obj.SourceEntityID, obj.LenderID) {
		processingFeePlusGST = math.Round(processingFeePlusGST)
	}

	mandateNo, mandateDate := getMandateInfo(loanApplicationID, obj.SourceEntityID)

	latLongIP := ""
	if obj.Lat != "" && obj.Lon != "" {
		latLongIP = fmt.Sprintf("Lat / Long: %s / %s  ", obj.Lat, obj.Lon)
	} else {
		latLongIP = fmt.Sprintf("IP: %s", obj.IPAddress)
	}
	// GST on insurance premium
	insurancePremiumPlusGST := calc.CalculateAmountWithGST(premium, obj.GST)
	// get template string and replace placeholders to get final html
	buf := new(bytes.Buffer)
	iorPointer := s3.GetFileStream(obj.SignedAgreementTemplate)
	if iorPointer == nil {
		err := fmt.Errorf("template file not found in s3 for loanApplicationID: %s, template: %s", loanApplicationID, obj.SignedAgreementTemplate)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}

	ioR := *iorPointer
	buf.ReadFrom(ioR)
	templateString := buf.String()

	var RepaymentFrequency string
	var EMINos int
	switch obj.Method {
	case constants.MethodDailyReducing:
		RepaymentFrequency = "Daily"
		EMINos = ediTenure
		emi = ediAmount
	default:
		RepaymentFrequency = "Monthly"
		EMINos = len(emiDates)
	}
	kfsOtherDisclosures := journey.GetKFSOtherDisclosures(obj.LenderID, obj.SourceEntityID, EMINos, obj.LoanType)

	apr, err := calc.GetAPR(totalEmi, obj.Method)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}

	stampDuty := 0
	otherCharges := commonutils.OtherCharges(obj.LenderID, obj.UserID)
	if _, ok := otherCharges["stampDuty"]; ok {
		stampDuty = otherCharges["stampDuty"]
	}

	otherChargesAmount := 0
	for _, charge := range otherCharges {
		otherChargesAmount += charge
	}

	totalOtherCharges := processingFeePlusGST + insurancePremiumPlusGST + advanceEMI + float64(otherChargesAmount)
	totalOtherChargesStr := journey.AmountToString(obj.LenderID, totalOtherCharges)

	var outstandingPrincipal float64
	isTopUp, _, _, _ := journey.IsTopUp(obj.SourceEntityID)
	if isTopUp {
		outstandingPrincipal, _, err = underwriting.GetForeclosureDetails(obj.UserID)
		if err != nil {
			logger.WithUser(obj.UserID).Errorln(err)
			return ""
		}
		amountToBePaid += outstandingPrincipal
		disbursalAmount -= outstandingPrincipal
		totalOtherChargesStr += journey.AmountToString(obj.LenderID, totalOtherCharges+outstandingPrincipal)
	}

	aprPercentage := ""
	if journey.IsOneMuthootPartner(obj.SourceEntityID) {
		aprPercentage = fmt.Sprintf("%.2f", obj.Interest+2.5)
	}

	amountToBePaid = obj.Amount + totalInterestAmount

	employerDetails, _ := users.GetEmployerDetails(obj.UserID)
	LateFeeRate, LateFeeCharge, BounceCharge, ForeclosureCharge := calc.GetContingentCharges(obj.LenderID)
	docOnlinePremium, _ := insuranceutils.GetUnpaidHealthInsurance(loanApplicationID)

	placeholderData := map[string]interface{}{
		"ApplicationType":                      "New",
		"DisbursedDate":                        obj.DisbursedDate,
		"LoanType":                             obj.LoanType,
		"BusinessName":                         businessDetails.BusinessName,
		"UserName":                             obj.Name,
		"FathersName":                          obj.FathersName,
		"DOB":                                  dateOfBirth.Format("2 Jan 2006"),
		"Gender":                               constants.GenderNumToStr[obj.Gender],
		"MaritalStatus":                        constants.MaritalNumToStr[obj.MaritalStatus],
		"Occupation":                           occupation,
		"Nationality":                          "Indian",
		"PanNumber":                            obj.PAN,
		"ProofOfAddress":                       proofOfAddress,
		"AddressType":                          constants.ResidenceNumToStr[obj.ResidenceType],
		"CurrentAddress":                       currentAddress,
		"PermanentAddress":                     permanentAddress,
		"BusinessAddress":                      businessDetails.BusinessAddresss,
		"Phone":                                obj.Mobile,
		"Email":                                obj.Email,
		"BankName":                             bankObj.Bank,
		"OtherCharges":                         totalOtherChargesStr,
		"AccountNumber":                        bankObj.Accountnumber,
		"IFSC":                                 bankObj.Ifsc,
		"AccountType":                          bankObj.AccountType,
		"BeneficiaryName":                      bankObj.HolderName,
		"LoanID":                               obj.LoanApplicationNo,
		"City":                                 addressMap["city"],
		"State":                                addressMap["state"],
		"LoanAmount":                           journey.AmountToString(obj.LenderID, obj.Amount),
		"LoanAmountWords":                      loanAmountWords,
		"InterestRate":                         obj.InterestText,
		"Purpose":                              purpose,
		"KycDocsHTML":                          kycHTMLBuilder.String(),
		"FacilityType":                         facilityType,
		"Tenure":                               obj.Tenure,
		"EMITableHTML":                         emiHTMLBuilder.String(),
		"EMIWithPrincipalAndInterestTableHtml": emiWithPricipalAndInterestHTMLBuilder.String(),
		"AmortScheduleHtml":                    amortScheduleHTMLBuilder.String(),
		"Age":                                  ageValue,
		"LenderName":                           lenderObj.LenderName,
		"InstallmentRowHTML":                   iHTMLBuilder.String(),
		"ProcessingFee":                        journey.AmountToString(obj.LenderID, obj.ProcessingFee),
		"GSTAmount":                            journey.AmountToString(obj.LenderID, gstAmount),
		"ProcessingFeePlusGST":                 journey.AmountToString(obj.LenderID, processingFeePlusGST),
		"SignImagePath":                        signedURL,
		"SignDate":                             signDate.Format("2 Jan 2006"),
		"SignDateTime":                         signDate.In(loc).Format("2 Jan 2006 15:04:05"),
		"SignDateDay":                          signDate.In(loc).Day(),
		"SignDateMonth":                        signDate.In(loc).Month().String(),
		"SignDateYear":                         signDate.In(loc).Year(),
		"SourceEntityName":                     sourceEntityObj.LegalName,
		"EmiNos":                               EMINos,
		"EmiAmount":                            journey.AmountToString(obj.LenderID, emi),
		"EmiStartDate":                         emiDates[0].Format("2 Jan 2006"),
		"EmiEndDate":                           emiDates[len(emiDates)-1].Format("2 Jan 2006"),
		"AdvanceEmiAmount":                     journey.AmountToString(obj.LenderID, advanceEMI),
		"AdvanceEmiNos":                        advanceEMINo,
		"BounceCharge":                         fmt.Sprintf("%.2f", BounceCharge),
		"LateCharge":                           fmt.Sprintf("%.2f", LateFeeCharge),
		"LateFeeRate":                          fmt.Sprintf("%.2f perc", LateFeeRate),
		"ForeClosureCharge":                    fmt.Sprintf("%.2f", ForeclosureCharge),
		"NatureOfEntity":                       businessDetails.Constitution,
		"BusinessRelation":                     businessDetails.BusinessRelation,
		"GST":                                  fmt.Sprintf("%.0f", obj.GST),
		"DisbursalAmount":                      journey.AmountToString(obj.LenderID, disbursalAmount),
		"GSTIN":                                businessDetails.Gstin,
		"DateOfInc":                            businessDetails.DateOfIncorporation,
		"Pincode":                              addressMap["pincode"],
		"MandateNo":                            mandateNo,
		"MandateDate":                          mandateDate,
		"IPAddress":                            obj.IPAddress,
		"LatLongIP":                            latLongIP,
		"UniqueID":                             obj.UniqueID,
		"InsurancePremiumPlusGST":              journey.AmountToString(obj.LenderID, insurancePremiumPlusGST),
		"RepaymentFrequency":                   RepaymentFrequency,
		"AmountToBePaid":                       journey.AmountToString(obj.LenderID, amountToBePaid),
		"Interest":                             obj.Interest,
		"TotalInterestAmount":                  journey.AmountToString(obj.LenderID, totalInterestAmount),
		"APR":                                  fmt.Sprintf("%.2f", apr),
		"APRPercentage":                        aprPercentage,
		"StampDuty":                            stampDuty,
		"EDIWithOutstandingPrincipalAndInterestHTMLBuilder": ediWithOutstandingPrincipalAndInterestHTMLBuilder.String(),
		"CoolOffPeriod":        kfsOtherDisclosures.CoolOffPeriod,
		"LSPAgent":             kfsOtherDisclosures.LSPAgent,
		"LSPAgentCustomer":     kfsOtherDisclosures.LSPAgentCustomer,
		"NodalOfficer":         kfsOtherDisclosures.NodalOfficer,
		"LSPLink":              kfsOtherDisclosures.LSPLink,
		"RELink":               kfsOtherDisclosures.RELink,
		"LockInPeriod":         kfsOtherDisclosures.LockInPeriod,
		"EmployerName":         employerDetails.Name,
		"EmployerPincode":      employerDetails.Pincode,
		"EmployerCity":         employerDetails.City,
		"EmployerState":        employerDetails.State,
		"DocOnlinePremium":     docOnlinePremium,
		"DocumentCharges":      documentChargesPlusGST,
		"OutstandingPrincipal": outstandingPrincipal,
		"isTopUp":              isTopUp,
		"EmiValueWords":        fmt.Sprintf("%s (%s)", journey.AmountToString(obj.LenderID, emi), general.AmountInWords(emi)),
		"TenureInYears":        int(math.Ceil(float64(obj.Tenure) / 12)),
	}

	if obj.LenderID == constants.ABFLPLID {
		placeholderData["IHOCharges"] = math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst))
		placeholderData["payableToThirdParty"] = general.FormatCurrency(math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst))+calc.CalculateAmountWithGST(premium-ihoInsurancePremium, insuranceGst), true)
		placeholderData["insuranceAndIHOCharges"] = math.Ceil(calc.CalculateAmountWithGST(ihoInsurancePremium, insuranceGst)) + calc.CalculateAmountWithGST(premium-ihoInsurancePremium, insuranceGst)
		placeholderData["sanctionedLoanAmount"] = general.FormatCurrency(obj.Amount, true)
		placeholderData["interestRateType"] = "Fixed"
		placeholderData["rateOfInterest"] = journey.AmountToString(obj.LenderID, obj.Interest) + "%"
		placeholderData["totalInterestAmount"] = general.FormatCurrency(totalInterestAmount, true)
		placeholderData["typeOfEpi"] = "Monthly"
		placeholderData["epi"] = general.FormatCurrency(emi, true)
		placeholderData["numberOfEpIs"] = EMINos
		placeholderData["totalInterestAmount"] = general.FormatCurrency(totalInterestAmount, true)
		placeholderData["netDisbursedAmount"] = general.FormatCurrency(disbursalAmount, true)
		placeholderData["totalAmountToBePaidByTheBorrower"] = general.FormatCurrency(totalInterestAmount+obj.Amount, true)
		placeholderData["dueDateOfPayment"] = emiDates[0].Format("2") + "th of every month"
		placeholderData["payableToTheRe"] = general.FormatCurrency(processingFeePlusGST, true)
		placeholderData["loanID"] = obj.LoanApplicationNo
		placeholderData["accountNo"] = obj.LoanApplicationNo
		placeholderData["loanTerm"] = "Months"
		placeholderData["tenureMonths"] = obj.Tenure
		placeholderData["PayableThirdParty"] = math.Ceil(insurancePremiumPlusGST)
		placeholderData["FeePayable"] = general.FormatCurrency(processingFeePlusGST+math.Ceil(insurancePremiumPlusGST), true)
		placeholderData["APR"] = journey.AmountToString(obj.LenderID, apr) + "%"
		placeholderData["applicantName"] = obj.Name
		placeholderData["date"] = signDate.Format("2 Jan 2006")
		placeholderData["commencementOfRepayment"] = int(emiDates[0].Sub(signDate).Hours() / 24) //difference between EMI commencement date and aggreement generation date
		placeholderData["ip"] = obj.IPAddress
	}

	if obj.LenderID == constants.IIFLBLID || journey.IsIIFLBLSourcing(obj.SourceEntityID) {
		loanAmount := math.Round(obj.Amount*100) / 100
		totalInterestRounded := math.Round(totalInterestAmount*100) / 100
		placeholderData["DocumentCharges"] = fmt.Sprintf("%.2f", documentChargesPlusGST)
		placeholderData["AmountToBePaid"] = journey.AmountToString(obj.LenderID, loanAmount+totalInterestRounded)
		placeholderData["daysTillFirstEMI"] = int(emiDates[0].Sub(signDate).Hours() / 24) //difference between EMI commencement date and aggreement generation date
		placeholderData["payableToTheRe"] = journey.AmountToString(obj.LenderID, processingFeePlusGST+advanceEMI+documentChargesPlusGST+float64(stampDuty))
		placeholderData["chargesPayable"] = journey.AmountToString(obj.LenderID, processingFeePlusGST+advanceEMI+documentChargesPlusGST+float64(stampDuty)+insurancePremiumPlusGST)
		placeholderData["DisbursalAmount"] = journey.AmountToString(obj.LenderID, loanAmount-(processingFeePlusGST+advanceEMI+documentChargesPlusGST+float64(stampDuty)+insurancePremiumPlusGST))
	}

	if journey.IsSuperMoneyJourney(obj.UserID, obj.SourceEntityID) {

		type LoanApplicationMetaData struct {
			AgreementData lenderservice.PflRps `json:"agreementData"`
		}

		var metaDataObj LoanApplicationMetaData
		if obj.Metadata != "" {
			err := json.Unmarshal([]byte(obj.Metadata), &metaDataObj)
			if err != nil {
				logger.WithUser(obj.UserID).Error(err)
				return ""
			}
		}

		userBankDetails, err := userbankdetails.GetData(context.TODO(), obj.UserID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Error(err)
			return ""
		}

		_, name, err := loankycdetails.GetIdentifierAndNameForDocType(loanApplicationID, constants.DocCatPANCard)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Error(err)
			return ""
		}

		placeholderData["loanDisbursalDate"] = metaDataObj.AgreementData.LoanDisbursalDate
		placeholderData["appliedLoanAmount"] = metaDataObj.AgreementData.AppliedLoanAmount
		placeholderData["healthWellnessCharge"] = metaDataObj.AgreementData.HealthWellnessCharge
		placeholderData["totalInterest"] = metaDataObj.AgreementData.TotalInterest
		placeholderData["totalAmountsToBePaidByTheBorrower"] = metaDataObj.AgreementData.TotalAmountsToBePaidByTheBorrower
		placeholderData["tenure"] = metaDataObj.AgreementData.Tenure
		placeholderData["emiAmount"] = metaDataObj.AgreementData.EmiAmount
		placeholderData["firstEmiDate"] = metaDataObj.AgreementData.FirstEmiDate
		placeholderData["rateOfInterest"] = metaDataObj.AgreementData.RateOfInterest
		placeholderData["stampDuty"] = metaDataObj.AgreementData.StampDuty
		placeholderData["processingFees"] = metaDataObj.AgreementData.ProcessingFees
		placeholderData["accountNumber"] = userBankDetails.AccountNumber
		placeholderData["bankName"] = userBankDetails.BankName
		placeholderData["ifscCode"] = userBankDetails.IfscCode
		placeholderData["ovdName"] = name
		placeholderData["name"] = name
		placeholderData["ovdAddress"] = obj.PermanentAddress
	}

	placeholderData, err = underwriting.AdditionalClauseFlags(loanApplicationID, placeholderData)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error(err)
		return ""
	}
	if journey.IsMFLBLSourcing(obj.SourceEntityID) {

		userlocationObj, err := userlocation.Get(obj.UserID, userlocation.LocationTypeSelfieCapture)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}

		var uan string
		query := `select coalesce(uan, '') as uan from user_business_uan where user_id = $1 order by created_at desc limit 1`
		err = database.Get(&uan, query, obj.UserID)
		if err != nil {
			err := fmt.Errorf("no uan details present for loanApplicationID: %s, err: %s", loanApplicationID, err)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}

		maritalStatus := constants.MaritalNumToStr[obj.MaritalStatus]
		if maritalStatus == "" {
			maritalStatus, err = users.GetDynamicUserInfoKey(obj.UserID, "maritalStatus", "")
			if err != nil {
				maritalStatus = "Not Applicable"
			}
		}

		placeholderData["location"] = userlocationObj.Address
		placeholderData["stock"] = "Not Applicable"
		placeholderData["total"] = "Not Applicable"
		placeholderData["security"] = "Not Applicable"
		placeholderData["date"] = time.Now().In(loc).Format("02/01/2006 15:04:05")
		placeholderData["mobileNumber"] = obj.Mobile
		placeholderData["DOB"] = dateOfBirth.Format("02/01/2006")
		placeholderData["applicantName"] = obj.Name
		placeholderData["applicationNumber"] = obj.LoanApplicationNo
		placeholderData["applicationName"] = obj.Name
		placeholderData["MaritalStatus"] = maritalStatus
		placeholderData["borrowerName"] = obj.Name
		placeholderData["MobileNoforOTP"] = obj.Mobile
		placeholderData["platform"] = "Digital Journey"
		placeholderData["ipAddress"] = ""
		placeholderData["timeStamp"] = signDate.In(loc).Format("02/01/2006 15:04:05")
		placeholderData["loanID"] = obj.LoanApplicationNo
		placeholderData["accountNo"] = obj.LoanApplicationNo

		placeholderData["address"] = permanentAddress
		placeholderData["state"] = addressMap["state"]
		placeholderData["State"] = addressMap["state"]

		placeholderData["identityProofDetails"] = aadhaarIdentifier
		placeholderData["issueExpiryDate"] = ""
		placeholderData["issueExpiryDate"] = ""

		// TODO: Remove hard coding and get from some table.
		placeholderData["processingFeesPercentage"] = "2 %"
		placeholderData["processingFees"] = journey.AmountToString(obj.LenderID, processingFeePlusGST)

		placeholderData["businessProof"] = "Udyam Aadhar"
		placeholderData["BussinessShopName"] = businessDetails.BusinessName
		placeholderData["businessProofDetails"] = uan
		placeholderData["businessaddress"] = businessDetails.BusinessAddresss
		placeholderData["businesscity"] = businessDetails.City
		placeholderData["businessstate"] = businessDetails.State
		placeholderData["businesspincode"] = businessDetails.Pincode
		placeholderData["typeOfBusiness"] = businessDetails.BusinessRelation
		placeholderData["constitutionOfBusiness"] = businessDetails.Constitution
		placeholderData["mflSignHtml"] = fmt.Sprint("<img style=\"width: 100%; height: 100% ;\" src=\"https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/Signature+sample.jpg\">")

		placeholderData["typeOfLoan"] = "Unsecured Business Loan"
		placeholderData["loanAmount"] = journey.AmountToString(obj.LenderID, obj.Amount)
		placeholderData["sanctionedLoanAmount"] = journey.AmountToString(obj.LenderID, obj.Amount)
		placeholderData["esignNameIdentifier"] = "Name: " + obj.Name
		// placeholderData["esignNumberIdentifier"] = "Signed Using : " + obj.Mobile
		placeholderData["sanctionedLoanAmount"] = journey.AmountToString(obj.LenderID, obj.Amount)
		placeholderData["disbursalSchedule"] = "SINGLE"
		placeholderData["commencementOfRepayment"] = emiDates[0].Format("02/01/2006")
		placeholderData["dueDate"] = emiDates[0].Format("02/01/2006")
		placeholderData["commencementOfRepayments"] = emiDates[0].Format("02/01/2006")
		placeholderData["firstInstalmentDate"] = emiDates[0].Format("02/01/2006")
		placeholderData["loanTerm"] = "Months"
		placeholderData["tenureMonths"] = obj.Tenure
		placeholderData["replaymentMethod"] = "NACH"
		placeholderData["eachInstalment"] = journey.AmountToString(obj.LenderID, emi)
		placeholderData["typeOfInstalments"] = "EMI"
		placeholderData["numberOfEpIs"] = EMINos
		placeholderData["ciNoOfInstalments"] = EMINos
		placeholderData["noOfInstalments"] = EMINos
		placeholderData["epi"] = journey.AmountToString(obj.LenderID, math.Ceil(emi))
		placeholderData["typeOfEpi"] = "Monthly"
		placeholderData["interestRate"] = journey.AmountToString(obj.LenderID, obj.Interest) + "% "
		placeholderData["interestRateSanctionPage"] = journey.AmountToString(obj.LenderID, obj.Interest) + "% "
		placeholderData["payableToTheRe"] = journey.AmountToString(obj.LenderID, processingFeePlusGST+obj.AdvanceEMI)
		placeholderData["rateOfInterest"] = obj.Interest
		placeholderData["totalInterestAmount"] = journey.AmountToString(obj.LenderID, totalInterestAmount)
		placeholderData["pfOneTimeRE"] = "One time"
		placeholderData["interestRateType"] = "Fixed"
		placeholderData["pfAmountRE"] = journey.AmountToString(obj.LenderID, processingFeePlusGST)
		placeholderData["annualPercentageRate"] = fmt.Sprintf("%.2f", apr) + "%"
		placeholderData["scheduleOfDisbursement"] = "Single"
		placeholderData["loanPurpose"] = obj.LoanPurpose
		placeholderData["APR"] = fmt.Sprintf("%.2f", apr)
		placeholderData["purposeOfLoan"] = obj.LoanPurpose
		placeholderData["dueDateOfPayment"] = emiDates[0].Format("2") + "th of every month"
		placeholderData["netDisbursedAmount"] = journey.AmountToString(obj.LenderID, disbursalAmount)
		placeholderData["totalAmountToBePaidByTheBorrower"] = journey.AmountToString(obj.LenderID, totalInterestAmount+obj.Amount)

		//in edi journey these keys have different values from emi journey
		placeholderData["schemeName"] = "Bandhan Plus"
		placeholderData["repaymentMethod"] = "Monthly"
		placeholderData["bounceCharges"] = "Rs. 500 +  GST per Bounce"
		placeholderData["product"] = "Bandhan Plus Loan"
		placeholderData["repaymentMandateCharges"] = `Rs 500/- + GST
		Bounce charges shall means charged for (i) dishonour of any payment instruments; or (ii) non-payment of installment (s) on their respective due dates due to dishonour of payment mandate or non-registration of the payment mandate or any other reason`
		placeholderData["loanPreClosureCharge"] = "3% + GST on overall Principal Outstanding"
		placeholderData["fieldVisitCollectionCharges"] = "Rs 50/- + GST"
		placeholderData["currentAgreement"] = "EMI"
		placeholderData["installmentTypeDaily"] = false
		placeholderData["repaymentMandateSwap"] = "Not Applicable"
		placeholderData["rateOfInterest"] = journey.AmountToString(obj.LenderID, obj.Interest) + " %"
		placeholderData["brokenPeriodInterest"] = journey.AmountToString(obj.LenderID, advanceEMI)

		if installmentFrequency == constants.InstallmentProgrammeDaily {
			placeholderData["payableToTheRe"] = journey.AmountToString(obj.LenderID, processingFeePlusGST)
			placeholderData["edi"] = ediAmount
			placeholderData["installmentTypeDaily"] = true
			placeholderData["typeOfInstalments"] = "EDI"
			placeholderData["epi"] = ediAmount
			placeholderData["typeOfEpi"] = "Daily"
			placeholderData["loanTerm"] = "Days"
			placeholderData["EdiAmortScheduleHTMLBuilder"] = ediAmortScheduleHTMLBuilder.String()
			placeholderData["schemeName"] = "Vyapar Mitra"
			placeholderData["repaymentMethod"] = "Daily"
			placeholderData["bounceCharges"] = "Not Applicable"
			placeholderData["product"] = "Vyapar Mitra Loan"
			placeholderData["repaymentMandateCharges"] = "NIL"
			placeholderData["repaymentMandateSwap"] = "NIL"
			placeholderData["fieldVisitCollectionCharges"] = "Rs 30/- + GST"
			placeholderData["currentAgreement"] = "EDI"
			placeholderData["typeOfInstalments"] = "EDI"
			placeholderData["numberOfEpIs"] = len(ediDates)
			placeholderData["ciNoOfInstalments"] = len(ediDates)
			placeholderData["noOfInstalments"] = len(ediDates)
			placeholderData["commencementOfRepayments"] = ediDates[0].Format("02/01/2006")
			placeholderData["commencementOfRepayment"] = ediDates[0].Format("02/01/2006")
			placeholderData["dueDateOfPayment"] = ediDates[0].Format("02/01/2006")
			placeholderData["eachInstalment"] = journey.AmountToString(obj.LenderID, ediAmount)
			placeholderData["dueDate"] = ediDates[0].Format("02/01/2006")
			placeholderData["loanPreClosureCharge"] = "NIL"
			placeholderData["processingFeesPercentage"] = "3 %"
			placeholderData["brokenPeriodInterest"] = "NA"
			placeholderData["netDisbursedAmount"] = journey.AmountToString(obj.LenderID, obj.Amount-math.Ceil(processingFeePlusGST))
		}
	}

	htmlBody := general.GetStringFromTemplate(string(templateString), placeholderData)

	// upload html to s3
	objectKey := obj.UserID + "/" + "signed_" + obj.LoanApplicationNo + ".html"
	_, flag := s3.UploadRawFileS3(strings.NewReader(htmlBody), objectKey)
	if !flag {
		err := fmt.Errorf("unable to upload signed agreement to s3 for loanApplicationID: %s", loanApplicationID)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}
	// convert to pdf and upload to s3
	pdfObjectKey := obj.UserID + "/" + "signed_" + obj.LoanApplicationNo + ".pdf"
	if boostID != "" {
		// change file name if boost case
		pdfObjectKey = obj.UserID + "/" + boostID + ".pdf"
		query = `UPDATE boost_attempts set sign_agreement = $1, updated_at = NOW() where boost_id = $2`
		_, err = database.Exec(query, pdfObjectKey, boostID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}
	}
	fileConvert := convert.HTMLToPDF(objectKey, pdfObjectKey)
	if !fileConvert {
		err := fmt.Errorf("PDF Conversion failed for loanApplicationID: %s", loanApplicationID)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}
	// now update the object key in db
	query = `update loan_application set
		updated_at = current_timestamp,
		sign_url = $1
		where loan_application_id = $2`
	_, err = database.Exec(query, pdfObjectKey, loanApplicationID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ""
	}
	// mark an activity here
	dateTimeNowString := general.GetTimeStampString()
	activityObj := activity.ActivityEvent{
		UserID:            obj.UserID,
		SourceEntityID:    obj.SourceEntityID,
		LoanApplicationID: loanApplicationID,
		EntityType:        constants.EntityTypeSystem,
		EntityRef:         "",
		EventType:         constants.ActivityLoanSignedAgreementGenerated,
		Description:       "",
	}
	activity.RegisterEvent(&activityObj, dateTimeNowString)

	pdfURL := s3.GetPresignedURLS3(pdfObjectKey, constants.WeekInMinutes) //active for 7 days

	if obj.LoanType != constants.LoanTypeCreditLine && obj.LoanType != constants.LoanTypeOverDraft && journey.SendCustomerMail(obj.SourceEntityID) {
		// now send email to user in case its not credit line
		paymentDetailObj := PaymentDetailStruct{
			EMI:                     emi,
			ProcessingFeePlusGST:    processingFeePlusGST,
			AdvanceEMIAmount:        advanceEMI,
			DisbursalAmount:         disbursalAmount,
			InsurancePremiumPlusGST: insurancePremiumPlusGST,
			EDI:                     ediAmount,
		}

		_, errStr := SendEmailToUser(obj, lenderObj.LenderName, sourceEntityObj.SourceEntityName, pdfURL, paymentDetailObj)
		if errStr != "" {
			err := fmt.Errorf(errStr)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}
	}

	//send agreement to user via sms
	if obj.Mobile != "" && journey.SendSMSPostAgreement(obj.SourceEntityID) && obj.LenderID != constants.ABFLPLID {
		err = SendAgreementViaSMS(obj.UserID, obj.SourceEntityID, obj.Mobile, obj.Name, obj.LoanApplicationNo, lenderObj.LenderName, pdfURL, obj.Amount, obj.LenderID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}
	}

	// now send email to lender (lender kit) along with kyc zip file and payment schedule xlsx

	if obj.LoanType != constants.LoanTypeCreditLine && obj.LoanType != constants.LoanTypeOverDraft {
		// generate repayment schedule only in case of non credit line
		f := GetPaymentScheduleXLSX(obj.LoanApplicationNo, obj.Name, emi, emiDates)
		buffer, err := f.WriteToBuffer()
		if err != nil {
			err := fmt.Errorf("unable to write excel file for loanApplicationID: %s", loanApplicationID)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			panic(err)
		}
		_, flag = s3.UploadRawFileS3(buffer, obj.UserID+"/"+"schedule_"+obj.LoanApplicationNo+".xlsx")
		if !flag {
			err := fmt.Errorf("unable to upload signed agreement to s3 for loanApplicationID: %s", loanApplicationID)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return ""
		}
	}

	// if lenderObj.Emails != "" {
	// 	emailData := map[string]interface{}{
	// 		"UserName":           obj.Name,
	// 		"PanNumber":          obj.Pan,
	// 		"CurrentAddress":     currentAddress,
	// 		"PinCode":            addressMap["pincode"],
	// 		"Mobile":             obj.Mobile,
	// 		"Email":              obj.Email,
	// 		"DOB":                dateOfBirth.Format("2 Jan 2006"),
	// 		"Age":                ageValue,
	// 		"KycDocsHTML":        kycHTMLBuilder.String(),
	// 		"AccountNumber":      bankObj.Accountnumber,
	// 		"IFSC":               bankObj.Ifsc,
	// 		"LoanID":             obj.Loan_application_no,
	// 		"DisbursalAmount":    disbursalAmount,
	// 		"InstallmentRowHTML": iHTMLBuilder.String(),
	// 		"LoanType":           obj.LoanType,
	// 	}
	// 	if obj.LoanType == constants.LoanTypeCreditLine {
	// 		emailData["DisbursalAmount"] = "NA"
	// 	}
	// 	attachments := []emaillib.EmailAttachment{
	// 		{
	// 			Path:     pdfURL,
	// 			FileName: fmt.Sprintf("Signed_Agreement_%s.pdf", obj.Loan_application_no),
	// 		},
	// 		{
	// 			Path:     s3.GetPresignedURLS3(obj.Kyc_zip_path, 300),
	// 			FileName: fmt.Sprintf("KYC_%s.zip", obj.Loan_application_no),
	// 		},
	// 	}

	// 	if obj.LoanType != constants.LoanTypeCreditLine {
	// 		attachments = append(attachments, emaillib.EmailAttachment{
	// 			Path:     s3.GetPresignedURLS3(obj.User_id+"/"+"schedule_"+obj.Loan_application_no+".xlsx", 300),
	// 			FileName: fmt.Sprintf("Repayment_Schedule_%s.xlsx", obj.Loan_application_no),
	// 		})
	// 	}

	// 	emailSubject := general.GetStringFromTemplate(emaillib.LenderKitEmailSignedSubject, map[string]interface{}{
	// 		"LoanID": obj.Loan_application_no,
	// 	})
	// 	emailBody := general.GetStringFromTemplate(emaillib.LenderKitEmailSignedHTML, emailData)
	// 	lenderEmails := strings.Split(lenderObj.Emails, ",")
	// 	lenderNames := []string{}
	// 	for i := 0; i < len(lenderEmails); i++ {
	// 		lenderNames = append(lenderNames, lenderObj.Lender_name)
	// 	}
	// 	emaillib.SendMail(lenderEmails, lenderNames, emailSubject, emailBody, attachments, true)
	// }

	if obj.SourceEntityID == constants.GeniusID || obj.SourceEntityID == constants.LetsTransportID {
		// generate authorization letter if required
		buf = new(bytes.Buffer)
		ioRPointer := s3.GetFileStream("templates/authorization_letter.html")
		if ioRPointer == nil {
			err := fmt.Errorf("auth template not found in s3 for loanApplicationID: %s", loanApplicationID)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return ""
		}
		ioR := *ioRPointer
		buf.ReadFrom(ioR)
		templateString = buf.String()

		appName, _ := journey.GetLoanAppName(obj.SourceEntityID, sourceEntityObj.SourceEntityName, lenderObj.LenderName)
		placeholderData = map[string]interface{}{
			"UserName":                obj.Name,
			"FathersName":             obj.FathersName,
			"Age":                     ageValue,
			"PanNumber":               obj.PAN,
			"CurrentAddress":          currentAddress,
			"LoanAmount":              fmt.Sprintf("%.2f", obj.Amount),
			"LenderName":              lenderObj.LenderName,
			"SignDate":                signDate.Format("2 Jan 2006"),
			"EMITableHTML":            emiHTMLBuilder.String(),
			"SourceEntityName":        sourceEntityObj.LegalName,
			"Phone":                   obj.Mobile,
			"SignImagePath":           signedURL,
			"LoanAppName":             appName,
			"InsurancePremiumPlusGST": insurancePremiumPlusGST,
		}
		htmlBody = general.GetStringFromTemplate(string(templateString), placeholderData)
		// upload html to s3
		objectKey = obj.UserID + "/" + "auth_" + obj.LoanApplicationNo + ".html"
		_, flag = s3.UploadRawFileS3(strings.NewReader(htmlBody), objectKey)
		if !flag {
			err := fmt.Errorf("unable to upload auth letter to s3 for loanApplicationID: %s", loanApplicationID)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return ""
		}
		// convert to pdf and upload to s3
		pdfObjectKey = obj.UserID + "/" + "auth_" + obj.LoanApplicationNo + ".pdf"
		fileConvert := convert.HTMLToPDF(objectKey, pdfObjectKey)
		if !fileConvert {
			err := fmt.Errorf("PDF Conversion failed for loanApplicationID: %s", loanApplicationID)
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return ""
		}
		// now update the object key in db
		query = `update loan_application set
			updated_at = current_timestamp,
			auth_letter_path = $1
			where loan_application_id = $2`
		_, err = database.Exec(query, pdfObjectKey, loanApplicationID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return ""
		}

		if sourceEntityObj.Emails != "" {
			pdfURL = s3.GetPresignedURLS3(pdfObjectKey, 300)

			// now send email to sourcing entity with authorization letter
			emailData := map[string]interface{}{
				"LoanID":         obj.LoanApplicationNo,
				"UserName":       obj.Name,
				"Mobile":         obj.Mobile,
				"CurrentAddress": currentAddress,
				"PinCode":        addressMap["pincode"],
				"UniqueID":       obj.UniqueID,
			}
			attachments := []emaillib.EmailAttachment{
				{
					Path:     pdfURL,
					FileName: fmt.Sprintf("Auth_Letter_%s.pdf", obj.LoanApplicationNo),
				},
			}
			emailSubject := general.GetStringFromTemplate(emaillib.AuthorizationLetterEmailSubject, map[string]interface{}{
				"UserName": obj.Name,
				"UniqueID": obj.UniqueID,
			})
			emailBody := general.GetStringFromTemplate(emaillib.AuthorizationLetterEmailHTML, emailData)
			sourceEntityEmails := strings.Split(sourceEntityObj.Emails, ",")
			sourceEntityNames := []string{}
			for i := 0; i < len(sourceEntityEmails); i++ {
				sourceEntityNames = append(sourceEntityNames, sourceEntityObj.SourceEntityName)
			}
			emaillib.SendMail(sourceEntityEmails, sourceEntityNames, emailSubject, emailBody, attachments, true)
		}
	}
	return pdfObjectKey
}

func SendEmailToUser(obj agreementutils.AgreementDBDataStruct, lenderName, sourceEntityName, pdfURL string, paymentDetailObj PaymentDetailStruct) (bool, string) {
	lenderObj, _ := lender.Get(obj.LenderID)
	appName, throughSentence := journey.GetLoanAppName(obj.SourceEntityID, sourceEntityName, lenderObj.LenderName)
	emailTemplate := emaillib.WelcomeEmailHTML
	emailSubject := general.GetStringFromTemplate(emaillib.WelcomeEmailSubject, map[string]interface{}{
		"LoanAppName": appName,
	})

	installmentProgramme, _ := journey.GetInstallmentFrequency(obj.UserID, obj.SourceEntityID)

	toEmail := []string{obj.Email}
	toName := []string{obj.Name}
	if general.InArr(obj.LenderID, []string{constants.ABFLPLID}) {
		paymentDetailObj.DisbursalAmount += paymentDetailObj.AdvanceEMIAmount
		emailTemplate = emaillib.WelcomeEmailHTMLABFL
	} else if general.InArr(obj.LenderID, []string{constants.MFLBLID}) {
		emailTemplate = emaillib.WelcomeEmailHTMLMFLBL

		if journey.IsEDIJourney(obj.UserID, obj.SourceEntityID) {
			paymentDetailObj.AdvanceEMIAmount = 0
		}

		paymentDetailObj.DisbursalAmount += paymentDetailObj.AdvanceEMIAmount
		emailSubject = general.GetStringFromTemplate(emaillib.WelcomeEmailSubjectMFLBL, map[string]interface{}{
			"LoanAppName": appName,
		})
		toName = append(toName, "Notification Muthoot")
		toEmail = append(toEmail, "<EMAIL>")
	}

	emailData := map[string]interface{}{
		"Name":                    obj.Name,
		"LenderName":              lenderName,
		"Emi":                     paymentDetailObj.EMI,
		"Tenure":                  obj.Tenure,
		"LoanApplicationNum":      obj.LoanApplicationNo,
		"LoanAmount":              fmt.Sprintf("%.2f", obj.Amount),
		"Interest":                obj.InterestText,
		"ProcessingFeePlusGST":    paymentDetailObj.ProcessingFeePlusGST,
		"AdvanceEmiAmount":        paymentDetailObj.AdvanceEMIAmount,
		"DisbursalAmount":         paymentDetailObj.DisbursalAmount,
		"LoanAppName":             appName,
		"ThroughApp":              throughSentence,
		"SupportEmail":            journey.GetSupportEmail(obj.SourceEntityID, obj.LenderID),
		"InsurancePremiumPlusGST": paymentDetailObj.InsurancePremiumPlusGST,
		"InstallmentProgramme":    installmentProgramme,
		"Edi":                     paymentDetailObj.EDI,
	}
	attachments := []emaillib.EmailAttachment{
		{
			Path:     pdfURL,
			FileName: "Signed_Agreement.pdf",
		},
	}
	attachments = append(attachments, journey.ExtraCustomerAttachment(obj.SourceEntityID)...)
	emailBody := general.GetStringFromTemplate(emailTemplate, emailData)

	// TODO: Change once the template is finalized
	isSuccessful := true
	var errStr string
	if !journey.IsABFLBLSourcing(obj.SourceEntityID) {
		isSuccessful, errStr = emaillib.SendMail(toEmail, toName, emailSubject, emailBody, attachments, false)
	}
	return isSuccessful, errStr
}

func GetSignedAgreement(ctx context.Context, loaanApplicationID, lenderID string, opts GetSignedAgreementOptions) (*lenderservice.KYCDocumentstructsDetails, error) {
	loanObj, err := loanapplication.Get(ctx, loaanApplicationID)
	if err != nil {
		logger.WithLoanApplication(loaanApplicationID).Error(err)
		return nil, err
	}

	if loanObj.SignURL == "" {
		err := fmt.Errorf("signed agreement not found")
		logger.WithLoanApplication(loaanApplicationID).Error(err)
		return nil, err
	}
	signURLObj := loanObj.SignURL
	if !s3.IsObjectInS3(signURLObj) {
		errMsg := fmt.Sprintf("object not found in s3, key: %s", signURLObj)
		logger.WithLoanApplication(loaanApplicationID).Error(errMsg)
		return nil, errors.New(errMsg)
	}
	var signedAgreementURL string

	if opts.UseDownloadableRedirect {
		// Insert into media and downloadables
		downloadableID, err := downloadables.CreateAlongWithMediaInsertion(ctx, loanObj.UserID, media.UploadMediaDataStruct{
			MediaID:   general.GetUUID(),
			MediaType: constants.MediaTypeAgreement,
			UserID:    loanObj.UserID,
			Path:      signURLObj,
		}, downloadables.Options{})

		if err != nil {
			logger.WithLoanApplication(loaanApplicationID).Errorln(err)
			return nil, err
		}

		signedAgreementURL = downloadables.GetDownloadURL(ctx, downloadableID)
	} else {
		signedAgreementURL = s3.GetPresignedURLS3(signURLObj, 10080)
	}

	return &lenderservice.KYCDocumentstructsDetails{
		DocumentURL: signedAgreementURL,
	}, err
}

// SendAgreementViaSMS takes userId, mobile number to which sms needs to be sent, user's name, loan application no., lender name and s3 URL for the document, sends sms to user with link to download the document and returns an error object if sms was not sent and nil in case of success
func SendAgreementViaSMS(userID, sourceEntityID, mobile, name, loanNo, lenderName, pdfURL string, loanAmount float64, lenderID string) error {
	var text string
	var dlteDetails karix.DltDetails
	var smsErr error
	sent := true

	if journey.IsMuthootCLPartner(sourceEntityID) {
		postAggreementSMSTemplates := []karix.DltDetails{sms.CreditLineActivationAlertMFLDltDetails, sms.MuthootCLVerificationPending}

		var smsSentWG sync.WaitGroup
		smsErrorChan := make(chan error, len(postAggreementSMSTemplates))
		for _, smsTemplate := range postAggreementSMSTemplates {
			smsTemplate := smsTemplate
			var text string
			if smsTemplate == sms.CreditLineActivationAlertMFLDltDetails {
				shortURL, err := short.ShortenURL(pdfURL, userID, constants.MuthootCLID)
				if err != nil {
					errorHandler.ReportToSentryWithFields(map[string]interface{}{
						"userId":         userID,
						"sourceEntityID": sourceEntityID,
						"pdfurl":         pdfURL,
					}, errorspkg.New("error in shortening url"))
					logger.WithUser(userID).Error(err)
				}
				text = fmt.Sprintf(smsTemplate.DLTTemplateID, name, loanAmount, shortURL)
			} else if smsTemplate == sms.MuthootCLVerificationPending {
				text = fmt.Sprintf(smsTemplate.DLTTemplateID, name, fmt.Sprintf("%.2f", loanAmount))
			}
			smsSentWG.Add(1)
			go func() {
				defer errorHandler.RecoveryNoResponse()
				defer smsSentWG.Done()
				if mobile != "" {
					_, err := sms.SendSMSWithFallback(userID, mobile, text, smsTemplate, false)
					if err != nil {
						logger.WithUser(userID).Error(err)
						smsErrorChan <- err
					}
				}
			}()

		}

		smsSentWG.Wait()
		close(smsErrorChan)
		for err := range smsErrorChan {
			smsErr = err
		}

	} else if sourceEntityID == constants.KhatabookID {
		shortPDFURL, err := short.ShortenURL(pdfURL, userID, "")
		if err != nil {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userId":         userID,
				"sourceEntityID": sourceEntityID,
				"pdfurl":         pdfURL,
			}, errorspkg.New("error in shortening url"))
			return fmt.Errorf("unable to send sms for userID: %s with error: %s", userID, err.Error())
		}
		text = fmt.Sprintf("Hi %s, thank you for choosing %s! You can download a signed copy of the agreement with ref. Loan Application Number %s from: %s. -FinBox",
			name, lenderName, loanNo, shortPDFURL)
		dlteDetails = sms.AgreementDltDetails
		sent, smsErr = sms.SendSMSWithFallback(userID, mobile, text, dlteDetails, false)
	} else if lenderID == constants.ABFLPLID {
		postAggreementSMSTemplates := sms.ABFLPLAggrementMsgDetails
		shortURL, err := short.ShortenURL(pdfURL, userID, "")
		if err != nil {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userId":         userID,
				"sourceEntityID": sourceEntityID,
				"pdfurl":         pdfURL,
			}, errorspkg.New("error in shortening url"))
			logger.WithUser(userID).Error(err)
			return err
		}
		text = fmt.Sprintf(postAggreementSMSTemplates.DLTTemplateID, shortURL)

		sent, smsErr = sms.SendSMSWithFallback(userID, mobile, text, postAggreementSMSTemplates, false)
	}

	if smsErr != nil {
		logger.WithUser(userID).Errorln(smsErr)
		return smsErr
	} else if !sent {
		err := fmt.Errorf("unable to send SMS to mobile: %s of user: %s", mobile, userID)
		logger.WithUser(userID).Errorln(err)
		return err
	}
	return nil
}

func SaveAndSendEmailToUserFormintifi(ctx context.Context, userID, lenderID, loanApplicationID, sourceEntityID string) error {
	agreementObj := agreementutils.AgreementDBDataStruct{}
	agreementQuery := `select u.user_id, to_char(u.dob, 'yyyy-mm-dd') as dob, u.mobile, u.pan, u.name,
							coalesce(u.gender, -1) as gender, coalesce(u.email, '') as email, u.unique_id,
							a.amount, a.processing_fee, a.interest, a.gst, a.tenure,
							a.loan_application_no, a.lender_id, a.source_entity_id, coalesce(a.kyc_zip_path, '') as kyc_zip_path,
							a.loan_type,
							a.signed_agreement_template as signedagreementtemplate,
							a.unsigned_agreement_template as unsignedagreementtemplate,
							d.current_address, coalesce(d.fathers_name, '') as fathers_name,
							coalesce(d.marital_status, 0) as marital_status, coalesce(d.residence_type, 0) as residence_type,
							coalesce(d.loan_purpose, '') loan_purpose,
							coalesce(d.salary, 0) as salary, coalesce(d.income, 0) as income,
							coalesce(m.path, '') as sign_path,
							coalesce(d.pre_loan_data::TEXT, '') as preloandata,
							coalesce(a.sign_ip_address, '') as ipaddress,
							coalesce(a.sign_lat, '') as lat,
							coalesce(a.sign_lon, '') as lon,
							o.method,
							coalesce(to_char(a.disbursed_date, 'YYYY-MM-DD HH24:MI:SS'), '') as disburseddate
						from users u
						join loan_application a on u.user_id = a.user_id
						join user_loan_details d on  d.loan_application_id = a.loan_application_id
						join loan_offer o on a.loan_offer_id = o.loan_offer_id
						left join media m on a.sign_media_id = m.media_id
						where a.loan_application_id = $1`
	err := database.Get(&agreementObj, agreementQuery, loanApplicationID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error("failed to get signed agreement error: ", err)
		return err
	}
	lenderObj, err := lender.Get(lenderID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error("failed to get lender object", err)
		return err
	}
	// now fetch source entity information
	sourceEntityObj, err := sourceentitymodel.FetchSourceEntityInfo(sourceEntityID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error("failed to get souce entity object", err)
		return err
	}
	docURL, err := mintifi.GetSignedDocument(ctx, userID, sourceEntityID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error("failed to get signed agreement", err)
		return err
	}
	agreementKey := fmt.Sprintf("%s/%s.pdf", userID, loanApplicationID)
	_, success := s3.ReadFromURLAndUploadFileS3(docURL, agreementKey)
	if !success {
		logger.WithLoanApplication(loanApplicationID).Error("error uploading file to s3")
		return err
	}
	pdfURL := s3.GetPresignedURLS3(agreementKey, 300)
	paymentDetailObj := PaymentDetailStruct{
		DisbursalAmount: agreementObj.Amount,
	}
	query := `	update loan_application set
			updated_at = current_timestamp, sign_url = $1
			where loan_application_id = $2`

	_, err = database.ExecContext(ctx, query, agreementKey, loanApplicationID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error("unable to update loan_application details: ", err.Error())
		panic(err)
	}

	_, errStr := SendEmailToUser(agreementObj, lenderObj.LenderName, sourceEntityObj.SourceEntityName,
		pdfURL, paymentDetailObj)
	if errStr != "" {
		logger.WithLoanApplication(loanApplicationID).Error("failed to send agreement to user", err)
		return err
	}
	return nil
}

func GenerateTCAPKYCDocuments(loanApplicationID string, approvedDate time.Time, forceUpdateNameInAgreement bool, name string, ipAddress string) error {

	tcapKYCDocs := []string{"tcap_loan_agreement_", "tcap_loan_details_", "tcap_loan_application_", "tcap_derived_variables_"}
	obj, err := agreementutils.GetAgreementDataValues(loanApplicationID, forceUpdateNameInAgreement, name)
	if err != nil {
		err := fmt.Errorf("signed agreement failed, error: %s", err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return err
	}

	loc, _ := time.LoadLocation("Asia/Calcutta")
	premium := 0.0
	gst := 0.0
	emi, _, _ := calc.GetEMI(obj.Method, obj.Amount, obj.Tenure, obj.Interest, approvedDate, obj.SourceEntityID, obj.LenderID, obj.UserID)
	offer := personalloanoffer.GetAcceptedOffer(obj.UserID)
	advanceEMI := offer.OfferMetadataObj.BPI
	disbursalAmount := calc.CalculateDisbursalAmount(obj.Amount, obj.ProcessingFee, gst, advanceEMI, premium, commonutils.OtherCharges(obj.LenderID, obj.UserID), "")

	dynamicVars, err := lendervariables.GetDynamicVariables(obj.UserID, constants.TataCapitalID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error(err)
		return err
	}

	dynamicUserInfo, err := users.GetDynamicUserInfoMap(obj.UserID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return err
	}
	var additionalInfo structs.AdditionalInfo
	additionalInfoObj := dynamicUserInfo["additionalInfo"]
	b, err := json.Marshal(additionalInfoObj)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return err
	}
	additionalInfo.OfficeAddress.City, additionalInfo.OfficeAddress.State = pincodeapi.GetCityState(additionalInfo.OfficeAddress.Pincode)
	err = json.Unmarshal(b, &additionalInfo)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return err
	}
	deviceConnectData, err := deviceconnectdetails.GetLatestObjByUserAndStatus(obj.UserID, constants.DeviceConnectStatusCompleted)
	if err != nil && err != sql.ErrNoRows {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return err
	}
	emiEndDate, err := time.Parse("02/01/2006", offer.OfferMetadataObj.FirstEMIDueDate)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return err
	}
	emiEndDateStr := emiEndDate.AddDate(0, obj.Tenure, 0).Format("02/01/2006")
	placeholderData := map[string]interface{}{
		"SignDate":                  approvedDate.Format("02/01/2006"),
		"SignDateTime":              approvedDate.In(loc).Format("2006-01-02T15:04:05.003 PM"),
		"Mobile":                    obj.Mobile,
		"IPAddress":                 ipAddress,
		"LoanAmount":                fmt.Sprintf("%0.2f", obj.Amount),
		"LoanAmountWords":           general.AmountInWords(obj.Amount),
		"DisbursalAmount":           disbursalAmount,
		"Tenure":                    obj.Tenure,
		"TenureWords":               general.Spell(obj.Tenure),
		"InterestRate":              obj.Interest,
		"ProcessingFeePlusGST":      obj.ProcessingFee,
		"ProcessingFeePlusGSTWords": general.AmountInWords(obj.ProcessingFee),
		"BPI":                       offer.OfferMetadataObj.BPI,
		"APR":                       offer.OfferMetadataObj.APR,
		"BPIWords":                  general.AmountInWords(offer.OfferMetadataObj.BPI),
		"InsurancePremiumPlusGST":   0.0,
		"StampDuty":                 0.0,
		"TotalOtherCharges":         offer.OfferMetadataObj.BPI, // + insurance + stamp duty
		"EmiStartDate":              offer.OfferMetadataObj.FirstEMIDueDate,
		"EmiEndDate":                emiEndDateStr,
		"EMIDueDayString":           "5th of every month",                             //TODO
		"PlaceOfExecution":          dynamicUserInfo["country_derived_from_lat_long"], //TODO
		"EmiAmount":                 emi,
		"Gender":                    obj.Gender,
		"UserName":                  obj.Name,
		"FathersName":               dynamicVars["fathersName"],
		"MothersName":               dynamicVars["mothersName"],
		"DOB":                       obj.DOB,
		"OfficeAddress":             additionalInfo.OfficeAddress,
		"AdditionalInfo":            additionalInfo,
		"EmploymentType":            dynamicUserInfo["occupation"],
		"CompanyName":               dynamicUserInfo["company_name"],
		"Pincode":                   dynamicUserInfo["user_declared_pincode"],
		"Email":                     dynamicUserInfo["company_email"],
		"PersonalEmail":             obj.Email,
		"CustomerHash":              obj.UniqueID,
		"LeadID":                    dynamicVars["plLeadId"],
		"WebTopID":                  dynamicVars["plWebtopId"],
		"OpportunityID":             dynamicVars["plOpportunityId"],
		"Variables":                 deviceConnectData.Data,
	}

	err = tatacapital.CreateUploadDocument(obj.UserID, loanApplicationID, obj.LoanApplicationNo, tcapKYCDocs, placeholderData, obj.Email)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error(err)
	}
	err = tatacapital.UploadKycDocumentsCustom(context.TODO(), obj.UserID, loanApplicationID, constants.TataPLID, obj.LoanApplicationNo, dynamicVars["plOpportunityId"].(string), dynamicVars["plLeadId"].(string), obj.UniqueID,
		"", []string{constants.DocTypeCustomLoanAgreement, constants.DocTypeCustomLoanApplication, constants.DocTypeCustomLoanDetails})
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error(err)
	}
	err = tatacapital.UploadKycDocumentsCustom(context.TODO(), obj.UserID, loanApplicationID, constants.TataPLID, obj.LoanApplicationNo, dynamicVars["plOpportunityId"].(string), dynamicVars["plWebtopId"].(string), obj.UniqueID,
		"MID", []string{constants.DocTypeCustomLoanAgreement, constants.DocTypeCustomLoanDetails})
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error(err)
	}

	return nil
}

func GenerateSanctionLetterPFL(loanApplicationID, userID, sourceEntityID, lenderID, backdatedDate string) (string, error) {

	obj, err := agreementutils.GetAgreementDataValues(loanApplicationID, false, "")
	if err != nil {
		err := fmt.Errorf("saction data fetch failed for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	var dynamicUserInfo map[string]interface{}
	var maxEMI float64
	err = json.Unmarshal([]byte(obj.DynamicUserInfo), &dynamicUserInfo)
	if err != nil {
		err := fmt.Errorf("unable to unmarshal dynamic user info for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	countryOfStudy, _ := dynamicUserInfo["course_details_country"].(string)
	courseDetails, _ := dynamicUserInfo["course_details_course_type"].(string)
	universityName, _ := dynamicUserInfo["university"].(string)
	score, _ := dynamicUserInfo["gmat_score"].(string)
	if score == "" {
		score, _ = dynamicUserInfo["gre_score"].(string)
	}
	coUserName := ""
	coApps, _ := multiuserloanrelations.GetCoApplicantUserDetails(userID, true)
	if len(coApps) > 0 {
		coUserName = coApps[0].Name
	}

	var addressMap map[string]any
	permanentAddress, err := populateAddress(loanApplicationID, obj.PermanentAddress, addressMap)
	if err != nil {
		err := fmt.Errorf("unable to populate address for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}

	if backdatedDate == "" {
		backdatedDate = time.Now().Format("02 Jan 2006")
	}

	offerMetaData := personalloanoffer.OfferMetadata{}

	err = json.Unmarshal([]byte(obj.OfferMetadata), &offerMetaData)
	if err != nil {
		err := fmt.Errorf("unable to unmarshal offer_metadata for loanApplicationID: %s, error : %s", loanApplicationID, err)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}

	var pmiAmount float64
	if len(offerMetaData.Offers) > 0 {
		pmiAmount = offerMetaData.Offers[0].PartialMonthlyInterest
		maxEMI = offerMetaData.Offers[0].MaxEMI
	}

	placeholderData := map[string]interface{}{
		"loanAmountText":    journey.AmountToString(obj.LenderID, obj.Amount),
		"LoanAmount":        obj.Amount,
		"AmountInWords":     general.AmountInWords(obj.Amount),
		"loanApplicationNo": obj.LoanApplicationNo,
		"roi":               obj.Interest,
		"roiPercentage":     fmt.Sprintf("%.2f", obj.Interest),
		"ProcessingFee":     journey.AmountToString(obj.LenderID, obj.ProcessingFee),
		"loanType":          "Education Loan",
		"Purpose":           obj.LoanPurpose,
		"loanTenure":        obj.Tenure,
		"UserN":             obj.Name,
		"permanentAddress":  permanentAddress,
		"CountyOfStudy":     countryOfStudy,
		"CourseDetails":     courseDetails,
		"UniversityName":    universityName,
		"Score":             score,
		"CoUser":            coUserName,
		"CurrentAddress":    permanentAddress,
		"PmiAmount":         pmiAmount,
		"UserName":          obj.Name,
		"CurrentDate":       backdatedDate,
		"EmiAmount":         maxEMI,
	}

	sactionLetterPath := "templates/sanctionLetterPFL.html"
	buf := new(bytes.Buffer)
	ioRPointer := s3.GetFileStream(sactionLetterPath)
	if ioRPointer == nil {
		err := fmt.Errorf("file not found in s3 for loanApplicationID: %s and path: %s", loanApplicationID, sactionLetterPath)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", err
	}
	ioR := *ioRPointer
	buf.ReadFrom(ioR)
	templateString := buf.String()

	htmlBody := general.GetStringFromTemplate(string(templateString), placeholderData)
	// upload html to s3
	objectKey := userID + "/" + "sanction_letter_" + loanApplicationID + ".html"
	_, flag := s3.UploadRawFileS3(strings.NewReader(htmlBody), objectKey)
	if !flag {
		err := fmt.Errorf("unable to upload sanction letter to s3 for loanApplicationID: %s", loanApplicationID)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", err
	}
	return objectKey, nil
}

// GenerateSanctionLetterPreSignedURL generates presigned url for the sanction letter
func GenerateSanctionLetterPreSignedURL(loanApplicationID, userID, sourceEntityID, lenderID, backdatedDate string) (string, error) {
	objectKey, err := GenerateSanctionLetterPFL(loanApplicationID, userID, sourceEntityID, lenderID, backdatedDate)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", err
	}
	pdfObjectKey := userID + "/" + "sanction_letter_" + loanApplicationID + ".pdf"
	fileConvert := convert.HTMLToPDF(objectKey, pdfObjectKey)
	if !fileConvert {
		err := fmt.Errorf("PDF Conversion failed for loanApplicationID: %s", loanApplicationID)
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", err
	}
	return s3.GetPresignedURLS3(pdfObjectKey, 300), nil
}

func GenerateConsentPDF(userID, mobile, email, currentDate string) (string, error) {
	// get current data
	if currentDate == "" {
		// get current datetime
		currentDateTime := time.Now()
		currentDate = currentDateTime.Format("2006-01-02 15:04:05")
	}
	placeholderData := map[string]interface{}{
		"mobile": mobile,
		"date":   currentDate,
	}

	consentPFLPath := "templates/consentPFL.html"
	buf := new(bytes.Buffer)
	ioRPointer := s3.GetFileStream(consentPFLPath)
	if ioRPointer == nil {
		err := fmt.Errorf("file not found in s3 for userID: %s and path: %s", userID, consentPFLPath)
		return "", err
	}
	ioR := *ioRPointer
	buf.ReadFrom(ioR)
	templateString := buf.String()

	htmlBody := general.GetStringFromTemplate(string(templateString), placeholderData)
	// upload html to s3
	objectKey := userID + "/" + "consent_pfl_" + userID + ".html"
	_, flag := s3.UploadRawFileS3(strings.NewReader(htmlBody), objectKey)
	if !flag {
		err := fmt.Errorf("unable to upload sanction letter to s3 for userID: %s", userID)
		return "", err
	}
	pdfObjectKey := userID + "/" + "consent_pfl_" + userID + ".pdf"
	fileConvert := convert.HTMLToPDF(objectKey, pdfObjectKey)
	if !fileConvert {
		err := fmt.Errorf("PDF Conversion failed for userID: %s", userID)
		logger.WithLoanApplication(userID).Errorln(err)
		return "", err
	}

	mediaType := constants.MediaTypeConsentPDF
	mediaID := general.GetUUID()
	mediaObj := media.UploadMediaDataStruct{
		UserID:    userID,
		Path:      pdfObjectKey,
		MediaID:   mediaID,
		MediaType: mediaType,
	}
	err := media.Insert(nil, context.TODO(), mediaObj, email)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return "", err
	}
	return objectKey, nil
}
