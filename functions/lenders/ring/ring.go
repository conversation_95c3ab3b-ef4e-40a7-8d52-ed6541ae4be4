package ring

import (
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/workflowutils"
	"fmt"
	"strings"
)

func ProcessCallBack(callbackDtls lenderservice.CallbackReq) error {
	serviceName := callbackDtls.Type
	callBackReqData := callbackDtls.Data
	userID := callbackDtls.ApplicationReq.UserID
	sourceEntityID := callbackDtls.ApplicationReq.SourceEntityID
	callBackReq, err := json.Marshal(callBackReqData)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}
	loanApplication, err := loanapplication.GetLatestByUserAndLender(userID, constants.RingMCID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithUser(userID).Error(err)
		return err
	}

	if err == sql.ErrNoRows {
		customErr := errors.New("no loan application found")
		logger.WithUser(userID).Error(customErr)
		return customErr
	}
	if loanApplication.LenderID != constants.RingMCID {
		err = fmt.Errorf(constants.NoLoanActiveForUser)
		logger.WithUser(userID).Error(err)
		return err
	}
	if general.InArr(loanApplication.Status, []int{constants.LoanStatusCancelled, constants.LoanStatusClosed, constants.LoanStatusLoanRejected}) {
		customErr := errors.New(constants.ErrorLoanApplicationInTerminalState)
		logger.WithUser(userID).Error(customErr)
		return customErr
	}

	switch strings.ToUpper(serviceName) {
	case constants.ApplicationStatusService:
		var appStatus lenderservice.ApplicationStatusRes
		err = json.Unmarshal(callBackReq, &appStatus)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		module, err := workflowutils.GetCurrentModule(userID, sourceEntityID, loanApplication.ID.String())
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		loanApplicationID := loanApplication.ID.String()
		status := constants.ApplicationStatusToLoanStatusMap[appStatus.ApplicationStatus]
		subStatus := appStatus.ApplicationSubStatus
		eventType := appStatus.ActivityLog
		var kycStatus int
		toUpdateKYC := false
		if appStatus.Response.Message != "" {
			kycStatus = constants.KYCstringToIntMap[appStatus.Response.Message]
			toUpdateKYC = true

		}
		var loanObj loanapplication.StructForSet
		var offer lenderservice.Offer
		if appStatus.AdditionalData != nil {
			offerByte, err := json.Marshal(appStatus.AdditionalData)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}

			err = json.Unmarshal(offerByte, &offer)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			loanObj = loanapplication.StructForSet{
				ID:            loanApplicationID,
				Amount:        offer.Amount,
				Tenure:        offer.Tenure,
				Interest:      &offer.Roi,
				ProcessingFee: &offer.ProcessingFee,
			}
		}
		currentModule := module.Module.ModuleName
		dateTimeNowString := general.GetTimeStampString()
		var activityObj activity.ActivityEvent
		description := fmt.Sprintf(`{"lender":"%s","eventType":"%s"}`, constants.LenderNamesMap[constants.RingMCID], subStatus)
		if appStatus.LenderData != nil {
			type LenderData struct {
				KYCMode string `json:"kyc_mode"`
			}
			offerByte, err := json.Marshal(appStatus.LenderData)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}

			var lenderData LenderData
			err = json.Unmarshal(offerByte, &lenderData)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			if lenderData.KYCMode != "" {
				description += " KYC Mode: " + lenderData.KYCMode
			}
		}
		switch currentModule {
		case usermodulemapping.Redirection, usermodulemapping.OfferSelection:
			switch status {
			case constants.LoanStatusDetails, constants.LoanStatusBankAdded, constants.LoanStatusESign, constants.LoanStatusClosed, constants.LoanStatusFresh, constants.LoanStatusLoanApproved:
				loanObj.ID = loanApplicationID
				loanObj.Status = &status
				if toUpdateKYC {
					loanObj.KYCStatus = &kycStatus
				}
				dateTimeNowString = general.GetTimeStampString()
				activityObj = activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: loanApplicationID,
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         userID,
					EventType:         eventType,
					Description:       description,
				}

			case constants.LoanStatusLoanRejected:
				if loanApplication.Status == constants.LoanStatusLoanRejected {
					logger.WithUser(userID).Info("Loan is already rejected. Skipping update.")
					return nil // No action needed
				}
				_, err = underwriting.RejectLoan(loanApplicationID, constants.RingMCID, constants.EntityTypeSystem, "", description, underwriting.LoanRejectionDetails{})
				if err != nil {
					logger.WithUser(userID).Error(err)
					return err
				}
				logger.WithUser(userID).Info("Loan has been successfully rejected.")
				return nil
			case constants.LoanStatusDisbursed:
				MetadataMap := loanApplication.GetMetadataMap()
				MetadataMap["EMI"] = offer.MaxEmi
				MetadataMap["FirstEmiDate"] = offer.FirstEmiDate

				metadataBytes, err := json.Marshal(MetadataMap)
				if err != nil {
					logger.WithUser(userID).Error("Failed to marshal metadatamap", err)
					return err
				}

				// handle old cases
				dateTimeNowString = general.GetTimeStampString()
				loanObj = loanapplication.StructForSet{
					ID:                loanApplicationID,
					Status:            &status,
					DisbursedDate:     dateTimeNowString,
					Interest:          &offer.InterestRate,
					Tenure:            offer.Tenure,
					Amount:            offer.Amount,
					EMI:               offer.MaxEmi,
					DisbursedAmount:   offer.TotalAmount,
					LoanApplicationNo: appStatus.ApplicationID,
					Metadata:          sql.NullString{String: string(metadataBytes), Valid: true},
				}

				// added to handle old cases where FBX loanApplication has already been moved to old_loan_application_no
				if strings.Contains(loanApplication.LoanApplicationNo, "FBMC") {
					loanObj.OldApplicationNo = loanApplication.LoanApplicationNo
				}

				activityObj = activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: loanApplicationID,
					EntityType:        constants.System,
					EntityRef:         userID,
					EventType:         eventType,
					Description:       description,
				}

				err = usermodulemapping.Create(nil, userID, userID, usermodulemapping.Disbursal, constants.UserModuleStatusCompleted, loanApplicationID)
				if err != nil {
					logger.WithUser(userID).Error(err)
					errorHandler.ReportToSentryWithFields(map[string]interface{}{"loanApplicationID": loanApplication.ID.String()}, err)

				}
			default:
				custErr := fmt.Errorf("unexpected Status from Ring")
				logger.WithUser(userID).Error(custErr)
				return custErr
			}

			if err := loanapplication.Update(nil, loanObj); err != nil {
				logger.WithUser(loanObj.UserID).Error(err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{"loanApplicationID": loanApplication.ID.String()}, err)
				return err
			}

			go func() {
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
		}

	}
	return nil
}
