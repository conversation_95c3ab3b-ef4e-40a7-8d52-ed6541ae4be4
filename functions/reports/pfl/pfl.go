package pfl

import (
	"context"
	"encoding/csv"
	"encoding/json"

	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"

	"finbox/go-api/models/exportlogs"
	"finbox/go-api/utils/dashboardutils"
	export "finbox/go-api/utils/exportutils"

	"finbox/go-api/utils/lockutils"
	"finbox/go-api/utils/mask"
	"fmt"
)

func GeneratePFLUtmReport(ctx context.Context, payload PflUtmReport) error {
	defer func() {
		errorHandler.RecoveryNoResponse()
		lockutils.UnLock(constants.PFLUtmReport, nil)
	}()

	exportID, csvFile, err := export.CreateExportLog(payload.UserEmail, payload.Attributes, "PFL UTM Report", exportlogs.DashboardTypePlatform, payload.UserEmail, payload.UserObj.MasterUserID, "csv")
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return err
	}

	sourceEntityID := constants.PoonawallaFincorpID
	defer func() {
		if err := csvFile.Close(); err != nil {
			logger.WithContext(ctx).Errorln(err)
		}
	}()
	ctx, cancel := context.WithTimeout(context.Background(), constants.QueryTimeoutExports)
	defer cancel()
	// now get actual records
	pwUTMReport, err := export.PFLUTMReport(ctx, payload.Filter, payload.FromDate, payload.ToDate, sourceEntityID)

	if err != nil && errorHandler.IsContextCancelledError(err) {
		logger.WithContext(ctx).Errorln(err)
		exportLogsErr := exportlogs.Update(exportlogs.StatusFailed, "operation timed out, please try with a smaller time range", "", exportID)
		if exportLogsErr != nil {
			logger.WithContext(ctx).Errorln(exportLogsErr)
			return exportLogsErr
		}
		return err
	}

	csvWriter := csv.NewWriter(csvFile)
	//writing headers
	headers := []string{
		"lead_id", "customer_id", "source", "status", "user_created_at", "loan_application_no", "loan_status", "application_created_at", "application_updated_at", "sign_agreement_date", "disbursed_or_activation_date"}

	maskInfo := dashboardutils.GetMaskingInfo(&payload.UserObj, sourceEntityID)

	dynamicHeaders := make(map[string]bool)
	rows := []map[string]string{}

	for index := range pwUTMReport {
		rowPtr := &pwUTMReport[index]

		err = mask.PIIData(rowPtr, maskInfo.Export)
		if err != nil {
			panic(err)
		}

		row := map[string]string{}
		row["lead_id"] = pwUTMReport[index].LeadID
		row["customer_id"] = pwUTMReport[index].CustomerID
		row["source"] = pwUTMReport[index].Source
		row["status"] = constants.UserStatusNumToStr[pwUTMReport[index].Status]
		row["user_created_at"] = pwUTMReport[index].UserCreatedAt
		row["loan_application_no"] = pwUTMReport[index].LoanApplicationNo
		row["loan_status"] = constants.GetLoanStatusText(pwUTMReport[index].LoanStatus, pwUTMReport[index].LoanKYCStatus)
		row["application_created_at"] = pwUTMReport[index].ApplicationCreatedAt
		row["application_updated_at"] = pwUTMReport[index].ApplicationUpdatedAt
		row["sign_agreement_date"] = pwUTMReport[index].SignAgreementDate
		row["disbursed_or_activation_date"] = pwUTMReport[index].DisbursedOrActivationDate

		if len(rowPtr.CampaignParams) > 0 {
			campaignParams := make(map[string]interface{})
			err := json.Unmarshal([]byte(rowPtr.CampaignParams), &campaignParams)
			if err != nil {
				logger.WithContext(ctx).Errorln(err)
				return err
			}
			for key, value := range campaignParams {
				dynamicHeaders[key] = true
				row[key] = fmt.Sprintf("%v", value)
			}
		}
		rows = append(rows, row)

	}
	for header := range dynamicHeaders {
		headers = append(headers, header)
	}
	err = csvWriter.Write(headers)
	if err != nil {
		logger.WithContext(ctx).Errorln("failed to write CSV headers:", err)
		return err
	}
	for _, row := range rows {
		t := []string{}
		for _, header := range headers {
			t = append(t, row[header])
		}
		err = csvWriter.Write(t)
		if err != nil {
			logger.WithContext(ctx).Errorln(err)
			return err
		}
	}

	csvWriter.Flush()
	err = csvWriter.Error()
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return err
	}
	err = export.SendExportMail(exportID, constants.LenderNamesMap[constants.PoonawallaFincorpID], "PFL UTM Report", payload.FromDate, payload.ToDate, payload.Filter, payload.UserEmail, "csv")
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return err
	}

	logger.WithContext(ctx).Infof("[GeneratePFLUtmReport] GeneratePFLUtmReport complete")

	return nil

}
