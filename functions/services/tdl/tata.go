package tdl

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/async/producer"
	"finbox/go-api/authentication"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/functionalityModels"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/lenders/hdfctdl"
	"finbox/go-api/models/expiry"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/userworkflows"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/serviceslib"
	"finbox/go-api/functions/structs"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/infra/db"
	"finbox/go-api/models/companydetails"
	"finbox/go-api/models/surveyconfig"
	"finbox/go-api/models/users"
	"finbox/go-api/models/usersurveyresponse"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/journeyutils"
	"finbox/go-api/utils/surveyutils"
	"finbox/go-api/utils/workflowutils"

	"github.com/hibiken/asynq"
)

var log = logger.Log
var database = db.GetDB()

type CustomerDetail struct {
	NameAsPerPan            string        `json:"nameAsPerPan" db:"name_as_per_pan"`
	CustomerID              string        `json:"customerId" db:"unique_id"`
	Pan                     string        `json:"pan"`
	Dob                     string        `json:"dob"`
	FirstName               string        `json:"firstName" db:"first_name"`
	MiddleName              string        `json:"middleName" db:"middle_name"`
	LastName                string        `json:"lastName" db:"last_name"`
	Gender                  string        `json:"gender"`
	Pin                     string        `json:"pin" db:"pincode"`
	LatLongPincode          string        `json:"latLongPincode" db:"pincode_derived_from_lat_long"`
	Email                   string        `json:"email"`
	Occupation              string        `json:"occupation"`
	MonthlySalary           float64       `json:"monthlySalary" db:"monthly_income"`
	Purpose                 string        `json:"purpose" db:"loan_purpose"`
	CompanyCodeString       string        `json:"-" db:"company_code"`
	CompanyCode             int64         `json:"companyCode"`
	KBCompanyCode           string        `json:"kbCompanyCode"`
	DeviceID                string        `json:"deviceId" db:"device_id"`
	Platform                string        `json:"platform"`
	CompanyEmail            string        `json:"companyEmail" db:"company_email"`
	FinBoxEligibilityStatus string        `json:"finBoxEligibilityStatus"`
	RejectionReason         string        `json:"rejectionReason"`
	ConsentData             []ConsentData `json:"consentData"`
	CompanyName             string        `json:"companyName" db:"company_name"`
	Source                  string        `json:"-" db:"source"`
}

type CustomerDetailBNPL struct {
	CustomerID     string  `json:"customerId" db:"unique_id"`
	PAN            string  `json:"pan"`
	DOB            string  `json:"dob"`
	FirstName      string  `json:"firstName" db:"first_name"`
	MiddleName     string  `json:"middleName" db:"middle_name"`
	LastName       string  `json:"lastName" db:"last_name"`
	Gender         string  `json:"gender"`
	Pin            string  `json:"pin" db:"pincode"`
	Email          string  `json:"email"`
	EmploymentType string  `json:"employmentType"`
	MonthlyIncome  float64 `json:"monthlyIncome" db:"monthly_income"`
	Source         string  `json:"source"`
	LatLongPincode string  `json:"latLongPincode" db:"pincode_derived_from_lat_long"`
}

type ConsentData struct {
	ConsentTimestamp string `json:"consentTimestamp"`
	ConsentType      string `json:"consentType"`
	DeviceID         string `json:"deviceId"`
	IPAddress        string `json:"ipAddress"`
	Version          string `json:"version"`
}

type CustomerDetailTDLAPIResp struct {
	Status string `json:"status"`
	Data   struct {
		IsFraud         bool   `json:"isFraud"`
		RejectionReason string `json:"rejectionReason"`
	} `json:"data"`
	Errors []struct {
		ErrorCode string `json:"errorCode"`
		ErrorType string `json:"errorType"`
		ErrorMsg  string `json:"errorMsg"`
	} `json:"errors"`
}

type ReferralResponse struct {
	Status    bool        `json:"status"`
	Data      interface{} `json:"data"`
	ErrorInfo struct {
		ErrorCode    string `json:"errorCode"`
		ErrorMessage string `json:"errorMessage"`
	} `json:"errorInfo"`
}

var errTDLAPI = errors.New("error calling tdl customer detail service")
var ErrMarshal = errors.New("error marshalling customer detail for tdl api")
var ErrUnmarshal = errors.New("error in unmarshal tdl api response")
var errTDLConnect = errors.New("error connecting tdl service")
var errResponseRead = errors.New("error in reading tdl response")
var errIsFraud = errors.New("customer is fraud")

// PushCustomerDetailToTDL method calls TDL API to push send customer details and redirect it.
func PushCustomerDetailToTDL(userID, sourceEntityID string, ipAddress string) (CustomerDetailTDLAPIResp, error) {
	var custObj CustomerDetail
	query := `SELECT coalesce(u.dynamic_user_info::jsonb ->> 'company_name', '') AS company_name,
				coalesce(u.dynamic_user_info::jsonb ->> 'company_code', '') AS company_code,
				coalesce(u.dynamic_user_info::jsonb ->> 'company_email', '') AS company_email,
				coalesce(u.dynamic_user_info::jsonb ->> 'occupation', '') AS occupation,
				coalesce(u.dynamic_user_info::jsonb ->> 'monthly_income', '0') AS monthly_income,
				coalesce(u.dynamic_user_info::jsonb ->> 'loan_purpose', '') AS loan_purpose,
				coalesce(u.dynamic_user_info::jsonb ->> 'pincode_derived_from_lat_long','')AS pincode_derived_from_lat_long,
				coalesce(t.device_id, '') AS device_id,
				u.pan,
				to_char(u.dob, 'YYYY-MM-DD') as dob,
				u.unique_id,
				u.gender,
				u.pincode,
				u.email,
				coalesce(u.source, '') as source,
				coalesce(t.platform, 'ANDROID/IOS') AS platform,
				coalesce(u.dynamic_user_info::jsonb ->> 'first_name', '') AS first_name,
				coalesce(u.dynamic_user_info::jsonb ->> 'middle_name', '') AS middle_name,
				coalesce(u.dynamic_user_info::jsonb ->> 'last_name', '') AS last_name,
				pd.name AS name_as_per_pan
			FROM users u
			LEFT JOIN tdl_data t ON t.user_id = u.user_id
			LEFT JOIN pan_details pd ON pd.user_id = u.user_id
			WHERE u.user_id = $1
			AND u.source_entity_id = $2
			ORDER BY t.created_at DESC
			LIMIT 1;`
	err := database.Get(&custObj, query, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]any{
			"userID": userID,
		}, err)
		return CustomerDetailTDLAPIResp{}, err
	}

	custObj.DeviceID = general.Coalesce(general.RemoveExtraSpaces(custObj.DeviceID), "NA")
	custObj.Platform = general.Coalesce(general.RemoveExtraSpaces(custObj.Platform), "ANDROID/IOS")
	custObj.FinBoxEligibilityStatus = "APPROVED"

	kbCompanyCodes, err := companydetails.SearchCompanyName(custObj.CompanyName, 1, 0, "", constants.TDLKreditBeeID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
	}

	if len(kbCompanyCodes) == 0 {
		//assign default code
		logger.WithUser(userID).Infof("No KB company code found for %s, using default", custObj.CompanyName)
		custObj.KBCompanyCode = "-1"
	} else {
		custObj.KBCompanyCode = kbCompanyCodes[0].CompanyCode
	}

	if conf.ENV != conf.ENV_PROD {
		custObj.CompanyCodeString = "709530"
		custObj.CompanyName = "TATA CONSULTANCY SERVICES LIMITED"
	}

	// TODO : get consent from /submitUserDetails
	custObj.ConsentData = []ConsentData{
		{ConsentTimestamp: time.Now().Add(-time.Second * 78).Format(time.RFC3339),
			ConsentType: "BASIC_DETAILS",
			DeviceID:    custObj.DeviceID,
			IPAddress:   ipAddress,
			Version:     "V1",
		}, {
			ConsentTimestamp: time.Now().Format(time.RFC3339),
			ConsentType:      "BASIC_DETAILS2",
			DeviceID:         custObj.DeviceID,
			IPAddress:        ipAddress,
			Version:          "V1",
		},
	}

	if custObj.CompanyCodeString != "" {
		custObj.CompanyCode, err = strconv.ParseInt(custObj.CompanyCodeString, 10, 64)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithFields(map[string]any{
				"userID": userID,
			}, err)
			return CustomerDetailTDLAPIResp{}, err
		}
	}

	genderInt, err := strconv.Atoi(custObj.Gender)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]any{
			"userID": userID,
		}, err)
		return CustomerDetailTDLAPIResp{}, err
	}
	switch genderInt {
	case 0:
		custObj.Gender = "Female"
	case 1:
		custObj.Gender = "Male"
	case 2:
		custObj.Gender = "Others"
	default:
		custObj.Gender = "I'd rather not say"
	}

	var obj CustomerDetailTDLAPIResp
	switch sourceEntityID {
	case constants.TataPLID:
		var OccupationMap = map[string]string{
			"SALARIED":      "S",
			"SELF-EMPLOYED": "E",
		}
		custObj.Occupation = OccupationMap[custObj.Occupation]

		obj, err = callCustomerDetailsPL(custObj, sourceEntityID, userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return CustomerDetailTDLAPIResp{}, err
		}
		if obj.Status == "Failure" {
			if len(obj.Errors) > 0 {
				logger.WithUser(userID).Error(obj.Errors[0].ErrorMsg)
				return obj, errors.New(obj.Errors[0].ErrorMsg)
			} else {
				return obj, errIsFraud
			}

		} else if obj.Status == "Success" {
			if obj.Data.IsFraud {
				isDisqualified := usersutil.DisqualifyUser(userID, sourceEntityID, obj.Data.RejectionReason, "")
				if !isDisqualified {
					logger.WithUser(userID).Error(err)
				}
				return obj, errIsFraud
			}
		}
	case constants.TataBNPLID:
		custObjBNPL := CustomerDetailBNPL{
			CustomerID:     custObj.CustomerID,
			PAN:            custObj.Pan,
			DOB:            custObj.Dob,
			FirstName:      custObj.FirstName,
			MiddleName:     custObj.MiddleName,
			LastName:       custObj.LastName,
			Gender:         custObj.Gender,
			Pin:            custObj.Pin,
			Email:          custObj.Email,
			EmploymentType: custObj.Occupation,
			MonthlyIncome:  custObj.MonthlySalary,
			Source:         custObj.Source,
			LatLongPincode: custObj.LatLongPincode,
		}
		if obj, err = callCustomerDetailsBNPL(custObjBNPL, sourceEntityID, userID); err != nil {
			logger.WithUser(userID).Error(err)
			return CustomerDetailTDLAPIResp{}, err
		}
		if obj.Status == "Failure" {
			if len(obj.Errors) > 0 {
				logger.WithUser(userID).Error(obj.Errors[0].ErrorMsg)
				return obj, errors.New(obj.Errors[0].ErrorMsg)
			}

		} else if obj.Status == "Success" {
			if obj.Data.IsFraud {
				isDisqualified := usersutil.DisqualifyUser(userID, sourceEntityID, obj.Data.RejectionReason, "")
				if !isDisqualified {
					logger.WithUser(userID).Error(err)
				}
				return obj, errIsFraud
			}
		}
		request := map[string]interface{}{
			"customerHash": custObj.CustomerID,
		}

		_, name, err := getNameFromPAN(userID, request)
		if err != nil {
			logger.WithUser(userID).Error(err)
		}
		if general.IsValidName(name) {
			err = users.UpdateDynamicUserInfoFieldV2(userID, "nameFromTDL", name, nil)
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithFields(map[string]any{
					"userID": userID,
				}, err)
			}
		}
	}

	return obj, nil
}

func callCustomerDetailsPL(custDetail CustomerDetail, sourceEntityID string, userID string) (CustomerDetailTDLAPIResp, error) {
	baseURL := conf.TDLCreds["baseURL"]
	clientID := conf.TDLCreds["clientID"]

	url := baseURL + "/api/v2/marketplacepartnercontroller/customer-details"

	method := http.MethodPost

	var tdlObj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     "",
	}

	var tdlAPIResp CustomerDetailTDLAPIResp
	payload, err := json.Marshal(custDetail)
	if err != nil {
		errObj := fmt.Errorf("incorrect payload for tdl api: %s", string(payload))
		logger.WithUser(userID).Errorln(errObj)
		errorHandler.ReportToSentryWithFields(map[string]any{
			"userID": userID,
		}, errObj)
		return tdlAPIResp, ErrMarshal
	}

	tdlObj["strReq"] = string(payload)

	payloadStrReader := strings.NewReader(string(payload))
	client := tracer.GetTraceableHTTPClient(nil, "customer-details-tdl")

	token, err := activity.RetrieveOauthTokenWithRedis(userID)
	if err != nil {
		logger.WithUser(userID).Errorln("error retrieving oauth token for tdl: ", err.Error())
		return CustomerDetailTDLAPIResp{}, err
	}
	err = retry.CustomRetry(3, 5*time.Second, func() error {
		tdlAPIResp = CustomerDetailTDLAPIResp{}

		tdlObj["id"] = general.GetUUID()

		req, err := requestutils.GetMockableHTTPRequest(userID, "customer-details-tdl", method, url, payloadStrReader)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDB("customer-details-tdl", tdlObj["strReq"], tdlObj["strRes"], 0, tdlObj["userID"], tdlObj["url"], err.Error(), tdlObj["id"])
			return errTDLAPI
		}
		req.Header.Add("client_id", clientID)
		req.Header.Add("Authorization", token)
		req.Header.Add("posAuthorization", "true")
		req.Header.Add("Content-Type", "application/json")

		res, err := client.Do(req)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDB("customer-details-tdl", tdlObj["strReq"], tdlObj["strRes"], 0, tdlObj["userID"], tdlObj["url"], err.Error(), tdlObj["id"])
			return errTDLConnect
		}
		defer res.Body.Close()

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDB("customer-details-tdl", tdlObj["strReq"], tdlObj["strRes"], 0, tdlObj["userID"], tdlObj["url"], err.Error(), tdlObj["id"])
			return errResponseRead
		}

		tdlObj["strRes"] = string(body)
		err = json.Unmarshal(body, &tdlAPIResp)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDB("customer-details-tdl", tdlObj["strReq"], tdlObj["strRes"], 0, tdlObj["userID"], tdlObj["url"], err.Error(), tdlObj["id"])
			return ErrUnmarshal
		}
		return nil
	})

	if err == nil {
		serviceslib.WriteToDB("customer-details-tdl", tdlObj["strReq"], tdlObj["strRes"], 1, tdlObj["userID"], tdlObj["url"], "", tdlObj["id"])
	}

	return tdlAPIResp, err
}

func callCustomerDetailsBNPL(custDetail CustomerDetailBNPL, sourceEntityID string, userID string) (CustomerDetailTDLAPIResp, error) {
	baseURL := conf.TataConfig["baseURL"]
	clientID := conf.TataConfig["ClientID"]
	authKey := conf.TataConfig["Authorization"]
	url := baseURL + "/v2/finbox/partner/bnpl/customer-details"

	method := http.MethodPost
	serviceName := "customer-details-tdl-bnpl"

	var tdlObj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     "",
	}
	var tdlAPIResp CustomerDetailTDLAPIResp

	var reqHeaders http.Header
	var respHeaders http.Header
	var httpStatusCode int
	var success int

	payload, err := json.Marshal(custDetail)
	if err != nil {
		errObj := fmt.Errorf("incorrect payload for tdl api: %s", string(payload))
		logger.WithUser(userID).Errorln(errObj)
		errorHandler.ReportToSentryWithFields(map[string]any{
			"userID": userID,
		}, errObj)
		return tdlAPIResp, err
	}

	tdlObj["strReq"] = string(payload)

	payloadStrReader := strings.NewReader(string(payload))
	client := tracer.GetTraceableHTTPClient(nil, serviceName)

	err = retry.CustomRetry(3, 5*time.Second, func() error {
		tdlObj["id"] = general.GetUUID()

		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, method, url, payloadStrReader)
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], userID, serviceName, url, tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return err
		}
		req.Header.Add("client_id", clientID)
		req.Header.Add("Authorization", authKey)
		req.Header.Add("Content-Type", "application/json")

		reqHeaders = req.Header

		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], userID, serviceName, url, tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return err
		}
		defer res.Body.Close()

		httpStatusCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], userID, serviceName, url, tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return err
		}

		tdlObj["strRes"] = string(body)
		respHeaders = res.Header
		err = json.Unmarshal(body, &tdlAPIResp)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		if res.StatusCode != http.StatusOK {
			err := fmt.Errorf("status code - %d", res.StatusCode)
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], userID, serviceName, url, tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return err
		}

		return nil
	})

	if err == nil {
		success = 1
		serviceslib.WriteToDBWithHeaders(tdlObj["id"], userID, serviceName, url, tdlObj["strReq"], tdlObj["strRes"], "", success, httpStatusCode, reqHeaders, respHeaders)
	}

	return tdlAPIResp, err
}

// RouteCustomerToTDL redirect customer to TDL
func RouteCustomerToTDL(w http.ResponseWriter, r *http.Request, url string) {
	http.Redirect(w, r, url, http.StatusSeeOther)
}

func ReferralWrapper(userID, uniqueID, sourceEntityID, productCode string) (*ReferralResponse, *structs.CustomError) {
	baseURL := conf.TDLCreds["baseURL"]
	clientID := conf.TDLCreds["clientID"]

	url := baseURL + "/api/FinBoxPartner/Controller/referral/fetch-referral-code"
	method := "GET"

	var tdlObj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     "",
	}

	serviceName := "referral-tdl"
	client := tracer.GetTraceableHTTPClient(nil, serviceName)

	var reqHeaders http.Header
	var respHeaders http.Header
	var httpStatusCode int
	var success int

	var tdlAPIResp ReferralResponse
	token, err := activity.RetrieveOauthTokenWithRedis(userID)
	if err != nil {
		logger.WithUser(userID).Errorln("error retrieving oauth token for tdl: ", err.Error())
		return &ReferralResponse{}, &structs.CustomError{}
	}
	// serviceslib.WriteToDBWithHeaders(gobj["id"], gobj["userID"], serviceName, gobj["url"], gobj["strReq"], gobj["strRes"], "", success, httpStatusCode, reqHeaders, respHeaders, programID)
	err = retry.CustomRetry(3, 5*time.Second, func() error {
		tdlAPIResp = ReferralResponse{}

		tdlObj["id"] = general.GetUUID()

		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, method, url, nil)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return errTDLAPI
		}

		queryParams := req.URL.Query()
		queryParams.Add("productCode", productCode)

		req.URL.RawQuery = queryParams.Encode()

		req.Header.Add("client_id", clientID)
		req.Header.Add("Authorization", token)
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("customerHash", uniqueID)
		req.Header.Add("posAuthorization", "true")

		reqHeaders = req.Header

		res, err := client.Do(req)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return errTDLConnect
		}
		defer res.Body.Close()

		httpStatusCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return errResponseRead
		}
		tdlObj["strRes"] = string(body)
		respHeaders = res.Header

		err = json.Unmarshal(body, &tdlAPIResp)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return ErrUnmarshal
		}
		return nil
	})

	if err != nil {
		logger.WithUser(userID).Error(err)
		return &tdlAPIResp, &structs.CustomError{HTTPCode: httpStatusCode, Err: err}
	}

	success = 1
	serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], "", success, httpStatusCode, reqHeaders, respHeaders)
	return &tdlAPIResp, nil
}

type TcapRoutingResponse struct {
	LenderID               string
	ToRejectUser           bool
	RejectReason           string
	IsRoutingAPICallFailed bool
	GatingApprovedLenders  []string // this list is a list of all lenders who were approved from the routing api
}

func TcapRouting(userObj users.User) (resp TcapRoutingResponse, err error) {
	conf := conf.TataConfig
	url := conf["baseURL"] + "/v2/marketplacepartnercontroller/fraud-check"
	var gobj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userObj.ID,
		"id":     general.GetUUID(),
	}

	request := map[string]interface{}{
		"customerId":   userObj.UniqueID,
		"customerHash": userObj.UniqueID,
		"pincode":      userObj.Pincode,
	}
	payload, err := json.Marshal(request)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return TcapRoutingResponse{IsRoutingAPICallFailed: true}, err
	}
	gobj["strReq"] = string(payload)

	serviceName := "lender_routing_api"
	var responseCode int

	client := tracer.GetTraceableHTTPClient(nil, serviceName)
	token, err := activity.RetrieveOauthTokenWithRedis(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln("error retrieving oauth token for tdl: ", err.Error())
		return TcapRoutingResponse{}, err
	}
	var lenderRoutingResp LenderRouting
	err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {
		lenderRoutingResp = LenderRouting{}

		req, err := requestutils.GetMockableHTTPRequest(userObj.ID, serviceName, http.MethodPost, url, bytes.NewReader(payload))
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			return err
		}
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Authorization", token)
		req.Header.Add("client_id", conf["ClientID"])
		req.Header.Add("posAuthorization", "true")

		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			return err
		}
		defer res.Body.Close()
		responseCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			return err
		}
		gobj["strRes"] = string(body)

		err = json.Unmarshal(body, &lenderRoutingResp)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			return err
		}
		return nil
	})

	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return TcapRoutingResponse{IsRoutingAPICallFailed: true}, err
	}

	if responseCode != 200 {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], "not 2xx series", gobj["id"])
		return TcapRoutingResponse{LenderID: constants.IIFLID, IsRoutingAPICallFailed: true}, errors.New("status code is not 200")
	}

	serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])

	if lenderRoutingResp.Data.IsRejected {
		return TcapRoutingResponse{
			LenderID:     "",
			ToRejectUser: true,
			RejectReason: lenderRoutingResp.Data.RejectReason,
		}, nil
	}

	if lenderRoutingResp.Data.TcapRouting == "yes" {
		return TcapRoutingResponse{
			LenderID:              constants.TataCapitalID,
			GatingApprovedLenders: getGatingApprovedLenders(lenderRoutingResp),
		}, nil
	} else {
		return TcapRoutingResponse{
			LenderID:              constants.IIFLID,
			GatingApprovedLenders: getGatingApprovedLenders(lenderRoutingResp),
		}, nil
	}
}

func PennyDrop(userID string, request map[string]interface{}) (PennydropResp, string, error) {

	conf := conf.TataConfig
	url := conf["baseURL"] + "/v1/marketplacepartnercontroller/penny-drop"
	var gobj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}
	var pennydropResp PennydropResp
	payload, err := json.Marshal(request)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return pennydropResp, "", err
	}
	gobj["strReq"] = string(payload)

	serviceName := "pennydrop_tdl"
	var responseCode int

	client := tracer.GetTraceableHTTPClient(nil, serviceName)
	token, err := activity.RetrieveOauthTokenWithRedis(userID)
	if err != nil {
		logger.WithUser(userID).Errorln("error retrieving oauth token for tdl: ", err.Error())
		return PennydropResp{}, "", err
	}
	err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {
		pennydropResp = PennydropResp{}

		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, http.MethodPost, url, bytes.NewReader(payload))
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Authorization", token)
		req.Header.Add("client_id", conf["ClientID"])
		req.Header.Add("posAuthorization", "true")

		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		defer res.Body.Close()
		responseCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		gobj["strRes"] = string(body)

		err = json.Unmarshal(body, &pennydropResp)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		return nil
	})

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return pennydropResp, gobj["id"], err
	}

	if responseCode != 200 {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], "not 2xx series", gobj["id"])
		return pennydropResp, gobj["id"], errors.New("status code is not 200")
	}

	serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])
	return pennydropResp, gobj["id"], nil
}

func NameMatch(userID string, request map[string]interface{}) (NameMatchResp, string, string, error) {

	conf := conf.TataConfig
	url := conf["baseURL"] + "/v1/marketplacepartnercontroller/name-match"
	var gobj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}
	var nameMatchResp NameMatchResp
	payload, err := json.Marshal(request)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nameMatchResp, "", "", err
	}
	gobj["strReq"] = string(payload)

	serviceName := "name_match_api"
	var responseCode int

	client := tracer.GetTraceableHTTPClient(nil, serviceName)
	token, err := activity.RetrieveOauthTokenWithRedis(userID)
	if err != nil {
		logger.WithUser(userID).Errorln("error retrieving oauth token for tdl: ", err.Error())
		return NameMatchResp{}, "", "", err
	}
	err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {
		nameMatchResp = NameMatchResp{}

		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, http.MethodPost, url, bytes.NewReader(payload))
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Authorization", token)
		req.Header.Add("client_id", conf["ClientID"])
		req.Header.Add("posAuthorization", "true")

		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		defer res.Body.Close()
		responseCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		gobj["strRes"] = string(body)

		err = json.Unmarshal(body, &nameMatchResp)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		return nil
	})

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return nameMatchResp, "", "", err
	}

	if responseCode != 200 {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], "not 2xx series", gobj["id"])
		return nameMatchResp, "status code is not 200", "", nil
	}

	if len(nameMatchResp.Errors) > 0 {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], nameMatchResp.Errors[0].ErrorMsg, gobj["id"])
		return nameMatchResp, nameMatchResp.Errors[0].ErrorMsg, nameMatchResp.Errors[0].ErrorCode, nil
	}
	serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])
	return nameMatchResp, "", "", nil
}

func ReverseGeolocation(userID string, request map[string]interface{}) (string, error) {

	conf := conf.TataConfig
	url := conf["baseURL"] + "/v1/marketplacepartnercontroller/geolocation-pdf"
	var gobj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}
	var resp GeolocationResp
	payload, err := json.Marshal(request)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return resp.Data.PdfBase64, err
	}
	gobj["strReq"] = string(payload)

	serviceName := "reverse_geolocation_api"
	var responseCode int

	client := tracer.GetTraceableHTTPClient(nil, serviceName)
	token, err := activity.RetrieveOauthTokenWithRedis(userID)
	if err != nil {
		logger.WithUser(userID).Errorln("error retrieving oauth token for tdl: ", err.Error())
		return "", err
	}
	err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {
		resp = GeolocationResp{}

		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, http.MethodPost, url, bytes.NewReader(payload))
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Authorization", token)
		req.Header.Add("client_id", conf["ClientID"])
		req.Header.Add("posAuthorization", "true")

		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		defer res.Body.Close()
		responseCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		gobj["strRes"] = string(body)

		err = json.Unmarshal(body, &resp)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		return nil
	})

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return resp.Data.PdfBase64, err
	}

	if responseCode != 200 {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], "not 2xx series", gobj["id"])
		return resp.Data.PdfBase64, errors.New("status code is not 200")
	}

	if len(resp.Errors) > 0 {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], resp.Errors[0].ErrorMsg, gobj["id"])
		return resp.Data.PdfBase64, errors.New(resp.Errors[0].ErrorMsg)
	}
	serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])
	return resp.Data.PdfBase64, nil
}

// ExitSurvey ... Fetches the required information and call the survey api of tdl
func ExitSurvey(form []usersurveyresponse.ResponseRow) error {

	for _, r := range form {
		userID := r.UserID
		formName, questionText, err := surveyutils.GetFormNameAndQuestionText(r.QuestionID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		var responseText string
		if r.ResponseID != "" {
			responseText, err = surveyconfig.GetTextFromID(r.ResponseID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
		}
		additionalResponseText := r.AdditionalResponseText

		users, err := users.Get(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		err = callSurveyAPI(formName, questionText, responseText, additionalResponseText, userID, users.UniqueID, users.SourceEntityID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	}
	return nil
}

// callSurveyAPI ... send the question and answer the user selected during survey.
func callSurveyAPI(formName, queText, resText, additionalResText, userID, uniqueID, sourceEntityID string) error {
	baseURL := conf.TDLCreds["baseURL"]
	clientID := conf.TDLCreds["clientID"]
	relativeURL := journey.GetRelativeURLToSubmitExitSurvey(sourceEntityID)

	url := baseURL + relativeURL
	method := "POST"

	var tdlObj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     "",
	}

	serviceName := "tdl-exit-survey"
	client := tracer.GetTraceableHTTPClient(nil, serviceName)

	var (
		authHeader string
		err        error
	)
	switch sourceEntityID {
	case constants.TataPLID:
		authHeader, err = activity.RetrieveOauthTokenWithRedis(userID)
		if err != nil {
			logger.WithUser(userID).Errorln("error retrieving oauth token for tdl: ", err.Error())
			return err
		}
	case constants.TataBNPLID:
		// update to Oauth for tdl api
		authHeader = conf.TataConfig["Authorization"]
	}

	var reqHeaders http.Header
	var respHeaders http.Header
	var httpStatusCode int
	var success int

	var payload ExitSurveyPayload

	payload.PageName = formName
	payload.Question = queText
	payload.Response = resText
	payload.Source = "FINBOX"
	payload.CustomerHash = uniqueID
	payload.AdditionalResponse = additionalResText

	if sourceEntityID == constants.TataBNPLID {
		payload.AdditionalResponseText = additionalResText

	} else {
		payload.AdditionalResponse = additionalResText
	}

	btes, err := json.Marshal(payload)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}
	tdlObj["strReq"] = string(btes)

	err = retry.CustomRetry(3, 5*time.Second, func() error {

		tdlObj["id"] = general.GetUUID()

		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, method, url, bytes.NewReader(btes))
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return errTDLAPI
		}

		req.Header.Add("client_id", clientID)
		req.Header.Add("Authorization", authHeader)
		req.Header.Add("Content-Type", "application/json")
		if sourceEntityID == constants.TataPLID {
			req.Header.Add("posAuthorization", "true")
		}
		reqHeaders = req.Header

		res, err := client.Do(req)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return errTDLConnect
		}
		defer res.Body.Close()

		httpStatusCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return errResponseRead
		}
		log.Println(string(body))
		tdlObj["strRes"] = string(body)
		respHeaders = res.Header

		respMap := make(map[string]interface{})

		err = json.Unmarshal(body, &respMap)
		if err != nil {
			log.Println(err)
			serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return ErrUnmarshal
		}
		if sourceEntityID != constants.TataBNPLID {
			status, _ := respMap["status"].(string)
			if strings.ToLower(status) != "success" {
				err = errors.New("status is not a success")
				logger.WithUser(userID).Error(err)
				serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
				return err
			}

		}

		return nil
	})

	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	success = 1
	serviceslib.WriteToDBWithHeaders(tdlObj["id"], tdlObj["userID"], serviceName, tdlObj["url"], tdlObj["strReq"], tdlObj["strRes"], "", success, httpStatusCode, reqHeaders, respHeaders)
	return nil
}

func TriggerBringBackUsersJob() error {
	//send this to bg jobs
	asyncPayload := make(map[string]string)
	asyncPayload["source_entity_id"] = constants.TataPLID

	opts := []asynq.Option{
		asynq.Queue(constants.QueueNameTDL),
	}

	return producer.CreateNewtaskAndEnqueue(context.Background(), constants.TaskTDLBringBackUsers, asyncPayload, 1, opts)
}

// ArchiveUsers gets the list of tdl users and pass them to triggerArchiveUserJob one by one
func ArchiveUsers() {

	usersData, err := functionalityModels.GetUsersToArchive(constants.TataPLID, constants.ExpiryTypeArchival)
	if err != nil {
		errObj := fmt.Errorf("[ArchiveUsers] :: Tata PL :: failed to fetch users for archival - %w", err)
		errorHandler.ReportToSentryWithoutRequest(errObj)
		return
	}

	for _, entry := range usersData {
		triggerArchiveUserJob(entry.UserID)
	}

}

// triggerArchiveUserJob it enqueues a user into the queue and creates a task for each user
func triggerArchiveUserJob(userID string) {
	//send this to bg jobs
	asyncPayload := make(map[string]string)
	asyncPayload["user_id"] = userID

	opts := []asynq.Option{
		asynq.Queue(constants.QueueNameTDL),
		asynq.Timeout(2 * time.Minute),
	}

	err := producer.CreateNewtaskAndEnqueue(context.Background(), constants.TaskTDLArchiveUsers, asyncPayload, 1, opts)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"user_id": userID, "task": constants.TaskTDLArchiveUsers}, err)
	}
}

// ArchiveTdlUsers it marks the user as Archived and sends the webhook to TDL
func ArchiveTdlUsers(userID string) error {
	//archivedStatus := constants.UserStatusArchived

	userObj, err := users.Get(userID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	// newUniqueSuffix := fmt.Sprintf("_%s_archived", general.GenerateRandomString(5))
	// trimmedUniqueID := userObj.UniqueID

	// if len(trimmedUniqueID)+len(newUniqueSuffix) > 50 {
	// 	trimmedUniqueID = trimmedUniqueID[0 : len(trimmedUniqueID)-len(newUniqueSuffix)]
	// }
	//newUniqueID := trimmedUniqueID + newUniqueSuffix

	// tx, _ := database.Beginx()
	// err = users.Update(tx, users.User{ID: userID, Status: &archivedStatus, UniqueID: newUniqueID})
	// if err != nil {
	// 	logger.WithUser(userID).Error(err)
	// 	tx.Rollback()
	// 	return err
	// }

	// added to have separate events description for inactivity and policyRejected user expiry
	desc := "app_incomplete_30_days"
	event, _ := expiry.GetEvent(userID)
	if event == UserInactivityExpiryEvent {
		desc = "user_inactivity_30_days"
	}

	errStr, err := commonutils.InitiateNewApplication(context.Background(), userID, userObj.SourceEntityID, constants.EntityTypeCustomer, userID, "", authentication.UserStruct{}, true, false)
	if errStr != "" {
		logger.WithUser(userID).Error(errStr)
		return errors.New(errStr)
	}
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	err = expiry.UpdateStatusOfUserExpiry(nil, userID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	//_ = tx.Commit()

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userID, constants.TataPLID, userObj.UniqueID, constants.EntityTypeSystem, constants.ActivityUserArchived, desc, "", dateTimeNowString, false)

	return nil
}

// getNameFromCustomerID gets the name of the user from the customer id from TDL
func GetNameFromCustomerID(userID string, request map[string]interface{}) (NameForCustomerRes, string, error) {

	conf := conf.TataConfig
	url := conf["baseURL"] + "/v1/marketplacepartnercontroller/pan-name"
	var gobj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}
	var nameMatchResp NameForCustomerRes
	payload, err := json.Marshal(request)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nameMatchResp, "", err
	}
	gobj["strReq"] = string(payload)

	serviceName := "get-name-from-tdl-api"
	var responseCode int

	client := tracer.GetTraceableHTTPClient(nil, serviceName)
	token, err := activity.RetrieveOauthTokenWithRedis(userID)
	if err != nil {
		logger.WithUser(userID).Errorln("error retrieving oauth token for tdl: ", err.Error())
		return NameForCustomerRes{}, "", err
	}
	err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {
		nameMatchResp = NameForCustomerRes{}

		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, http.MethodPost, url, bytes.NewReader(payload))
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Authorization", token)
		req.Header.Add("client_id", conf["ClientID"])
		req.Header.Add("posAuthorization", "true")

		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		defer res.Body.Close()
		responseCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		gobj["strRes"] = string(body)

		err = json.Unmarshal(body, &nameMatchResp)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		return nil
	})

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return nameMatchResp, "", err
	}

	if responseCode != 200 {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], "not 2xx series", gobj["id"])
		return nameMatchResp, "", errors.New("status code is not 200")
	}

	if nameMatchResp.Errors != nil {
		errString, ok := nameMatchResp.Errors.(string)
		if !ok {
			errString = "invalid error response"
		}
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], errString, gobj["id"])
		return nameMatchResp, "", errors.New(errString)
	}

	if nameMatchResp.Status != "Success" {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], "status is not success", gobj["id"])
		return nameMatchResp, "", errors.New("invalid status")
	}

	serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])

	name := general.KeepOnlyAlphaSpace(nameMatchResp.Data.Result.Name)

	return nameMatchResp, name, nil
}

// FetchAndSaveCustomerName is a wrapper over getNameFromCustomerID which fetches the name from TDL and saves it in the user's dynamic info
func FetchAndSaveCustomerName(userID string, uniqueID string) error {
	request := map[string]interface{}{
		"customerHash": uniqueID,
	}
	_, name, err := GetNameFromCustomerID(userID, request)
	if err != nil {
		logger.WithUser(userID).Error(err)
	}

	err = users.UpdateDynamicUserInfoFieldV2(userID, "nameFromTDL", name, nil)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}
	return nil
}

// The function RejectLenderAndBringBack is used to reject the lender and return to the multi offer screen.
// This part of code has been used multiple times in several function so it has been moved to a common function.

func RejectLenderAndBringBack(lenderoffer LenderReject) (bool, error) {
	dateTimeNowString := general.GetTimeStampString()
	// offer has been marked as expired
	go activity.ActivityLogger(lenderoffer.UserID, lenderoffer.SourceEntityID, lenderoffer.UserID, constants.EntityTypeSystem, constants.ActivityLenderRejected, lenderoffer.Description, "", dateTimeNowString, false)
	err := personalloanoffer.ExpireOffer(nil, lenderoffer.OfferID, lenderoffer.UserID, constants.OfferStatusExpired)
	if err != nil {
		logger.WithUser(lenderoffer.UserID).Errorf("[Expire Offer] OfferID - %v; %v", lenderoffer.OfferID, err)
		return false, err
	}
	var userDisqualified bool
	err = journeyutils.EliminateLender(lenderoffer.UserID, lenderoffer.LenderID, lenderoffer.RejectReason)
	if err != nil {
		logger.WithUser(lenderoffer.UserID).Errorln(err)
	}
	activeOfferCount, err := personalloanoffer.GetCount(lenderoffer.UserID, constants.OfferStatusActive)
	if err != nil {
		logger.WithUser(lenderoffer.UserID).Error(err)
		return false, err
	}

	if activeOfferCount < 1 {
		if featureflag.Get(lenderoffer.UserID, journey.FlagLenderGrouping) {
			err := usermodulemapping.Create(nil, lenderoffer.UserID, lenderoffer.UserID, usermodulemapping.Bureau, constants.UserModuleStatusPending, "")
			if err != nil {
				logger.WithUser(lenderoffer.UserID).Error(err)
				return false, err
			}
			err = userworkflows.ArchiveByModuleAndUser(lenderoffer.UserID, usermodulemapping.Bureau)
			if err != nil {
				logger.WithUser(lenderoffer.UserID).Error(err)
				return false, err
			}
		} else {
			description := fmt.Sprintf(`{"rejectReason":"%s"}`, constants.ErrorNoActiveOffers)
			if done := usersutil.DisqualifyUser(lenderoffer.UserID, lenderoffer.SourceEntityID, description, ""); !done {
				err := fmt.Errorf("error in disqualifying user for user Id: %s", lenderoffer.UserID)
				logger.WithUser(lenderoffer.UserID).Error(err)
				return false, err
			}
			userDisqualified = true
		}
	} else {
		err := usermodulemapping.Create(nil, lenderoffer.UserID, lenderoffer.UserID, usermodulemapping.OfferHook, constants.UserModuleStatusPending, "")
		if err != nil {
			log.Error(err)
			return false, err
		}
		activity.ActivityLogger(lenderoffer.UserID, lenderoffer.SourceEntityID, lenderoffer.UserID, constants.EntityTypeSystem, constants.ActivityRedirectedToMultiOffers, lenderoffer.Description, "", dateTimeNowString, false)
		go activity.ActivityLogger(lenderoffer.UserID, lenderoffer.SourceEntityID, lenderoffer.UserID, constants.EntityTypeSystem, constants.ActivityEligibleForOtherOffers, lenderoffer.Description, "", dateTimeNowString, false)
	}

	err = workflowutils.UpdateWorkFlow(lenderoffer.UserID, lenderoffer.SourceEntityID, constants.WorkflowTDLPersonalInfo)
	if err != nil {
		logger.WithUser(lenderoffer.UserID).Error(err)
		return false, err
	}

	return userDisqualified, nil
}

func PostLoanRejectHandler(lenderoffer LenderReject) (bool, error) {
	err := personalloanoffer.ExpireOffer(nil, lenderoffer.OfferID, lenderoffer.UserID, constants.OfferStatusExpired)
	if err != nil {
		logger.WithUser(lenderoffer.UserID).Errorf("[Expire Offer] OfferID - %v; %v", lenderoffer.OfferID, err)
		return false, err
	}

	var userDisqualified bool
	err = journeyutils.EliminateLender(lenderoffer.UserID, lenderoffer.LenderID, lenderoffer.RejectReason)
	if err != nil {
		logger.WithUser(lenderoffer.UserID).Errorln(err)
	}
	activeOfferCount, err := personalloanoffer.GetCount(lenderoffer.UserID, constants.OfferStatusActive)
	if err != nil {
		logger.WithUser(lenderoffer.UserID).Error(err)
		return false, err
	}

	if activeOfferCount < 1 {
		if featureflag.Get(lenderoffer.UserID, journey.FlagLenderGrouping) {
			err := usermodulemapping.Create(nil, lenderoffer.UserID, lenderoffer.UserID, usermodulemapping.Bureau, constants.UserModuleStatusPending, "")
			if err != nil {
				logger.WithUser(lenderoffer.UserID).Error(err)
				return false, err
			}
			err = userworkflows.ArchiveByModuleAndUser(lenderoffer.UserID, usermodulemapping.Bureau)
			if err != nil {
				logger.WithUser(lenderoffer.UserID).Error(err)
				return false, err
			}
		} else {
			description := fmt.Sprintf(`{"rejectReason":"%s"}`, constants.ErrorNoActiveOffers)
			if done := usersutil.DisqualifyUser(lenderoffer.UserID, lenderoffer.SourceEntityID, description, ""); !done {
				err := fmt.Errorf("error in disqualifying user for user Id: %s", lenderoffer.UserID)
				logger.WithUser(lenderoffer.UserID).Error(err)
				return false, err
			}
			userDisqualified = true
		}
	} else {
		err := usermodulemapping.Create(nil, lenderoffer.UserID, lenderoffer.UserID, usermodulemapping.OfferHook, constants.UserModuleStatusPending, "")
		if err != nil {
			log.Error(err)
			return false, err
		}
	}

	err = workflowutils.UpdateWorkFlow(lenderoffer.UserID, lenderoffer.SourceEntityID, constants.WorkflowTDLPLGeneric)
	if err != nil {
		logger.WithUser(lenderoffer.UserID).Error(err)
		return false, err
	}

	return userDisqualified, nil
}

func CreateExitSurveyJob(form []usersurveyresponse.ResponseRow, sourceEntityID string) error {
	//send this to bg jobs
	asyncPayload := make(map[string]any)
	asyncPayload["sourceEntityID"] = sourceEntityID
	asyncPayload["form"] = form

	opts := []asynq.Option{
		asynq.Queue(constants.QueueNameTDL),
	}

	return producer.CreateNewtaskAndEnqueue(context.Background(), constants.TaskExitSurvey, asyncPayload, 1, opts)
}

// GetNameFromPAN fetches the customer's name using their PAN details by calling an external API (Karza).
// Return Values:
// - `NameForCustomerResBNPL`: Response containing name and related details.
// - `string`: Extracted name from the API response.
// - `error`: Returns an error if the request fails, the response status is not 200, or the name is invalid.

func getNameFromPAN(userID string, fetchNameReq map[string]interface{}) (NameForCustomerResBNPL, string, error) {
	baseURL := conf.TataConfig["baseURL"]
	clientID := conf.TataConfig["ClientID"]
	authKey := conf.TataConfig["Authorization"]
	url := baseURL + "/FinBoxPartner/Controller/bnpl/pan-details"
	serviceName := "get-name-from-karza-api"
	var gobj = map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}
	var reqHeaders http.Header
	var respHeaders http.Header
	var httpStatusCode int
	var success int
	var nameMatchResp NameForCustomerResBNPL
	method := http.MethodPost
	payload, err := json.Marshal(fetchNameReq)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDBWithHeaders(gobj["id"], gobj["userID"], serviceName, gobj["url"], gobj["strReq"], gobj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
		return nameMatchResp, "", err
	}
	gobj["strReq"] = string(payload)

	err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {
		nameMatchResp = NameForCustomerResBNPL{}
		gobj["id"] = general.GetUUID()
		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, method, url, bytes.NewReader(payload))
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDBWithHeaders(gobj["id"], gobj["userID"], serviceName, gobj["url"], gobj["strReq"], gobj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return err
		}

		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Accept", "application/json")
		req.Header.Add("Date", fmt.Sprintf("%d", time.Now().Unix()))
		req.Header.Add("Authorization", authKey)
		req.Header.Add("client_id", clientID)
		reqHeaders = req.Header

		timeout := 15 * time.Second
		client := tracer.GetTraceableHTTPClient(&timeout, serviceName)
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDBWithHeaders(gobj["id"], gobj["userID"], serviceName, gobj["url"], gobj["strReq"], gobj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return err
		}
		defer res.Body.Close()
		httpStatusCode = res.StatusCode

		body, err := io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDBWithHeaders(gobj["id"], gobj["userID"], serviceName, gobj["url"], gobj["strReq"], gobj["strRes"], err.Error(), success, httpStatusCode, reqHeaders, respHeaders)
			return err
		}
		gobj["strRes"] = string(body)
		respHeaders = res.Header

		err = json.Unmarshal(body, &nameMatchResp)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		return nil
	})

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return nameMatchResp, nameMatchResp.Result.Name, err
	}

	if httpStatusCode != http.StatusOK {
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], "not 2xx series", gobj["id"])
		return nameMatchResp, nameMatchResp.Result.Name, errors.New("status code is not 200")
	}

	if !general.IsValidName(nameMatchResp.Result.Name) {
		err = fmt.Errorf("invalid name - %s", nameMatchResp.Result.Name)
		logger.WithUser(userID).Errorln(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return nameMatchResp, nameMatchResp.Result.Name, err
	}

	serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])
	return nameMatchResp, nameMatchResp.Result.Name, nil

}

func TdlHDFCUpdateStatus(asyncPayload hdfctdl.UpdateData) error {
	//send this to bg jobs
	opts := []asynq.Option{
		asynq.Queue(constants.QueueNameHDFCTDL),
	}
	return producer.CreateNewtaskAndEnqueue(context.Background(), constants.TaskTdlHDFCStatusUpdate, asyncPayload, 1, opts)
}

func ArchiveInactiveUsers() {

	usersData, err := functionalityModels.GetInactiveUsersToArchive(constants.TataPLID)
	if err != nil {
		errObj := fmt.Errorf("[ArchiveInactiveUsers] :: Tata PL :: failed to fetch inactive users - %w", err)
		errorHandler.ReportToSentryWithoutRequest(errObj)
		return
	}

	for _, entry := range usersData {
		err = expiry.AddExpiry(entry.UserID, constants.ExpiryTypeArchival, UserInactivityExpiryEvent, time.Now())
		if err != nil {
			logger.WithUser(entry.UserID).Errorln(err)
			continue
		}
		triggerArchiveUserJob(entry.UserID)
	}
}
