package event

import (
	"context"
	"database/sql"
	"encoding/json"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/taskmanagement/check"
	dashboardsql "finbox/go-api/internal/repository/psql/dashboard"
	loansql "finbox/go-api/internal/repository/psql/loan"
	"finbox/go-api/internal/repository/psql/querybuilder"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	usersql "finbox/go-api/internal/repository/psql/user"
	dashboardModel "finbox/go-api/models/dashboard"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/utils/general"
	"fmt"

	"github.com/google/uuid"
)

type UpdateNotes struct {
	Notes taskmanagementsql.DBUpdateTaskNotesParam `json:"notes,omitempty"`
	Id    *string                                  `json:"id,omitempty"`
}
type UpdateTaskServiceData struct {
	Task  taskmanagementsql.DBUpdateTasksParam `json:"task,omitempty"`
	Id    string                               `json:"id"`
	Notes []UpdateNotes                        `json:"notes,omitempty"`
}

func (d *UpdateTaskServiceData) validate() error {
	return nil
}

type updateTaskService struct {
	data UpdateTaskServiceData
	check.CheckServiceFactory
}

func (h *updateTaskService) Init(data []byte) (EventService, error) {
	var err error
	if err = json.Unmarshal(data, &h.data); err == nil {
		err = h.data.validate()
	}
	return h, err
}

func NewUpdateTaskService(checkFac check.CheckServiceFactory) EventService {
	return &updateTaskService{
		CheckServiceFactory: checkFac,
	}
}

func (h *updateTaskService) Handle(ctx context.Context, actor, relEntity, relEntityID string) error {
	task := h.data.Task
	task.UpdatedAt = &[]string{general.GetTimeStampString()}[0]

	if task.StatusId == nil || *task.StatusId == "" {
		taskInDb, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{
			ID: &h.data.Id,
		}, &taskmanagementsql.DBGetTasksFields{StatusId: true})
		if err != nil {
			logger.WithContext(ctx).Errorf("[HandleUpdateTaskEvent]error in getting task for loanApplicationID:%s, err:%v", relEntityID, err)
			return err
		}
		task.StatusId = &taskInDb.StatusId
	}

	task.UpdatedBy = &actor
	now := general.GetTimeStampString()
	taskmanagementRepo := taskmanagementsql.NewTaskManagementDBRepository(database) //TODO refactor
	status, err := taskmanagementRepo.DBGetTaskStatusByParams(ctx, &taskmanagementsql.DBGetTaskStatusParam{TaskStatusID: task.StatusId}, nil)
	if err != nil {
		logger.WithContext(ctx).Error("[HandleUpdateEvent] Error fetching task", "error", err)
		return err
	}

	if status.State == mdashboard.Completed || status.State == mdashboard.Cancelled {
		task.ApprovedBy = &actor
		task.ApprovedAt = &now
	}

	taskData, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{ID: &h.data.Id}, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[HandleUpdateEvent] error fetching task for taskID:%s, err:%v", h.data.Id, err)
		return err
	}

	if task.DeletedAt != nil {
		userGroup, err := GetUserRBACGroup(ctx, actor)
		if err != nil {
			logger.WithContext(ctx).Errorf("[isAuthority] Failed to get RBAC group for user email: %s. Error: %v", actor, err)
			return err
		}

		task1, _ := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{ID: &h.data.Id}, nil)
		targetGroup, err := GetUserRBACGroup(ctx, task1.CreatedBy)
		if err != nil {
			logger.WithContext(ctx).Errorf("[isAuthority] Failed to get RBAC group for user email: %s. Error: %v", *task.CreatedBy, err)
			return err
		}

		// Validate target group exists in priority map
		if _, ok := dashboardModel.UserGroupPriority[targetGroup]; !ok {
			err := fmt.Errorf("invalid target group: %s", string(targetGroup))
			logger.WithContext(ctx).Errorf("[isAuthority] %v", err)
			return err
		}

		// Validate user group exists in priority map
		if _, ok := dashboardModel.UserGroupPriority[userGroup]; !ok {
			err := fmt.Errorf("invalid user group: %s", string(userGroup))
			logger.WithContext(ctx).Errorf("[isAuthority] %v", err)
			return err
		}

		// Compare priorities and return result
		isAuthorized := dashboardModel.UserGroupPriority[userGroup] >= dashboardModel.UserGroupPriority[targetGroup]
		if !isAuthorized {
			return fmt.Errorf("not allowed. required group is %s", task1.CreatedBy)
		}

		task.DeletedAt = &now
		fmt.Printf("Task DeletedAt: %s\n", *task.DeletedAt)
	}

	if taskData.TypeId == constants.MasterTaskCreditTaskTypeABFL || taskData.TypeId == constants.MasterTaskRCUTaskTypeABFL {
		loanRepo := loansql.NewLoanDBRepository(database)
		loanApplication, err := loanRepo.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{LoanApplicationID: taskData.IdentifierId})
		if err != nil {
			logger.WithContext(ctx).Errorf("[HandleUpdateEvent] Failed to fetch loan application for LoanApplicationID: %s, Error: %v", taskData.IdentifierId, err)
			return err
		}

		err = updateUserDynamicDataForABFLPLWorkflow(ctx, taskData.TypeId, loanApplication.UserID)
		if err != nil {
			logger.WithContext(ctx).Errorf("[HandleUpdateEvent] error in update user dynamic data for abflpl workflow for userID:%s, err: %v", loanApplication.UserID, err)
			return err
		}
	}

	updateMetadata, err := querybuilder.DBGetJsonAndMergeNewData(
		ctx,
		querybuilder.DBGetJsonAndMergeNewDataParam{
			TableName:    dashboardModel.TASKS_TABLE_NAME,
			JSONColumn:   dashboardModel.METADATA_COLUMN_NAME,
			LookupColumn: dashboardModel.ID_COLUMN_NAME,
			LookupValue:  h.data.Id,
		},
		task.Metadata,
	)
	if err != nil {
		logger.WithContext(ctx).Errorf("[HandleUpdateEvent] Error merging task metadata for taskID: %+v, err %v", h.data.Id, err)
		return err
	}

	updatedMetadataBytes, err := json.Marshal(updateMetadata)
	if err != nil {
		logger.WithContext(ctx).Errorf("[HandleUpdateEvent] Error marshaling updated metadata for taskID: %+v, err %v", h.data.Id, err)
		return err
	}

	updatedMetadataRaw := json.RawMessage(updatedMetadataBytes)
	task.Metadata = &updatedMetadataRaw

	err = taskmanagementsql.DBUpdateTasks(ctx, nil, &task, h.data.Id)
	if err != nil {
		logger.WithContext(ctx).Error("[HandleUpdateEvent] Error updating task", "error", err)
		return err
	}

	for _, note := range h.data.Notes {
		switch note.Id {
		case nil:
			if note.Notes.Type == nil || *note.Notes.Type == "" {
				logger.WithContext(ctx).Error("[HandleUpdateEvent] skipping inserting tasknotes empty task type")
				continue
			}
			noteID := uuid.New().String()
			err = taskmanagementsql.DBInsertTaskNotes(ctx, nil, &taskmanagementsql.DBInsertTaskNotesParam{
				Id:      &noteID,
				Type:    note.Notes.Type,
				Data:    note.Notes.Data,
				TaskId:  &h.data.Id,
				AddedBy: &actor,
			})
			if err != nil {
				logger.WithContext(ctx).Error("[HandleUpdateEvent] Error inserting tasknotes", "error", err)
				return err
			}
		default:
			if note.Notes.Type != nil && *note.Notes.Type == "" {
				note.Notes.Type = nil
			}
			err = taskmanagementsql.DBUpdateTaskNotes(ctx, nil, &note.Notes, *note.Id)
			if err != nil {
				logger.WithContext(ctx).Error("[HandleUpdateEvent] Error updating tasknotes", "error", err)
				return err
			}
		}

	}

	return nil
}

func GetUserRBACGroup(ctx context.Context, email string) (dashboardModel.RBACGroup, error) {
	fmt.Printf("GetUserRBACGroup")
	user, err := dashboardsql.DBGetLenderUsersByParams(ctx, &dashboardsql.DBGetLenderUsersParam{Email: &email}, &dashboardsql.DBGetLenderUsersFields{RbacGroupName: true})
	if err != nil {
		switch err {
		case sql.ErrNoRows:
			logger.WithContext(ctx).Errorf("[GetLenderUserRBACGroup] No user found for email: %s. Error: %v", email, err)
		default:
			logger.WithContext(ctx).Errorf("[GetLenderUserRBACGroup] Failed to fetch lender user by email: %s. Error: %v", email, err)
			errorHandler.ReportToSentryV2(ctx, err)
		}
		return dashboardModel.InvalidRBACGroup, err
	}

	fmt.Printf("User RbacGroupName: %s\n", user.RbacGroupName)
	return user.RbacGroupName, nil
}

func updateUserDynamicDataForABFLPLWorkflow(ctx context.Context, typeId, userID string) error {
	var key string
	switch typeId {
	case constants.MasterTaskCreditTaskTypeABFL:
		key = mdashboard.ABFLPLCreditWorkflowStatus
	case constants.MasterTaskRCUTaskTypeABFL:
		key = mdashboard.ABFLPLRCUWorkflowStatus
	default:
		return nil
	}

	err := usersql.DBUpdateUserDynamicVariables(ctx, userID, mdashboard.UsersTable, mdashboard.DynamicUserInfoColumn, map[string]interface{}{key: "approved"})
	if err != nil {
		logger.WithContext(ctx).Errorf("[updateUserDynamicDataForABFLPLWorkflow] error in update workflow data in user dynamic variables for userID:%s, error: %v", userID, err)
		return err
	}

	return nil
}
