package creditlinesql

import (
	"context"
	"finbox/go-api/internal/repository/psql"
	"fmt"
	"strings"

	"github.com/jmoiron/sqlx"
)

func DBGetSourceEntityByTxnID(ctx context.Context, transactionID string) (string, error) {
	var sourceEntityID string

	query := `
	SELECT 
	source_entity_id 
	FROM 
	credit_line c 
	JOIN
	credit_line_txn t 
	ON 
	c.credit_line_id =t.credit_line_id 
	WHERE 
	t.txn_id =$1;`

	err := psql.Database.Get(&sourceEntityID, query, transactionID)
	if err != nil {
		return sourceEntityID, err
	}
	return sourceEntityID, nil
}

func DBGetCreditLineDetailsByTxnIDAndSourceEntityID(ctx context.Context, txnID string, sourceEntityID string) (DBGetCreditLineDetailResponse, error) {

	var dbObj DBGetCreditLineDetailResponse
	query := `
	SELECT 
	c.user_id as user_id, 
	t.status, 
	coalesce(t.amount, 0) as amount,
	coalesce(t.first_emi_after_days, 0) as after_days,
	coalesce(t.salary_day, 0) as salary_day,
	c.max_limit as max_limit, 
	c.available_limit as available_limit,
	c.credit_line_id as credit_line_id,
	c.credit_line_type as credit_line_type,
	coalesce(t.interest, -1) as interest,
	coalesce(t.subvention_type, '') as subvention_type, 
	coalesce(t.subvention, 0) as subvention,
	coalesce(t.processing_fee_type, '') as processing_fee_type, 
	coalesce(t.processing_fee, 0) as processing_fee,
	coalesce(t.gst, 0) as gst, 
	coalesce(t.invoice_no, '') as invoice_no,
	to_char(t.created_at,'YYYY-MM-DD') as created_at,
	coalesce(t.lender_term_loan_no, '') as lender_drawdown_id,
	coalesce(t.lender_transaction_number, '') AS disbursal_utr,
	coalesce(invoice_no, '') as old_invoice_no, 
	coalesce(merchant_bank_account_no, '') as merchant_bank_account_no, 
	coalesce(merchant_ifsc_code, '') as merchant_ifsc_code, 
	coalesce(merchant_gst_no, '') as merchant_gst_no, 
	coalesce(merchant_mobile, '') as merchant_mobile, 
	coalesce(merchant_email, '') as merchant_email, 
	coalesce(merchant_name, '') as merchant_name, 
	coalesce(invoice_dates, '') as invoice_dates, 
	c.status as credit_line_status,
	coalesce(awb_no, '') as awb_no,
	source_entity_txn_id as partner_txn_id,
	c.lender_id as lender_id,
	l.loan_application_no as parent_loan_id,
	l.loan_application_id as loan_application_id
	FROM 
	credit_line_txn t 
	JOIN 
	credit_line c 
	ON 
	t.credit_line_id = c.credit_line_id
	JOIN 
	loan_application l 
	ON 
	l.loan_application_id = c.parent_loan_id
	WHERE 
	t.txn_id = $1 
	AND 
	c.source_entity_id = $2`

	err := psql.Database.Get(&dbObj, query, txnID, sourceEntityID)

	if err != nil {
		return dbObj, err
	}

	return dbObj, nil
}

func DBUpdateCreditLineDetails(ctx context.Context, tx *sqlx.Tx, request DBUpdateCreditLineTxnDetail, oldCreditlineResponse DBGetCreditLineDetailResponse, changeLogs *[]string) error {

	var err error

	query := `
	UPDATE 
	credit_line_txn 
	SET `

	var conditions []string
	var values []interface{}

	if request.AwbNo != "" {
		conditions = append(conditions, "awb_no = ?")
		values = append(values, request.AwbNo)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_awb_no:%s, new_awb_no:%s", oldCreditlineResponse.AwbNo, request.AwbNo))
	}

	if request.InvoiceNo != "" {
		conditions = append(conditions, "invoice_no = ?")
		values = append(values, request.InvoiceNo)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_invoice_noo:%s, new_invoice_noo:%s", oldCreditlineResponse.OldInvoiceNo, request.InvoiceNo))
	}

	if request.MerchantGSTNumber != "" {
		conditions = append(conditions, "merchant_gst_no = ?")
		values = append(values, request.MerchantGSTNumber)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_merchant_gst_no:%s, new_merchant_gst_no:%s", oldCreditlineResponse.MerchantGstNo, request.MerchantGSTNumber))
	}

	if request.MerchantIFSCCode != "" {
		conditions = append(conditions, "merchant_ifsc_code = ?")
		values = append(values, request.MerchantIFSCCode)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_merchant_ifsc_code:%s, new_merchant_ifsc_code:%s", oldCreditlineResponse.MerchantIfscCode, request.MerchantIFSCCode))
	}

	if request.MerchantBankAccountNumber != "" {
		conditions = append(conditions, "merchant_bank_account_no = ?")
		values = append(values, request.MerchantBankAccountNumber)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_merchant_bank_account_no:%s, new_merchant_bank_account_no:%s", oldCreditlineResponse.MerchantBankAccountNo, request.MerchantBankAccountNumber))
	}

	if request.MerchantMobile != "" {
		conditions = append(conditions, "merchant_mobile = ?")
		values = append(values, request.MerchantMobile)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_merchant_mobile:%s, new_merchant_mobile:%s", oldCreditlineResponse.MerchantMobile, request.MerchantMobile))
	}

	if request.MerchantEmail != "" {
		conditions = append(conditions, "merchant_email = ?")
		values = append(values, request.MerchantEmail)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_merchant_email:%s, new_merchant_email:%s", oldCreditlineResponse.MerchantEmail, request.MerchantEmail))
	}

	if request.MerchantName != "" {
		conditions = append(conditions, "merchant_name = ?")
		values = append(values, request.MerchantName)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_merchant_name:%s, new_merchant_name:%s", oldCreditlineResponse.MerchantName, request.MerchantName))
	}

	if request.InvoiceDates != "" {
		conditions = append(conditions, "invoice_dates = ?")
		values = append(values, request.InvoiceDates)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_invoice_dates:%s, new_invoice_dates:%s", oldCreditlineResponse.InvoiceDates, request.InvoiceDates))
	}

	if request.Status >= 0 {
		conditions = append(conditions, "status = ?")
		values = append(values, request.Status)
	}

	if request.ConfirmedAt != "" {
		conditions = append(conditions, "confirmed_at = ?")
		values = append(values, request.ConfirmedAt)
	}

	if request.LenderTermLoanNo != "" {
		conditions = append(conditions, "lender_term_loan_no = ?")
		values = append(values, request.LenderTermLoanNo)
		*changeLogs = append(*changeLogs, fmt.Sprintf("old_lender_term_loan_no:%s, new_lender_term_loan_no:%s", oldCreditlineResponse.LenderDrawdownID, request.LenderTermLoanNo))
	}

	if request.TxnMetadata != "" {
		conditions = append(conditions, "txn_metadata = ?")
		values = append(values, request.TxnMetadata)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " , ")
	} else {
		return fmt.Errorf("[DBUpdateCreditLineDetails] no condition to update. userID: %+v", request.TxnID)
	}

	query += ", updated_at = NOW() WHERE txn_id = ? "

	values = append(values, request.TxnID)
	query = psql.Database.Rebind(query)

	if tx != nil {
		_, err = tx.Exec(query, values...)
	} else {
		_, err = psql.Database.Exec(query, values...)
	}

	if err != nil {
		return fmt.Errorf("[DBUpdateCreditLineDetails] failed to update creditline details by txID. err: %v, userID: %+v", err, request.TxnID)
	}

	return nil

}

func DBDeleteCreditLineDoc(ctx context.Context, tx *sqlx.Tx, dbReq DBDeleteCreditLineDocRequest) error {

	var err error
	query := `
	DELETE 
	FROM 
	credit_line_txn_docs 
	WHERE 
	txn_id = $1 
	AND 
	media_id 
	IN 
	(SELECT 
	media_id 
	FROM 
	media 
	WHERE 
	media_type = $2 
	AND 
	user_id = $3
	)`

	if tx != nil {
		_, err = tx.Exec(query, dbReq.TxnID, dbReq.MediaType, dbReq.UserID)
	} else {
		_, err = psql.Database.Exec(query, dbReq.TxnID, dbReq.MediaType, dbReq.UserID)
	}

	if err != nil {
		return fmt.Errorf("[DBDeleteCreditLineDoc] failed to delete creditline doc. err: %v, txnID: %+v", err, dbReq.TxnID)
	}
	return nil
}

func DBInsertCreditLineDoc(ctx context.Context, tx *sqlx.Tx, insertReq DBUpdateCreditLineDocRequest) error {
	var err error

	query := `
	INSERT 
	INTO 
	credit_line_txn_docs (txn_id, media_id, created_at) 
	VALUES 
	($1, $2, NOW())`

	if tx != nil {
		_, err = tx.Exec(query, insertReq.TxnID, insertReq.MediaID)
	} else {
		_, err = psql.Database.Exec(query, insertReq.TxnID, insertReq.MediaID)
	}

	if err != nil {
		return fmt.Errorf("[DBInsertCreditLineDoc] failed to insert creditline doc. err: %v, txnID: %+v", err, insertReq.TxnID)
	}
	return nil
}

func DBUpdateCreditLine(ctx context.Context, tx *sqlx.Tx, dbReq DBUpdateCreditLineReq) error {
	var err error

	query := `
	UPDATE 
	credit_line 
	SET 
	updated_at = $1, available_limit = $2 
	WHERE 
	credit_line_id = $3`

	if tx != nil {
		_, err = tx.Exec(query, dbReq.DateTimeNowString, dbReq.NewBalance, dbReq.CreditLineID)
	} else {
		_, err = psql.Database.Exec(query, dbReq.DateTimeNowString, dbReq.NewBalance, dbReq.CreditLineID)
	}

	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBUpdateCreditLine] failed to update creditline . err: %v, creditLineID: %+v", err, dbReq.CreditLineID)
		return err
	}
	return nil
}

func DBCountCreditLineTxnWithCreditLineID(ctx context.Context, dbReq *DBCountCreditLineTxnWithCreditLineIDParam) (int, error) {
	var count int

	query := `
	SELECT
		count(*)
	FROM 
		credit_line_txn t 
	JOIN
		credit_line c
	ON
		c.credit_line_id = t.credit_line_id
	WHERE
		t.source_entity_txn_id = $1 
	AND
		c.source_entity_id = $2`

	err := psql.Database.GetContext(ctx, &count, query, dbReq.TransactionID, dbReq.SourceEntityID)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBCountCreditLineTxnWithCreditLineID] failed to get credit line txn count. err: %v, req: %+v", err, dbReq)
		return 0, err
	}

	return count, nil
}
