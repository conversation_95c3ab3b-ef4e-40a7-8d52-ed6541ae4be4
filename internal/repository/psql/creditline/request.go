// Package creditlinesql contains creditLine related models
package creditlinesql

// DBUpdateCreditLineTxnDetail represents creditline details update request
type DBUpdateCreditLineTxnDetail struct {
	UserID                    string `json:"userID"`
	InvoiceNo                 string `json:"invoiceNo"`
	AwbNo                     string `json:"awbNo"`
	TxnID                     string `json:"txnID"`
	InvoiceMediaID            string `json:"invoiceMediaID"`
	PODMediaID                string `json:"podMediaID"`
	MerchantGSTNumber         string `json:"merchantGSTNumber"`
	MerchantIFSCCode          string `json:"merchantIFSCCode"`
	MerchantBankAccountNumber string `json:"merchantBankAccountNumber"`
	MerchantMobile            string `json:"merchantMobile"`
	LenderTermLoanNo          string `json:"lenderTermLoanNo"`
	Status                    int    `json:"status"`
	ConfirmedAt               string `json:"confirmedAt"`
	MerchantEmail             string `json:"merchantEmail"`
	MerchantName              string `json:"merchantName"`
	InvoiceDates              string `json:"invoiceDates"`
	TxnMetadata               string `json:"txnMetadata"`
}

type DBUpdateCreditLineDocRequest struct {
	TxnID   string `json:"txnID"`
	MediaID string `json:"mediaID"`
}

type DBDeleteCreditLineDocRequest struct {
	TxnID     string `json:"txnID"`
	MediaType string `json:"mediaType"`
	UserID    string `json:"userID"`
}

type DBUpdateCreditLineReq struct {
	CreditLineID      string  `json:"creditLineID"`
	NewBalance        float64 `json:"newBalance"`
	DateTimeNowString string  `json:"dateTimeNowString"`
}

type DBCountCreditLineTxnWithCreditLineIDParam struct {
	TransactionID  string `json:"transactionID"`
	SourceEntityID string `json:"sourceEntityID"`
}
