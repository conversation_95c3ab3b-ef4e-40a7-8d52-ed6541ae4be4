package documentsql

// DBGetDashboardDocsResponse defines the response structure for dashboard documents
type DBGetDashboardDocsResponse struct {
	DocID             string `db:"doc_id"`
	LoanApplicationID string `db:"loan_application_id"`
	MediaID           string `db:"media_id"`
	CreatedAt         string `db:"created_at"`
	CreatedBy         string `db:"created_by"`
	EntityType        string `db:"entity_type"`
	Status            bool   `db:"status"`
	ReviewStatus      int    `db:"review_status"`
	DocumentID        string `db:"document_id"`
}

// DBGetDocumentResponse defines the response structure for a document
type DBGetDocumentResponse struct {
	DocumentID       string `db:"document_id"`
	DocumentName     string `db:"document_name"`
	DocumentCategory string `db:"document_category"`
	Status           int    `db:"status"`
	CreatedAt        string `db:"created_at"`
	CreatedBy        string `db:"created_by"`
	UpdatedAt        string `db:"updated_at"`
	UpdatedBy        string `db:"updated_by"`
	DocumentTitle    string `db:"document_title"`
	BothSides        bool   `db:"both_sides"`
	AllowUpload      bool   `db:"allow_upload"`
	IconSubURL       string `db:"icon_sub_url"`
}

type DBGetPanDetailsResponse struct {
	PanDetailsID      string `db:"pan_details_id"`
	UserID            string `db:"user_id"`
	ExternalServiceID string `db:"external_service_id"`
	Name              string `db:"name"`
	PanNumber         string `db:"pan_number"`
	Status            int    `db:"status"`
	Gender            int    `db:"gender"`
	FirstName         string `db:"first_name"`
	MiddleName        string `db:"middle_name"`
	LastName          string `db:"last_name"`
	DOB               string `db:"dob"`
	Address           string `db:"address"`
	AadhaarLinked     string `db:"aadhaar_linked"`
	Pincode           string `db:"pincode"`
	DynamicPanInfo    string `db:"dynamic_pan_info"`
}

// DBGetUserDocumentsResponse defines the response structure for user documents with media
type DBGetUserDocumentsResponse struct {
	DocID            string `db:"doc_id"`
	DocumentID       string `db:"document_id"`
	DocumentCategory string `db:"document_category"`
	DocumentName     string `db:"document_name"`
	Path             string `db:"path"`
	ReviewStatus     int    `db:"review_status"`
	Password         string `db:"password"`
	CreatedAt        string `db:"created_at"`
	UserID           string `db:"user_id"`
}

type DocumentDataInterface struct {
	DocumentID       string
	DocumentCategory string
	DocumentName     string
	Path             string
	ReviewStatus     int
	Password         string
	DocID            string
	CreatedAt        string
}

type DBGetDashboardCommentsResponse struct {
	DocId      string `db:"doc_id"`
	Comment    string `db:"comment"`
	CreatedAt  string `db:"created_at"`
	EntityType string `db:"entity_type"`
	Status     bool   `db:"status"`
	CommentId  string `db:"comment_id"`
}

const (
	DOC_STATUS_UNDER_REVIEW = 0
	DOC_STATUS_NOT_VERIFIED = 1
	DOC_STATUS_SAMPLED      = 2
	DOC_STATUS_SCREENED     = 3
	DOC_SAMPLED_POSITIVE    = 4
	DOC_SAMPLED_REFER       = 5
	DOC_SAMPLED_NEGATIVE    = 6
	DOC_SAMPLED_DECLINE     = 7
	DOC_SAMPLED_HOLD        = 8
)

var DocReviewStatusMap = map[int]string{
	DOC_STATUS_UNDER_REVIEW: "Under Review",
	DOC_STATUS_NOT_VERIFIED: "Not Verified",
	DOC_STATUS_SAMPLED:      "Sampled",
	DOC_STATUS_SCREENED:     "Screened",
	DOC_SAMPLED_POSITIVE:    "Sampled Positive",
	DOC_SAMPLED_REFER:       "Sampled Refer",
	DOC_SAMPLED_NEGATIVE:    "Sampled Negative",
	DOC_SAMPLED_DECLINE:     "Sampled Decline",
	DOC_SAMPLED_HOLD:        "Hold",
}
