package usersql

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/repository/psql"
	"fmt"
	"sort"
	"strings"

	"github.com/lib/pq"

	ftracer "finbox/go-api/functions/tracer"

	"github.com/jmoiron/sqlx"

	oteltrace "go.opentelemetry.io/otel/trace"
)

// DBGetUserByParamsV2 is to fetch user details by params
func (udr *UserDBRepository) DBGetUserByParamsV2(ctx context.Context, param DBGetUserInfoParam) (DBUserDetailResponse, error) {

	var span oteltrace.Span
	span, ctx = ftracer.StartOtelChildSpan(ctx, ftracer.ChildSpanInfo{
		OperationName: "DBGetUserByParamsV2"})
	defer span.End()

	var resp DBUserDetailResponse

	query := ` 
	SELECT 
		coalesce(u.user_id::TEXT, '') as user_id,
		coalesce(u.name, '') as name, 
		coalesce(u.email, '') as email, 
		coalesce(u.mobile, '') as mobile,
		coalesce(u.unique_id, '') as unique_id,  
		coalesce(u.gender, -1) as gender,
		coalesce(to_char(u.dob, 'YYYY-MM-DD'), '') as dob, 
		coalesce(u.pincode, '') as pincode,
		coalesce(u.fcm_token, '') as fcm_token,
        coalesce(u.pan, '') as pan,
        coalesce(u.source_entity_id::TEXT, '') as source_entity_id,
        coalesce(u.status, 0) as status,
		coalesce(u.kyc_rule_num, 0) as kyc_rule_num,
		coalesce(to_char(u.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at,
		coalesce(u.created_by, '') as created_by,
		coalesce(to_char(u.updated_at, 'YYYY-MM-DD HH24:MI:SS'), '') as updated_at,
        coalesce(u.updated_by, '') as updated_by,
		coalesce(u.sdk_version, '') as sdk_version,
		coalesce(u.partner_data::TEXT, '') as partner_data,
	    coalesce(u.crm_id, '') as crm_id,
		coalesce(u.device_id, '') as device_id,
		coalesce(u.partner_code, '') as partner_code,
		coalesce(u.prospect_no, '') as prospect_no,
		coalesce(u.firm_name, '') as firm_name,
		coalesce(u.source, '') as source,
		coalesce(u.kyc_flow, 0) as kyc_flow,
		coalesce(u.dsa_id::TEXT, '') as dsa_id,
		coalesce(u.journey_config_id::TEXT, '') as journey_config_id,
		coalesce(u.dynamic_user_info::TEXT, '') as dynamic_user_info
	FROM 
		users u
	WHERE 
	`

	var conditions []string
	var values []interface{}

	// Add conditions based on the provided values
	if param.SourceEntityID != "" {
		conditions = append(conditions, "source_entity_id = ?")
		values = append(values, param.SourceEntityID)
	}

	if param.UserID != "" {
		conditions = append(conditions, "user_id = ?")
		values = append(values, param.UserID)
	}

	if param.UniqueID != "" {
		conditions = append(conditions, "unique_id = ?")
		values = append(values, param.UniqueID)
	}

	if param.PAN != "" {
		conditions = append(conditions, "pan = ?")
		values = append(values, param.PAN)
	}

	if param.Mobile != 0 {
		conditions = append(conditions, "mobile = ?")
		values = append(values, param.UserID)
	}

	if param.DSAID != "" {
		conditions = append(conditions, "dsa_id = ?")
		values = append(values, param.DSAID)
	}

	if param.DeviceID != "" {
		conditions = append(conditions, "device_id = ?")
		values = append(values, param.DSAID)
	}

	if param.Status != nil {
		conditions = append(conditions, "status = ?")
		values = append(values, param.Status)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " AND ")
	}

	query = udr.db.Rebind(query)

	err := udr.db.Get(&resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetUserByParamsV2] failed to get user by params. err: %v, param: %+v", err, param)
		return resp, err
	}

	var dynamicUserInfo DBUserDynamicInfo
	if resp.DynamicUserInfoStr != "" {
		err = json.Unmarshal([]byte(resp.DynamicUserInfoStr), &dynamicUserInfo)
		if err != nil {
			logger.WithContext(ctx).Warnf("[DBGetUserByParamsV2] failed to parse dynamic user info. err: %v, userID: %+v", err, param)
		}
	}

	resp.DynamicUserInfo = dynamicUserInfo

	return resp, nil
}

// DBGetUserByID is to fetch user details by id from db
func DBGetUserByID(ctx context.Context, userID string) (DBUserDetailResponse, error) {

	var resp DBUserDetailResponse

	query := ` 
	SELECT 
		coalesce(u.name, '') as name, 
		coalesce(u.email, '') as email, 
		coalesce(u.mobile, '') as mobile, 
		coalesce(u.gender, -1) as gender,
		coalesce(to_char(u.dob, 'YYYY-MM-DD'), '') as dob, 
		source_entity_id,
		coalesce(u.pan,'') as pan, 
		u.unique_id as unique_id,
		coalesce(u.partner_code, '') as partner_code,
		coalesce(u.dsa_id::TEXT, '') as dsa_id,
		coalesce(u.dynamic_user_info::jsonb->>'loanAmount', '0')::decimal as req_loan_amount
	FROM 
		users u
	WHERE 
		u.user_id = $1
	`

	err := psql.Database.Get(&resp, query, userID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetUserByID] failed to get user by id. err: %v, userID: %+v", err, userID)
		return resp, err
	}

	return resp, nil
}

// DBGetUserBusinessDetailByUserID is to fetch user business details by id from db
func DBGetUserBusinessDetailByUserID(ctx context.Context, userID string) (DBGetUserBusinessDetailResponse, error) {

	var resp DBGetUserBusinessDetailResponse

	query := ` 
	Select 
		coalesce(industry_type, '') as industry_type, 
		coalesce(sub_industry_type, '') as sub_industry_type, 
		coalesce(req_loan_amount, 0) as req_loan_amount
	FROM 
		user_business 
	WHERE 
		user_id = $1
	`

	err := psql.Database.Get(&resp, query, userID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetUserBusinessDetailByUserID] failed to get user business detail by id. err: %v, userID: %+v", err, userID)
		return resp, err
	}

	return resp, nil
}

// DBGetUserBureauDetailByUserID is to fetch user bureau details by id from db
func DBGetUserBureauDetailByUserID(ctx context.Context, userID string) (DBGetUserBureauDetailResponse, error) {

	var resp DBGetUserBureauDetailResponse

	query := ` 
	SELECT 
		coalesce(b.experian_score, '-1')::integer as experian_score,
		coalesce(b.experian_status, '') as experian_status,
		coalesce(b.experian_error, '') as experian_error,
		coalesce(b.cibil_score, '-1')::integer as cibil_score,
		coalesce(b.cibil_status, '') as cibil_status,
		b.cibil_hard_pull_score as cibil_hard_pull_score
	FROM 
		bureau_score b 
	WHERE 
		b.user_id = $1
	`

	err := psql.Database.Get(&resp, query, userID)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBGetUserBaureauDetailByUserID] failed to get user bureau detail by id. err: %v, userID: %+v", err, userID)
		return resp, err
	}

	return resp, nil
}

// DBGetAdditionalUserInfo is to fetch user additional details by id from db
func DBGetAdditionalUserInfo(ctx context.Context, userID string, lenderID string) (DBGetAdditionalUserInfoResponse, error) {

	var resp DBGetAdditionalUserInfoResponse

	query := ` 
	SELECT
		coalesce(u.dynamic_user_info,'') as dynamic_user_info,
		coalesce(der.output_variables::json->>'distance_flag','') as distance_flag
	FROM 
		users u 
	INNER JOIN 
		decision_engine_response der 
	ON 
		u.user_id=der.user_id
	WHERE
		u.user_id=$1 
	AND 
		der.lender_id=$2
	ORDER BY
		der.created_at DESC LIMIT 1
	`

	err := psql.Database.GetContext(ctx, &resp, query, userID, lenderID)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBGetAdditionalUserInfo] failed to get user additional detail by id. err: %v, userID: %+v, lenderID: %+v", err, userID, lenderID)
		return resp, err
	}

	return resp, nil
}

// DBGetUsersCountByIDAndSourceEntityID is to fetch users count by id and sourceEntityId from db
func DBGetUsersCountByIDAndSourceEntityID(ctx context.Context, userID string, sourceEntityID string) (int, error) {

	query := ` 
	SELECT 
		count(*)
	FROM 
		users u
	WHERE 
		u.user_id = $1
	AND 
	    u.source_entity_id=$2
	`

	var resp int
	err := psql.Database.Get(&resp, query, userID, sourceEntityID)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBGetUserByID] failed to get user details by id and sourceEntityId. err: %v, userID: %+v,sourceEntityId: %v", err, userID, sourceEntityID)
		return resp, err
	}

	return resp, nil
}

// DBGetUserLoanDetailsByUserIDAndLenderID is to fetch users loan details from db
func DBGetUserLoanDetailsByUserIDAndLenderID(ctx context.Context, userID string, lenderID string) (DBGetUserLoanInfoResponse, error) {
	query := `
	SELECT 
	u.source_entity_id as source_entity_id, 
	l.loan_application_id as loan_application_id,
	l.loan_application_no as loan_application_no
	FROM 
	users u join loan_application l on l.user_id = u.user_id
	WHERE 
	u.user_id = $1 and lender_id = $2 
	ORDER by 
	l.created_at desc limit 1`

	var resp DBGetUserLoanInfoResponse

	err := psql.Database.Get(&resp, query, userID, lenderID)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBGetUserLoanDetailsByUserIDAndLenderID] failed to get user loan details by id and lenderId. err: %v, userID: %+v,lenderId: %v", err, userID, lenderID)
		return resp, err
	}

	return resp, nil
}

// DBUpdateUserDetails is to update user details
func DBUpdateUserDetails(ctx context.Context, tx *sqlx.Tx, userID string, firmName string) error {

	query := `
	UPDATE 
		users 
	SET 
	`

	var conditions []string
	var values []interface{}

	if firmName != "" {
		conditions = append(conditions, "firm_name = ?")
		values = append(values, firmName)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " , ")
	}

	query += ", updated_at = NOW() WHERE user_id = ? "

	values = append(values, userID)
	query = psql.Database.Rebind(query)

	if tx != nil {
		_, err := tx.Exec(query, values...)
		if err != nil {
			psql.Log.WithContext(ctx).Errorf("[DBUpdateUserDetails] failed to update user details. err: %v, userID: %+v", err, userID)
			return err
		}
		return nil
	}

	_, err := database.Exec(query, values...)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBUpdateUserDetails] failed to update user details. err: %v, userID: %+v", err, userID)
		return err
	}

	return nil
}

func DBGetUserHunterDetails(ctx context.Context, userID string) (DBGetUserHunterDetailsResponse, error) {
	query := `select 
			coalesce(dynamic_variables::jsonb->>'hunterIdentifier', '') as hunteridentifier,
			coalesce((dynamic_variables::jsonb->>'hunterMatchCount')::int, -1) as hunterMatchCount,
			coalesce(dynamic_variables::jsonb->>'hunterDate', '') as hunterdate,
			coalesce(dynamic_variables::jsonb->>'hunterApprovalStatus', '') as hunterapprovalstatus
from lender_variables where user_id = $1`

	var resp DBGetUserHunterDetailsResponse

	err := psql.Database.Get(&resp, query, userID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetUserHunterDetails] failed to get user hunter details. err: %v, userID: %+v", err, userID)
		return resp, err
	}

	return resp, nil

}

// DBUpdateUserVariables updates user variables in the lender_variables table
func DBUpdateDynamicVariables(ctx context.Context, userID string, updates map[string]interface{}) error {
	setClause := "dynamic_variables = "
	args := []interface{}{}
	keys := make([]string, 0, len(updates))
	for k := range updates {
		keys = append(keys, k)
	}
	sort.Strings(keys) // Sort keys to ensure consistent order
	for i, key := range keys {
		value := updates[key]
		var valuePlaceholder string
		switch v := value.(type) {
		case string:
			valuePlaceholder = "to_jsonb(?::text)"
			args = append(args, v)
		case int, int64, float64, bool:
			valuePlaceholder = "to_jsonb(?)"
			args = append(args, v)
		default:
			return fmt.Errorf("unsupported value type: %T", v)
		}
		if i == 0 {
			setClause += fmt.Sprintf("jsonb_set(COALESCE(dynamic_variables::jsonb, '{}'::jsonb), '{%s}', %s, true)", key, valuePlaceholder)
		} else {
			setClause = fmt.Sprintf("jsonb_set(%s, '{%s}', %s, true)", setClause, key, valuePlaceholder)
		}
	}
	query := fmt.Sprintf(`UPDATE lender_variables SET %s, updated_at = NOW() WHERE user_id = ?;`,
		setClause)
	args = append(args, userID)
	query = psql.Database.Rebind(query)
	_, err := psql.Database.Exec(query, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBUpdateDynamicVariables] failed to update dynamic variables. err: %v, updates: %+v",
			err, updates)
		return err
	}
	return nil
}

func DBGetUserWorkflowsByParam(ctx context.Context, param DBGetUserWorkflowsParam) (DBGetUserWorkflowsResponse, error) {

	var span oteltrace.Span
	span, ctx = ftracer.StartOtelChildSpan(ctx, ftracer.ChildSpanInfo{
		OperationName: "DBGetUserWorkflowsByParam"})
	defer span.End()

	var resp DBGetUserWorkflowsResponse

	query := `
	SELECT
		coalesce(uw.user_id::TEXT, '') as user_id,
		coalesce(to_char(uw.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at,
		coalesce(uw.run_id::TEXT, '') as run_id,
		coalesce(uw.workflow_config_id::TEXT, '') as workflow_config_id,
		coalesce(uw.metadata::TEXT, '') as metadata,
		coalesce(uw.id::TEXT, '') as id,
		coalesce(uw.init_source, '') as init_source,
		coalesce(uw.workflow_id, '') as workflow_id,
		coalesce(uw.module_name, '') as module_name,
		coalesce(uw.status, '') as status,
		coalesce(uw.error_type, '') as error_type,
		coalesce(uw.failure_reason, '') as failure_reason
	FROM 
		user_workflows uw
	WHERE `

	var conditions []string
	var values []interface{}

	// Add conditions based on the provided values
	if param.UserID != "" {
		conditions = append(conditions, " user_id = ?")
		values = append(values, param.UserID)
	}
	if param.ID != "" {
		conditions = append(conditions, " id = ?")
		values = append(values, param.ID)
	}
	if param.WorkflowID != "" {
		conditions = append(conditions, " workflow_id = ?")
		values = append(values, param.WorkflowID)
	}
	if param.ModuleName != "" {
		conditions = append(conditions, " module_name = ?")
		values = append(values, param.ModuleName)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " AND ")
	}

	query += " ORDER BY created_at DESC limit 1"
	query = psql.Database.Rebind(query)

	err := psql.Database.Get(&resp, query, values...)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBGetUserWorkflowsByParam] failed to get user workflows by params. err: %v, param: %+v", err, param)
		return resp, err
	}

	return resp, nil
}

func DBGetMultiUserLoanRelationsByParam(ctx context.Context, param DBGetMultiUserLoanRelationsParam) (DBGetMultiUserLoanRelationsResponse, error) {
	var resp DBGetMultiUserLoanRelationsResponse

	query := `
	SELECT
		coalesce(mulr.user_id::TEXT, '') as user_id,
		coalesce(mulr.parent_user_id::TEXT, '') as parent_user_id,
		coalesce(to_char(mulr.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at,
		coalesce(to_char(mulr.updated_at, 'YYYY-MM-DD HH24:MI:SS'), '') as updated_at,
		coalesce(mulr.active, false) as active,
		coalesce(mulr.source_entity_id::TEXT, '') as source_entity_id,
		coalesce(mulr.dynamic_info::TEXT, '') as dynamic_info,
		coalesce(mulr.relation, '') as relation,
		coalesce(mulr.loan_type, '') as loan_type,
		coalesce(mulr.loan_variant, '') as loan_variant
	FROM 
		multi_user_loan_relations mulr
	WHERE `

	var conditions []string
	var values []interface{}

	// Add conditions based on the provided values
	if param.UserID != "" {
		conditions = append(conditions, " user_id = ?")
		values = append(values, param.UserID)
	}
	if param.ParentUserID != "" {
		conditions = append(conditions, " parent_user_id = ?")
		values = append(values, param.ParentUserID)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " AND ")
	}

	query = psql.Database.Rebind(query)

	err := psql.Database.Get(&resp, query, values...)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBGetMultiUserLoanRelationsByParam] failed to get multi-user loan relations by params. err: %v, param: %+v", err, param)
		return resp, err
	}

	return resp, nil
}

func DBGetMultiUserLoanRelationsListByParam(ctx context.Context, param DBGetMultiUserLoanRelationsParam) ([]DBGetMultiUserLoanRelationsResponse, error) {

	var span oteltrace.Span
	span, ctx = ftracer.StartOtelChildSpan(ctx, ftracer.ChildSpanInfo{
		OperationName: "DBGetMultiUserLoanRelationsListByParam"})
	defer span.End()

	var resp []DBGetMultiUserLoanRelationsResponse
	query := `
	SELECT
		coalesce(mulr.user_id::TEXT, '') as user_id,
		coalesce(mulr.parent_user_id::TEXT, '') as parent_user_id,
		coalesce(to_char(mulr.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at,
		coalesce(to_char(mulr.updated_at, 'YYYY-MM-DD HH24:MI:SS'), '') as updated_at,
		coalesce(mulr.active, false) as active,
		coalesce(mulr.source_entity_id::TEXT, '') as source_entity_id,
		coalesce(mulr.dynamic_info::TEXT, '') as dynamic_info,
		coalesce(mulr.relation, '') as relation,
		coalesce(mulr.loan_type, '') as loan_type,
		coalesce(mulr.loan_variant, '') as loan_variant
	FROM 
		multi_user_loan_relations mulr
	WHERE `

	var conditions []string
	var values []interface{}

	// Add conditions based on the provided values
	if param.UserID != "" {
		conditions = append(conditions, " user_id = ?")
		values = append(values, param.UserID)
	}
	if param.ParentUserID != "" {
		conditions = append(conditions, " parent_user_id = ?")
		values = append(values, param.ParentUserID)
	}

	if param.Active != nil {
		conditions = append(conditions, " active =?")
		values = append(values, *param.Active)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " AND ")
	}

	query = psql.Database.Rebind(query)

	err := psql.Database.Select(&resp, query, values...)
	if err != nil {
		psql.Log.WithContext(ctx).Errorf("[DBGetMultiUserLoanRelationsListByParam] failed to get multi-user loan list relations by params. err: %v, param: %+v", err, param)
		return resp, err
	}

	return resp, nil
}
func DBUpdateUserDynamicVariables(ctx context.Context, userID, table, column string, updates map[string]interface{}) error {
	var existingData interface{}
	query := fmt.Sprintf(`SELECT %s FROM %s WHERE user_id = ?;`, pq.QuoteIdentifier(column), pq.QuoteIdentifier(table))
	query = database.Rebind(query)
	err := psql.Database.Get(&existingData, query, userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to fetch existing data: %w", err)
	}

	var existingMap map[string]interface{}

	// Check the type of existing data (string or JSON)
	switch data := existingData.(type) {
	case string:
		// Handle string columns
		if data == "" {
			existingMap = make(map[string]interface{})
		} else if err := json.Unmarshal([]byte(data), &existingMap); err != nil {
			return fmt.Errorf("failed to unmarshal existing string data: %w", err)
		}
	case []byte:
		// Handle JSON columns (as []byte from the database)
		if len(data) == 0 {
			existingMap = make(map[string]interface{})
		} else if err := json.Unmarshal(data, &existingMap); err != nil {
			return fmt.Errorf("failed to unmarshal existing JSON data: %w", err)
		}
	case nil:
		existingMap = make(map[string]interface{})
	default:
		// Handle unexpected data types
		return fmt.Errorf("unexpected data type for column: %T", data)
	}

	// Merge updates into the existing map
	for key, value := range updates {
		existingMap[key] = value
	}

	// Convert merged data back to JSON
	mergedJSON, err := json.Marshal(existingMap)
	if err != nil {
		return fmt.Errorf("failed to marshal merged data to JSON: %w", err)
	}

	// Perform the update
	updateQuery := fmt.Sprintf(`UPDATE %s SET %s = ?, updated_at = NOW() WHERE user_id = ?;`, pq.QuoteIdentifier(table), pq.QuoteIdentifier(column))
	updateQuery = database.Rebind(updateQuery)
	_, err = psql.Database.ExecContext(ctx, updateQuery, mergedJSON, userID)
	if err != nil {
		return fmt.Errorf("failed to update dynamic variables: %w", err)
	}

	return nil
}

func DBGetServiceRequestID(ctx context.Context, userID, workflowName string) (string, error) {
	var serviceRequestID string
	query := `select dwst.service_request_id from dashboard_workflow_status_tracker dwst 
left join loan_application la on dwst.resource_id=la.loan_application_id 
left join users u on la.user_id=u.user_id where u.user_id=? 
and dwst.workflow_name = ?
order by la.created_at desc`
	query = database.Rebind(query)
	err := database.GetContext(ctx, &serviceRequestID, query, userID, workflowName)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetServiceRequestID] Error getting serviceRequestID for userID:%s, err:%v", userID, err)
		return serviceRequestID, err
	}
	return serviceRequestID, nil
}

func DBCheckIfUserExists(ctx context.Context, sourceEntityID string, mobile string) (bool, error) {
	var exists bool
	query := `SELECT EXISTS (SELECT 1 FROM users WHERE mobile = $1 AND source_entity_id = $2 AND status != $3)`
	err := database.Get(&exists, query, mobile, sourceEntityID, constants.UserStatusArchived)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBCheckIfUserExists] failed to check if user exists with error:%v for mobile:%v", err, mobile)
		return false, err
	}
	return exists, nil
}

// DBUpdateManualQCData updates the ManualQCApprovedBy field in dynamic_user_info
func DBUpdateManualQCData(ctx context.Context, userID string, reviewer string, status string) error {
	var span oteltrace.Span
	span, ctx = ftracer.StartOtelChildSpan(ctx, ftracer.ChildSpanInfo{
		OperationName: "DBUpdateManualQCData"})
	defer span.End()

	// First get the current dynamic_user_info
	query := `
    SELECT 
        coalesce(dynamic_user_info::TEXT, '{}') as dynamic_user_info
    FROM 
        users 
    WHERE 
        user_id = $1`

	var currentInfo struct {
		DynamicUserInfoStr string `db:"dynamic_user_info"`
	}

	err := psql.Database.Get(&currentInfo, query, userID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBUpdateManualQCData] failed to get current dynamic_user_info. err: %v, userID: %s", err, userID)
		return err
	}

	// Initialize the map
	dynamicInfo := make(map[string]interface{})

	// Parse the current dynamic_user_info into the map
	if currentInfo.DynamicUserInfoStr != "" && currentInfo.DynamicUserInfoStr != "{}" {
		if err = json.Unmarshal([]byte(currentInfo.DynamicUserInfoStr), &dynamicInfo); err != nil {
			logger.WithContext(ctx).Errorf("[DBUpdateManualQCData] failed to unmarshal dynamic_user_info. err: %v, userID: %s", err, userID)
			return err
		}
	}

	// Update only the ManualQC field
	dynamicInfo["manual_qc_reviewer"] = reviewer
	dynamicInfo["manual_qc_status"] = status

	// Marshal the updated map back to JSON
	updatedInfoBytes, err := json.Marshal(dynamicInfo)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBUpdateManualQCData] failed to marshal updated dynamic_user_info. err: %v, userID: %s", err, userID)
		return err
	}

	// Update the users table
	updateQuery := `
    UPDATE users 
    SET 
        dynamic_user_info = $1,
        updated_at = NOW()
    WHERE 
        user_id = $2`

	result, err := psql.Database.ExecContext(ctx, updateQuery, string(updatedInfoBytes), userID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBUpdateManualQCData] failed to update dynamic_user_info. err: %v, userID: %s", err, userID)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBUpdateManualQCData] failed to get rows affected. err: %v, userID: %s", err, userID)
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("no user found with ID: %s", userID)
	}

	return nil
}

func (udr *UserDBRepository) DBUpdateUser(ctx context.Context, tx psql.TxProvider, param DBUpdateUserParams) error {
	if param.UserID == "" {
		logger.WithContext(ctx).Errorf("[DBUpdateUser] empty User ID, userID: %+v", param.UserID)
		return fmt.Errorf("empty User ID")
	}

	query := `
    UPDATE 
        users 
    SET 
    `

	var conditions []string
	var values []interface{}

	if param.PartnerData != "" {
		conditions = append(conditions, "partner_data = ?")
		values = append(values, param.PartnerData)
	}

	if param.DynamicUserInfo != "" {
		conditions = append(conditions, "dynamic_user_info = ?")
		values = append(values, param.DynamicUserInfo)
	}

	if param.Email != "" {
		conditions = append(conditions, "email = ?")
		values = append(values, param.Email)
	}

	if param.IsEmailVerified != nil {
		conditions = append(conditions, "is_email_verified = ?")
		values = append(values, *param.IsEmailVerified)
	}

	if len(conditions) == 0 {
		return fmt.Errorf("no fields to update")
	}

	query += strings.Join(conditions, " , ")
	query += ", updated_at = NOW() WHERE user_id = ? "

	values = append(values, param.UserID)
	query = udr.db.Rebind(query)

	_, err := tx.ExecContext(ctx, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBUpdateUser] failed to update User details by id. err: %v, userID: %+v", err, param.UserID)
		return err
	}

	return nil
}

func (udr *UserDBRepository) DBListUsersByParams(
	ctx context.Context,
	req DBListUsersByParamsRequest,
) ([]DBUserDetailResponse, error) {
	var resp []DBUserDetailResponse

	// Validate that at least one filter condition is provided
	if len(req.UserIDs) == 0 && len(req.SourceEntityIDs) == 0 && len(req.UniqueIDs) == 0 &&
		len(req.Mobiles) == 0 {
		err := errors.New("at least one filter condition is required")
		logger.WithContext(ctx).Errorf("[DBListUsersByParams] no filter conditions provided: %+v", req)
		return nil, err
	}

	// Add performance safeguards
	if len(req.UserIDs) > 100 {
		err := errors.New("userIDs limit cannot exceed 100")
		logger.WithContext(ctx).Errorf("[DBListUsersByParams] userIDs limit too high: %d", len(req.UserIDs))
		return nil, err
	}

	baseQuery := `
	SELECT 
		coalesce(u.user_id::TEXT, '') as user_id,
		coalesce(u.name, '') as name, 
		coalesce(u.email, '') as email, 
		coalesce(u.mobile, '') as mobile,
		coalesce(u.unique_id, '') as unique_id,  
		coalesce(u.gender, -1) as gender,
		coalesce(to_char(u.dob, 'YYYY-MM-DD'), '') as dob, 
		coalesce(u.pincode, '') as pincode,
		coalesce(u.fcm_token, '') as fcm_token,
		coalesce(u.pan, '') as pan,
		coalesce(u.source_entity_id::TEXT, '') as source_entity_id,
		coalesce(u.status, 0) as status,
		coalesce(u.kyc_rule_num, 0) as kyc_rule_num,
		coalesce(to_char(u.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at,
		coalesce(u.created_by, '') as created_by,
		coalesce(to_char(u.updated_at, 'YYYY-MM-DD HH24:MI:SS'), '') as updated_at,
		coalesce(u.updated_by, '') as updated_by,
		coalesce(u.sdk_version, '') as sdk_version,
		coalesce(u.partner_data::TEXT, '') as partner_data,
		coalesce(u.crm_id, '') as crm_id,
		coalesce(u.device_id, '') as device_id,
		coalesce(u.partner_code, '') as partner_code,
		coalesce(u.prospect_no, '') as prospect_no,
		coalesce(u.firm_name, '') as firm_name,
		coalesce(u.source, '') as source,
		coalesce(u.kyc_flow, 0) as kyc_flow,
		coalesce(u.dsa_id::TEXT, '') as dsa_id,
		coalesce(u.journey_config_id::TEXT, '') as journey_config_id,
		coalesce(u.dynamic_user_info::TEXT, '') as dynamic_user_info
	FROM users u`

	whereParts := []string{}
	args := []interface{}{}

	if len(req.UserIDs) > 0 {
		whereParts = append(whereParts, "u.user_id IN (?)")
		args = append(args, req.UserIDs)
	}
	if len(req.SourceEntityIDs) > 0 {
		whereParts = append(whereParts, "u.source_entity_id IN (?)")
		args = append(args, req.SourceEntityIDs)
	}
	if len(req.UniqueIDs) > 0 {
		whereParts = append(whereParts, "u.unique_id IN (?)")
		args = append(args, req.UniqueIDs)
	}
	if len(req.Mobiles) > 0 {
		whereParts = append(whereParts, "u.mobile IN (?)")
		args = append(args, req.Mobiles)
	}
	if req.Status != nil {
		whereParts = append(whereParts, "u.status = ?")
		args = append(args, *req.Status)
	}

	//Build WHERE clause
	query := baseQuery

	if len(whereParts) == 0 {
		err := errors.New("at least one filter condition is required")
		logger.WithContext(ctx).Errorf("[DBListUsersByParams] no filter conditions provided, req: %+v", req)
		return nil, err
	} else {
		query += " WHERE " + strings.Join(whereParts, " AND ")
	}

	// Add ORDER BY for consistent results
	query += " ORDER BY u.created_at DESC"

	// Add LIMIT with default safeguard
	limit := 100 // Default limit
	if req.Limit != nil {
		if *req.Limit > 100 {
			err := errors.New("limit cannot exceed 100")
			logger.WithContext(ctx).Errorf("[DBListUsersByParams] limit too high: %d", *req.Limit)
			return nil, err
		}
		limit = *req.Limit
	}
	query += " LIMIT ?"
	args = append(args, limit)

	// Add OFFSET if provided
	if req.Offset != nil {
		query += " OFFSET ?"
		args = append(args, *req.Offset)
	}

	// Expand IN queries for sqlx.In
	query, args, err := sqlx.In(query, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBListUsersByParams] sqlx.In expansion failed: %v, original query: %s", err, query)
		return nil, fmt.Errorf("query expansion failed: %w", err)
	}

	// Rebind for PostgreSQL driver
	query = udr.db.Rebind(query)

	// Execute query
	if err := udr.db.SelectContext(ctx, &resp, query, args...); err != nil {
		logger.WithContext(ctx).Errorf("[DBListUsersByParams] select failed: %v, query: %s, args: %v, req: %+v", err, query, args, req)
		return nil, fmt.Errorf("database query failed: %w", err)
	}

	for i := range resp {
		var dynamicUserInfo DBUserDynamicInfo
		if resp[i].DynamicUserInfoStr != "" {
			err = json.Unmarshal([]byte(resp[i].DynamicUserInfoStr), &dynamicUserInfo)
			if err != nil {
				logger.WithContext(ctx).Warnf("[DBListUsersByParams] failed to parse dynamic user info. err: %v, userID: %+v", err, resp[i].UserID)
			}
		}
		resp[i].DynamicUserInfo = dynamicUserInfo
	}

	return resp, nil
}

func IsUserCoApplicant(ctx context.Context, userID string) (bool, string) {
	query := `
	select 
	    parent_user_id
	from 
	    multi_user_loan_relations
	where 
	    user_id = $1`
	var parentUserId string
	params := []interface{}{userID}

	err := database.GetContext(ctx, &parentUserId, query, params...)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, ""
		}
		logger.WithContext(ctx).Errorf("[IsUserCoApplicant] Failed to get get parent userId for %v", userID)
		return false, userID
	}
	return true, parentUserId
}
