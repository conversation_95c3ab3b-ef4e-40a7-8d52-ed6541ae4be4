package documents

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"sync"

	"finbox/go-api/authentication"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/s3"
	configsql "finbox/go-api/internal/repository/psql/configmanagement"
	documentsql "finbox/go-api/internal/repository/psql/document"
	loansql "finbox/go-api/internal/repository/psql/loan"
	usersql "finbox/go-api/internal/repository/psql/user"
	"finbox/go-api/internal/service/configmanagement"
	configsrv "finbox/go-api/internal/service/configmanagement"
	"finbox/go-api/internal/service/dropdown"
	dashboardPolicy "finbox/go-api/internal/service/redgatepolicies/dashboard"
	"finbox/go-api/models/dashboard"
	mdashboard "finbox/go-api/models/dashboard"
	_ "finbox/go-api/models/documents"
	"finbox/go-api/thirdparty/redgate"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/kycutils"

	"github.com/samber/lo"
)

var database = db.GetDB()

func GetDocumentsByParamsWithPolicy(ctx context.Context, bearerToken string, req mdashboard.GetDocumentsParam) (mdashboard.CategorizedDocsResponse, error) {
	var (
		lenderUser      authentication.LenderUserStruct
		filteredDocIds  []string
		categoryMap     map[string][]string
		documentReqType string
	)

	userDashboard := ctx.Value("user")
	switch v := userDashboard.(type) {
	case authentication.LenderUserStruct:
		lenderUser = v
	case authentication.MasterDashboardUserStruct:
		// masterUser = v
	default:
		logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] invalid user")
		return mdashboard.CategorizedDocsResponse{}, errors.New(constants.InvalidDashboardUserMessage)
	}

	loanRepo := loansql.NewLoanDBRepository(database)
	loanDetails, err := loanRepo.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{
		LoanApplicationID: req.LoanApplicationID,
	})
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] Failed to fetch loan application for LoanApplicationID: %s, Error: %v", req.LoanApplicationID, err)
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("no loan application exists with ID %s", req.LoanApplicationID)
	}

	// Get user employment type from loan application's primary applicant
	userRepo := usersql.NewUserDBRepository(database)
	userDetails, err := userRepo.DBGetUserByParamsV2(ctx, usersql.DBGetUserInfoParam{
		UserID: loanDetails.UserID,
	})
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] Failed to fetch user info req: %v, Error: %v", req, err)
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("no user exists with ID %s", req.UserID)
	}

	var (
		employmentType      string
		professionalDetails mdashboard.ProfessionalDetails
	)

	err = json.Unmarshal([]byte(userDetails.DynamicUserInfoStr), &professionalDetails)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] Error unmarshalling DynamicUserInfo: %+v, err %v", userDetails.DynamicUserInfoStr, err)
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("failed to marshal dynamic user info for loanID: %s", req.LoanApplicationID)
	}

	professionalDetails.EmploymentType = "Salaried"
	if professionalDetails.EmploymentType == "" {
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("empty employment type for loanID: %s", req.LoanApplicationID)
	}

	employmentType = professionalDetails.EmploymentType

	logger.WithContext(ctx).Infof("[GetDocumentsByParamsWithPolicy] Lender user: %+v", lenderUser)
	if len(req.DocIDs) > 0 {
		filteredDocIds = req.DocIDs
		documentReqType = mdashboard.DocIdsBasedDocumentReqType
	} else {
		configManageemntDBRepository := configsql.NewConfigManagementDBRepository(database)
		configSrvRepository := configsrv.NewConfigManagementSrvRepository(configsrv.ConfigManagementSrvRepository{
			ConfigDBRepositoryProvider: configManageemntDBRepository,
		})
		configRes, err := configSrvRepository.LegacyProvider().GetConfigInfoByParam(ctx, &configmanagement.GetConfigInfoParam{
			ResourceName: "document_dropdown",
		}, configmanagement.ConfigKeyValueMap{
			"lender_id": loanDetails.LenderID,
		})

		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] error in getting config for lenderID:%s, err:%v", loanDetails.LenderID, err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		resData, err := dropdown.GetFilteredDropDownDataRecursive(ctx, configRes.Config, req.CustomCategories, req.ReturnAllLeafNodes)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] error parsing config for customCategory:%+v, err:%v", req.CustomCategories, err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		filteredDocIds, categoryMap, err = extractFilteredDocumentIDs(resData, employmentType)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] error extracting document IDs: %v", err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		logger.WithContext(ctx).Infof("[GetDocumentsByParamsWithPolicy] extracted document IDs: %+v", filteredDocIds)
		documentReqType = mdashboard.CategoryBasedDocumentsReqType
	}

	// Using WaitGroup for parallel database calls
	var (
		wg              sync.WaitGroup
		dashboardResult mdashboard.DocResult
		kycResult       mdashboard.DocResult
	)

	// Add two tasks to the WaitGroup
	wg.Add(2)

	// Goroutine for fetching dashboard docs
	go func() {
		defer wg.Done()
		additionalDocsResp, err := GetDashboardDocumentsWithMedia(ctx, loanDetails.LoanApplicationID, req.UserID)
		dashboardResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(additionalDocsResp), Err: err}
	}()

	// Goroutine for fetching KYC docs based on user type
	go func() {
		defer wg.Done()
		if isCoApplicant(req.UserID) {
			docs, err := kycutils.GetCoApplicantKycDocs(ctx, loanDetails.LenderID, req.UserID)
			kycDataResp := lo.Map(docs, func(item kycutils.CoApplicantKYCData, _ int) kycutils.KYCDataResp {
				return item.KYCDataResp
			})
			kycResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(kycDataResp), Err: err}
		} else {
			docs, err := kycutils.GetKYCDashboardDocs(req.LoanApplicationID)
			kycDataResp := lo.Map(docs, func(item kycutils.LoanKYCDataResp, _ int) kycutils.KYCDataResp {
				return item.KYCDataResp
			})
			kycResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(kycDataResp), Err: err}
		}
	}()

	// Wait for both goroutines to complete
	wg.Wait()

	// Check results for errors
	if dashboardResult.Err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] error fetching dashboard docs: %v", dashboardResult.Err)
	}

	if kycResult.Err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] error fetching KYC docs: %v", kycResult.Err)
	}

	// Combine results
	combinedDocsInfo := combineDocsInfo(dashboardResult.Docs, kycResult.Docs)

	// Filter if needed
	if len(filteredDocIds) > 0 {
		// Convert filter array to map for quick lookup
		docIDSet := lo.SliceToMap(filteredDocIds, func(id string) (string, bool) {
			return id, true
		})

		// Filter the combined docs
		combinedDocsInfo = lo.Filter(combinedDocsInfo, func(doc mdashboard.DocInfo, _ int) bool {
			_, exists := docIDSet[doc.DocumentID]
			return exists
		})
	}

	response, err := makeGetDocumentsResponse(combinedDocsInfo, categoryMap, dashboardResult, documentReqType, req)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] error creating final response: err: %v", err)
		return mdashboard.CategorizedDocsResponse{}, errors.New(constants.GenericInternalIssuesMessage)
	}

	finalResponse, err := dashboardPolicy.ExecuteGetDocumentPolicy(ctx, redgate.ExecutePolicyRequest{
		AppClientID:       loanDetails.LenderID,
		LoanApplicationID: req.LoanApplicationID,
		UserID:            loanDetails.UserID,
		BearerToken:       bearerToken,
	}, &response)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParamsWithPolicy] error executing document policy: err: %v", err)
		return mdashboard.CategorizedDocsResponse{}, errors.New(constants.GenericInternalIssuesMessage)
	}

	return *finalResponse, nil
}

func GetDocumentsByParams(ctx context.Context, req mdashboard.GetDocumentsParam) (mdashboard.CategorizedDocsResponse, error) {
	var (
		lenderUser      authentication.LenderUserStruct
		filteredDocIds  []string
		categoryMap     map[string][]string
		documentReqType string
	)

	userDashboard := ctx.Value("user")
	switch v := userDashboard.(type) {
	case authentication.LenderUserStruct:
		lenderUser = v
	case authentication.MasterDashboardUserStruct:
		// masterUser = v
	default:
		logger.WithContext(ctx).Errorf("[GetDocumentsByParams] invalid user")
		return mdashboard.CategorizedDocsResponse{}, errors.New(constants.InvalidDashboardUserMessage)
	}

	loanRepo := loansql.NewLoanDBRepository(database)
	loanDetails, err := loanRepo.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{
		LoanApplicationID: req.LoanApplicationID,
	})
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParams] Failed to fetch loan application for LoanApplicationID: %s, Error: %v", req.LoanApplicationID, err)
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("no loan application exists with ID %s", req.LoanApplicationID)
	}

	// Get user employment type from loan application's primary applicant
	userRepo := usersql.NewUserDBRepository(database)
	userDetails, err := userRepo.DBGetUserByParamsV2(ctx, usersql.DBGetUserInfoParam{
		UserID: loanDetails.UserID,
	})
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParams] Failed to fetch user info req: %v, Error: %v", req, err)
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("no user exists with ID %s", req.UserID)
	}

	var (
		employmentType      string
		professionalDetails mdashboard.ProfessionalDetails
	)

	err = json.Unmarshal([]byte(userDetails.DynamicUserInfoStr), &professionalDetails)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParams] Error unmarshalling DynamicUserInfo: %+v, err %v", userDetails.DynamicUserInfoStr, err)
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("failed to marshal dynamic user info for loanID: %s", req.LoanApplicationID)
	}

	professionalDetails.EmploymentType = "Salaried"
	if professionalDetails.EmploymentType == "" {
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("empty employment type for loanID: %s", req.LoanApplicationID)
	}

	employmentType = professionalDetails.EmploymentType

	logger.WithContext(ctx).Infof("[GetDocumentsByParams] Lender user: %+v", lenderUser)
	if len(req.DocIDs) > 0 {
		filteredDocIds = req.DocIDs
		documentReqType = mdashboard.DocIdsBasedDocumentReqType
	} else {
		configManageemntDBRepository := configsql.NewConfigManagementDBRepository(database)
		configSrvRepository := configsrv.NewConfigManagementSrvRepository(configsrv.ConfigManagementSrvRepository{
			ConfigDBRepositoryProvider: configManageemntDBRepository,
		})
		configRes, err := configSrvRepository.LegacyProvider().GetConfigInfoByParam(ctx, &configmanagement.GetConfigInfoParam{
			ResourceName: "document_dropdown",
		}, configmanagement.ConfigKeyValueMap{
			"lender_id": loanDetails.LenderID,
		})

		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsByParams] error in getting config for lenderID:%s, err:%v", loanDetails.LenderID, err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		resData, err := dropdown.GetFilteredDropDownDataRecursive(ctx, configRes.Config, req.CustomCategories, req.ReturnAllLeafNodes)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsByParams] error parsing config for customCategory:%+v, err:%v", req.CustomCategories, err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		filteredDocIds, categoryMap, err = extractFilteredDocumentIDs(resData, employmentType)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsByParams] error extracting document IDs: %v", err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		logger.WithContext(ctx).Infof("[GetDocumentsByParams] extracted document IDs: %+v", filteredDocIds)
		documentReqType = mdashboard.CategoryBasedDocumentsReqType
	}

	// Using WaitGroup for parallel database calls
	var (
		wg              sync.WaitGroup
		dashboardResult mdashboard.DocResult
		kycResult       mdashboard.DocResult
	)

	// Add two tasks to the WaitGroup
	wg.Add(2)

	// Goroutine for fetching dashboard docs
	go func() {
		defer wg.Done()
		additionalDocsResp, err := GetDashboardDocumentsWithMedia(ctx, loanDetails.LoanApplicationID, req.UserID)
		dashboardResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(additionalDocsResp), Err: err}
	}()

	// Goroutine for fetching KYC docs based on user type
	go func() {
		defer wg.Done()
		if isCoApplicant(req.UserID) {
			docs, err := kycutils.GetCoApplicantKYCDashboardDocs(req.UserID)
			kycDataResp := lo.Map(docs, func(item kycutils.CoApplicantKYCData, _ int) kycutils.KYCDataResp {
				return item.KYCDataResp
			})
			kycResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(kycDataResp), Err: err}
		} else {
			docs, err := kycutils.GetKYCDashboardDocs(req.LoanApplicationID)
			kycDataResp := lo.Map(docs, func(item kycutils.LoanKYCDataResp, _ int) kycutils.KYCDataResp {
				return item.KYCDataResp
			})
			kycResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(kycDataResp), Err: err}
		}
	}()

	// Wait for both goroutines to complete
	wg.Wait()

	// Check results for errors
	if dashboardResult.Err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParams] error fetching dashboard docs: %v", dashboardResult.Err)
	}

	if kycResult.Err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParams] error fetching KYC docs: %v", kycResult.Err)
	}

	// Combine results
	combinedDocsInfo := combineDocsInfo(dashboardResult.Docs, kycResult.Docs)

	// Filter if needed
	if len(filteredDocIds) > 0 {
		// Convert filter array to map for quick lookup
		docIDSet := lo.SliceToMap(filteredDocIds, func(id string) (string, bool) {
			return id, true
		})

		// Filter the combined docs
		combinedDocsInfo = lo.Filter(combinedDocsInfo, func(doc mdashboard.DocInfo, _ int) bool {
			_, exists := docIDSet[doc.DocumentID]
			return exists
		})
	}

	response, err := makeGetDocumentsResponse(combinedDocsInfo, categoryMap, dashboardResult, documentReqType, req)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsByParams] error creating final response: err: %v", err)
		return mdashboard.CategorizedDocsResponse{}, errors.New(constants.GenericInternalIssuesMessage)
	}
	return response, nil
}

func makeGetDocumentsResponse(combinedDocsInfo []mdashboard.DocInfo, categoryMap map[string][]string, dashboardDocs mdashboard.DocResult, documentReqType string, req mdashboard.GetDocumentsParam) (mdashboard.CategorizedDocsResponse, error) {

	// Create the categorized response
	var response mdashboard.CategorizedDocsResponse

	switch documentReqType {
	case mdashboard.CategoryBasedDocumentsReqType:
		response = createCategorizedResponse(combinedDocsInfo, categoryMap)
		response = handleAdditionalDocsSeparately(response, dashboardDocs, categoryMap)

		if len(req.FilteredCategories) == 0 {
			return response, nil
		}

		response = mdashboard.CategorizedDocsResponse{
			CategorisedData: lo.PickByKeys(response.CategorisedData, req.FilteredCategories),
		}

	case mdashboard.DocIdsBasedDocumentReqType:
		response = mdashboard.CategorizedDocsResponse{
			FilteredData: mdashboard.DocResp{
				DocList: combinedDocsInfo,
			},
		}
	}

	return response, nil
}

// combineDocsInfo combines two lists of documents, removing duplicates by DocID
// If a document with the same DocID exists in both lists, the one from primaryDocs is kept
func combineDocsInfo(docLists ...[]mdashboard.DocInfo) []mdashboard.DocInfo {
	// If there are no lists, return an empty result
	if len(docLists) == 0 {
		return []mdashboard.DocInfo{}
	}

	// Use a map to track which document IDs we've already seen
	seenDocIDs := make(map[string]bool)

	// Initialize the result slice with an appropriate capacity
	totalCapacity := 0
	for _, list := range docLists {
		totalCapacity += len(list)
	}
	result := make([]mdashboard.DocInfo, 0, totalCapacity)

	// Process each list in order of priority
	for _, docs := range docLists {
		for _, doc := range docs {
			// Only add the document if we haven't seen its ID before
			if !seenDocIDs[doc.DocumentID] {
				seenDocIDs[doc.DocumentID] = true
				result = append(result, doc)
			}
		}
	}

	return result
}

func extractFilteredDocumentIDs(resData interface{}, employmentType string) ([]string, map[string][]string, error) {
	// resData should be a slice of maps from the recursive result
	resultSlice, ok := resData.([]interface{})
	if !ok {
		return nil, nil, fmt.Errorf("unexpected result type: %T", resData)
	}

	docIDs := make([]string, 0, len(resultSlice))
	categoryMap := make(map[string][]string)

	for _, item := range resultSlice {
		// Each item should be a map with "value", "data", and "path" keys
		mapItem, ok := item.(map[string]interface{})
		if !ok {
			return nil, nil, fmt.Errorf("unexpected item type: %T", item)
		}

		// Extract the "value" field
		value, ok := mapItem["value"]
		if !ok {
			return nil, nil, fmt.Errorf("item does not contain 'value' field")
		}

		// Extract the "path" field
		pathInterface, ok := mapItem["path"]
		if !ok {
			return nil, nil, fmt.Errorf("item does not contain 'path' field")
		}

		// Handle different path types
		var pathSlice []string

		// Check if it's already a string slice
		if strSlice, ok := pathInterface.([]string); ok {
			pathSlice = strSlice
		} else if interfaceSlice, ok := pathInterface.([]interface{}); ok {
			// Convert interface slice to string slice
			pathSlice = make([]string, 0, len(interfaceSlice))
			for _, p := range interfaceSlice {
				if pStr, isStr := p.(string); isStr {
					pathSlice = append(pathSlice, pStr)
				}
			}
		} else {
			return nil, nil, fmt.Errorf("path is not a recognized slice type: %T", pathInterface)
		}

		// Filter by employment type if specified
		if employmentType != "" {
			// Check if employment type is in the path
			hasEmploymentType := false
			for _, p := range pathSlice {
				if p == employmentType {
					hasEmploymentType = true
					break
				}
			}

			// Skip this item if it doesn't contain the required employment type
			if !hasEmploymentType {
				continue
			}
		}

		// Convert value to string
		valueStr, ok := value.(string)
		if !ok {
			return nil, nil, fmt.Errorf("value is not a string: %T", value)
		}

		// Add to flat list
		docIDs = append(docIDs, valueStr)

		// Add to category map if we have a path
		if len(pathSlice) > 0 {
			// Get top-level category from the path
			category := pathSlice[0]
			if _, exists := categoryMap[category]; !exists {
				categoryMap[category] = make([]string, 0)
			}
			categoryMap[category] = append(categoryMap[category], valueStr)
		}
	}

	return docIDs, categoryMap, nil
}

// createCategorizedResponse organizes documents by category and filters empty categories
func createCategorizedResponse(docs []mdashboard.DocInfo, categoryMap map[string][]string) mdashboard.CategorizedDocsResponse {
	response := mdashboard.CategorizedDocsResponse{
		CategorisedData: make(map[string]mdashboard.DocResp),
	}

	// If no category map, just return empty response
	if len(categoryMap) == 0 {
		return response
	}

	// Create a map of docIDs to DocsInfo for efficient lookup
	docMap := make(map[string]mdashboard.DocInfo)
	for _, doc := range docs {
		docMap[doc.DocumentID] = doc
	}

	// Organize docs by category
	for category, docIDs := range categoryMap {
		// Create an array to hold documents for this category
		categoryDocs := make([]mdashboard.DocInfo, 0, len(docIDs))

		// Find matching documents
		for _, docID := range docIDs {
			if doc, exists := docMap[docID]; exists {
				categoryDocs = append(categoryDocs, doc)
			}
		}

		// Only add the category if it has at least one document
		if len(categoryDocs) > 0 {
			response.CategorisedData[category] = mdashboard.DocResp{
				DocList: categoryDocs,
			}
		}
	}

	return response
}

// mapKYCDataToDocResp maps a KYCDataResp to a DocResp
func mapKYCDataToDocResp(kycData kycutils.KYCDataResp) mdashboard.DocInfo {
	return mdashboard.DocInfo{
		FrontMediaID:     kycData.FrontMediaID,
		BackMediaID:      kycData.BackMediaID,
		DocType:          kycData.DocType,
		DocumentName:     kycData.DocumentName,
		Status:           kycData.Status,
		StatusText:       kycData.StatusText,
		DocumentID:       kycData.DocumentID,
		RejectionReason:  kycData.RejectionReason,
		ReviewStatus:     kycData.ReviewStatus,
		ReviewStatusText: kycData.ReviewStatusText,
		IsEditable:       kycData.IsEditable,
		Password:         kycData.Password,
		DocID:            kycData.DocID,
		CreatedAt:        kycData.CreatedAt,
	}
}

// mapKYCDataListToDocRespList maps a slice of KYCDataResp to a slice of DocResp
func mapKYCDataListToDocRespList(kycDataList []kycutils.KYCDataResp) []mdashboard.DocInfo {
	result := make([]mdashboard.DocInfo, len(kycDataList))
	for i, kycData := range kycDataList {
		result[i] = mapKYCDataToDocResp(kycData)
	}
	return result
}

/*
 * We required this special handling for additional docs because for one documentID we can have multiple
 * media's present also, in previous we didn't store documentID in dashboard_docs and media table and we
 * need to return that data as well. Hence we require separate handling.
 * Below we are handling the scenario if the response created has "Additional Docs" key present, then we
 * return all data that is returned from the db function.
 */
func handleAdditionalDocsSeparately(response mdashboard.CategorizedDocsResponse, dashboardResult mdashboard.DocResult, categoryMap map[string][]string) mdashboard.CategorizedDocsResponse {

	// Check if "Additional Docs" category already exists in the map
	if categoryMap[constants.AdditionalDocs] == nil {
		return response
	}

	// Early return if dashboardResult has an error
	if dashboardResult.Err != nil {
		return response
	}

	// Filter to keep ONLY documents with empty DocumentID or matching additionalDocsDocumentID using lo library
	filteredDocs := lo.Filter(dashboardResult.Docs, func(doc mdashboard.DocInfo, _ int) bool {
		return doc.DocumentID == "" || doc.DocumentID == constants.AdditionalDocCategoryDocumentID
	})

	// Only update if filteredDocs is not empty
	if len(filteredDocs) > 0 {
		response.CategorisedData[constants.AdditionalDocs] = mdashboard.DocResp{DocList: filteredDocs}
	} else {
		delete(response.CategorisedData, constants.AdditionalDocs)
	}

	return response
}

func isCoApplicant(userID string) bool {
	query := `select user_id
			    from multi_user_loan_relations
				where user_id = $1`
	var result string
	params := []interface{}{userID}

	err := database.Get(&result, query, params...)
	if err != nil {
		if err == sql.ErrNoRows {
			return false
		}
		logger.WithUser(userID).Errorln(err)
		return false
	}
	return true
}

// GetAdditionalDashboardDocuments is the service level function that calls the DB function and processes the results
func GetAdditionalDashboardDocuments(ctx context.Context, loanApplicationID string, entityTypes []string) ([]dashboard.DocsInfo, error) {
	// Call the DB function
	param := &documentsql.DBGetDashboardDocsParam{
		LoanApplicationID: loanApplicationID,
		EntityTypes:       entityTypes,
	}

	docsInfo, err := documentsql.DBGetAdditionalDashboardDocuments(ctx, param)
	if err != nil {
		return nil, err
	}

	// Process the documents with the common function
	return processDocsInfo(docsInfo, loanApplicationID, "additional_docs")
}

func processDocsInfo(docsInfo []dashboard.DBDocsInfo, loanApplicationNo string, fileNamePrefix string) ([]dashboard.DocsInfo, error) {
	currentIndex := -1
	var docsInfoObj []dashboard.DocsInfo
	docMap := make(map[string]int)

	for i, docInfo := range docsInfo {
		extension := filepath.Ext(docInfo.MediaPath)
		customFileName := fmt.Sprintf("%s_%s_%d%s", loanApplicationNo, fileNamePrefix, i+1, extension)
		if index, found := docMap[docInfo.DocID]; found {
			doc := docsInfoObj[index]
			doc.DocType = docInfo.DocType
			urls := doc.URLs
			urls = append(urls, s3.GetPresignedURLS3CustomName(docInfo.MediaPath, 60, customFileName))
			docsInfoObj[index].URLs = urls
		} else {
			docsInfoObj = append(docsInfoObj, dashboard.DocsInfo{
				DocID:        docInfo.DocID,
				DocType:      docInfo.DocType,
				Comment:      docInfo.Comment,
				URLs:         []string{s3.GetPresignedURLS3CustomName(docInfo.MediaPath, 60, customFileName)},
				ReviewStatus: constants.AdditionalKYCDocStatusText[docInfo.ReviewStatus],
			})
			currentIndex += 1
			docMap[docInfo.DocID] = currentIndex
		}
	}
	if docsInfoObj == nil {
		return []dashboard.DocsInfo{}, nil
	}

	return docsInfoObj, nil
}

func GetAdditionalKYCDocs(ctx context.Context, loanApplicationID, lenderID, userID string) ([]kycutils.LoanKYCDataResp, error) {

	additionalDocs, err := documentsql.GetAdditionalKYCDocs(ctx, loanApplicationID, lenderID, userID)
	if err != nil {
		return nil, err
	}

	// Convert to DocumentDataInterface
	docsInterface := make([]documentsql.DocumentDataInterface, len(additionalDocs))
	for i, doc := range additionalDocs {
		docsInterface[i] = documentsql.DocumentDataInterface{
			DocumentID:       doc.DocumentID,
			DocumentCategory: doc.DocumentCategory,
			DocumentName:     doc.DocumentName,
			Path:             doc.Path,
			ReviewStatus:     doc.ReviewStatus,
			Password:         doc.Password,
			CreatedAt:        doc.CreatedAt,
		}
	}

	kycDataResp, err := processDocuments(docsInterface)
	if err != nil {
		return nil, err
	}

	return lo.Map(kycDataResp, func(item kycutils.KYCDataResp, _ int) kycutils.LoanKYCDataResp {
		return kycutils.LoanKYCDataResp{
			KYCDataResp: item,
		}
	}), nil
}

func GetDashboardDocumentsWithMedia(ctx context.Context, loanApplicationID, userID string) ([]kycutils.KYCDataResp, error) {
	dashboardDocs, err := documentsql.DBGetDashboardDocumentsWithMedia(ctx, &documentsql.DBGetDashboardDocumentsParam{
		LoanApplicationID: loanApplicationID,
	})
	dashboardDocs = lo.Filter(dashboardDocs, func(doc documentsql.DBGetUserDocumentsResponse, _ int) bool {
		return doc.UserID == userID
	})
	if err != nil {
		return nil, err
	}

	// Convert to DocumentDataInterface
	docsInterface := make([]documentsql.DocumentDataInterface, len(dashboardDocs))
	for i, doc := range dashboardDocs {
		docsInterface[i] = documentsql.DocumentDataInterface{
			DocumentID:       doc.DocumentID,
			DocumentCategory: doc.DocumentCategory,
			DocumentName:     doc.DocumentName,
			Path:             doc.Path,
			ReviewStatus:     doc.ReviewStatus,
			Password:         doc.Password,
			DocID:            doc.DocID,
			CreatedAt:        doc.CreatedAt,
		}
	}

	return processDocuments(docsInterface)
}

// Common processing function that works with the interface struct
func processDocuments(docs []documentsql.DocumentDataInterface) ([]kycutils.KYCDataResp, error) {
	var result []kycutils.KYCDataResp
	for _, doc := range docs {
		url := s3.GetPresignedURLS3(doc.Path, 60)
		statusText := constants.AdditionalKYCDocStatusText[doc.ReviewStatus]
		rejectionReason := ""
		if general.InArr(doc.ReviewStatus, []int{constants.DocumentNotVerified, constants.DocumentUnderReview}) {
			rejectionReason = statusText
		}
		isEditable := true
		if doc.DocumentName == constants.DocumentNameAdditionalDocUANFromAPI {
			isEditable = false
		}
		result = append(result,
			kycutils.KYCDataResp{
				FrontMediaID:    url,
				DocType:         doc.DocumentCategory,
				DocumentName:    doc.DocumentName,
				DocumentID:      doc.DocumentID,
				RejectionReason: rejectionReason,
				StatusText:      statusText,
				IsEditable:      isEditable,
				Password:        doc.Password,
				DocID:           doc.DocID,
				CreatedAt:       doc.CreatedAt,
			})
	}
	return result, nil
}
