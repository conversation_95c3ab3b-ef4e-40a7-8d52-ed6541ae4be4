package mfl

import (
	"context"
	"fmt"
	"strings"

	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/service/myqueue"
	dashboardPolicy "finbox/go-api/internal/service/redgatepolicies/dashboard"
	"finbox/go-api/thirdparty/redgate"

	"github.com/jmoiron/sqlx"
)

type MFLLAPMyQueueFetcher struct {
	DB                 *sqlx.DB
	WorkFlowMap        map[string]string
	ReverseWorkflowMap map[string]string
}

func NewMFLLAPMyQueueFetcher(val MFLLAPMyQueueFetcher) *MFLLAPMyQueueFetcher {
	return &MFLLAPMyQueueFetcher{
		DB:                 val.DB,
		WorkFlowMap:        val.WorkFlowMap,
		ReverseWorkflowMap: val.ReverseWorkflowMap,
	}
}

func (m *MFLLAPMyQueueFetcher) MyQueueLenderDashboard(ctx context.Context, params *myqueue.MyQueueLenderDashboardParams) (*myqueue.MyQueueResponse, error) {
	// First execute the my queue policy
	var policy *dashboardPolicy.GetMyQueuePolicy
	if params.BearerToken != "" && params.LoggedInUserID != "" {

		policyRequest := &redgate.ExecutePolicyRequest{
			PolicyName:  "MYQUEUE_WORKFLOW_PERMISSIONS",
			AppClientID: params.LenderID,
			AppID:       constants.AppIdLendingDashboard,
			BearerToken: params.BearerToken,
			UserID:      params.LoggedInUserID,
		}

		var err error
		policy, err = dashboardPolicy.ExecuteMyQueuePolicy(ctx, policyRequest)
		if err != nil {
			logger.WithContext(ctx).Errorf("[MyQueueLenderDashboard] failed to execute policy, continuing without filtering. err: %v, params: %+v", err, params)
			return nil, err
		}
	}

	if policy == nil || policy.Data == nil {
		logger.WithContext(ctx).Warnf("[MyQueueLenderDashboard] policy is nil, continuing without filtering. params: %+v", params)
		return nil, fmt.Errorf("policy is nil")
	}

	for stateName, stateData := range policy.Data {
		if stateData.IsVisible {
			params.WorkflowStates = append(params.WorkflowStates, stateName)
		}

	}

	// Get total count
	totalCount, err := m.getTotalCount(ctx, params)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MyQueueLenderDashboard] failed to get total count. err: %v, params: %+v", err, params)
		return nil, err
	}

	// Get queue details with pagination
	queueDetails, hasNextPage, err := m.getQueueDetails(ctx, params)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MyQueueLenderDashboard] failed to get queue details. err: %v, params: %+v", err, params)
		return nil, err
	}

	// Create the original response
	originalResponse := &myqueue.MyQueueResponse{
		TotalCount:          totalCount,
		LenderQueueDetails:  queueDetails,
		IsNextPageAvailable: hasNextPage,
	}

	// Apply policy if available

	return dashboardPolicy.ProcessMyQueuePermissions(ctx, policy, originalResponse), nil
}

// getTotalCount fetches the total count of records matching the criteria
func (m *MFLLAPMyQueueFetcher) getTotalCount(ctx context.Context, params *myqueue.MyQueueLenderDashboardParams) (int, error) {
	baseQuery, conditions, args, err := m.buildBaseQueryAndConditions(ctx, params)
	if err != nil {
		return 0, err
	}

	// Build COUNT query
	countQuery := `SELECT COUNT(DISTINCT la.loan_application_id) ` + baseQuery
	if len(conditions) > 0 {
		countQuery += " AND " + strings.Join(conditions, " AND ")
	}
	countQuery = m.DB.Rebind(countQuery)

	var totalCount int
	err = m.DB.GetContext(ctx, &totalCount, countQuery, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[getTotalCount] failed to get total count. query: %v, args: %v, err: %v", countQuery, args, err)
		return 0, err
	}

	return totalCount, nil
}

// getQueueDetails fetches the paginated queue details
func (m *MFLLAPMyQueueFetcher) getQueueDetails(ctx context.Context, params *myqueue.MyQueueLenderDashboardParams) ([]myqueue.LenderQueueDetail, bool, error) {
	// Validate pagination parameters
	if params.Page < 0 || params.PageSize < 0 {
		logger.WithContext(ctx).Errorf("[getQueueDetails] incorrect pagination params: %v", params)
		return nil, false, fmt.Errorf("invalid pagination parameters")
	}

	baseQuery, conditions, args, err := m.buildBaseQueryAndConditions(ctx, params)
	if err != nil {
		return nil, false, err
	}

	// Build SELECT query with pagination
	selectQuery := `
		SELECT DISTINCT ON (la.loan_application_id)
			COALESCE(la.loan_application_id::TEXT, '') AS loanApplicationID,
			COALESCE(la.loan_application_no, '') AS loanApplicationNo,
			COALESCE(la.status, -1) AS status,
			COALESCE(la.kyc_status, -1) AS kycStatus,
			COALESCE(la.created_at::TEXT, '') AS receivedAt,
			COALESCE(u.name, '') AS name,
			COALESCE(u.email, '') AS email,
			COALESCE(u.mobile, '') AS mobile,
			COALESCE(wi.assigned_to, '') AS assignedTo,
			COALESCE(wi.id::TEXT, '') AS workflowInstanceID,
			COALESCE(wi.current_state, '') AS currentState,
			COALESCE(wi.updated_at::TEXT, '') AS assignedAt,
			COALESCE(u.dynamic_user_info::jsonb->>'source_type','') AS sourceType,
			COALESCE(u.dynamic_user_info::jsonb->'loanBranchDetails'->>'branchCode','') AS branchCode,
			COALESCE(u.dynamic_user_info::jsonb->'loanBranchDetails'->>'branchName','') AS branchName,
			COALESCE(u.dynamic_user_info::jsonb->'loanBranchDetails'->>'stateLabel','') AS state,
			COALESCE(u.dynamic_user_info::jsonb->'loanBranchDetails'->>'zone','') AS zone,
			COALESCE(u.dynamic_user_info::jsonb->'loanBranchDetails'->>'cluster','') AS cluster,
			COALESCE(tt.name, '') AS category
	` + baseQuery

	if len(conditions) > 0 {
		selectQuery += " AND " + strings.Join(conditions, " AND ")
	}

	// Add pagination
	offset := (params.Page - 1) * params.PageSize
	limit := params.PageSize + 1 // Fetch extra row for "has next page" logic

	argsWithPagination := append(append([]interface{}{}, args...), limit, offset)
	selectQuery += " LIMIT ? OFFSET ?"
	selectQuery = m.DB.Rebind(selectQuery)

	logger.WithContext(ctx).Infof("[getQueueDetails] final query: %v, args: %v", selectQuery, argsWithPagination)

	var dbResp []myqueue.LenderQueueDetail
	err = m.DB.SelectContext(ctx, &dbResp, selectQuery, argsWithPagination...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[getQueueDetails] failed to fetch data. query: %v, err: %v", selectQuery, err)
		return nil, false, err
	}

	// Determine if there's a next page and return appropriate slice
	var hasNextPage bool
	if len(dbResp) > params.PageSize {
		hasNextPage = true
		dbResp = dbResp[:params.PageSize]
	}

	return dbResp, hasNextPage, nil
}

// buildBaseQueryAndConditions builds the base query and conditions used by both count and select queries
func (m *MFLLAPMyQueueFetcher) buildBaseQueryAndConditions(ctx context.Context, params *myqueue.MyQueueLenderDashboardParams) (string, []string, []interface{}, error) {
	// Map workflow types
	var workDBTypes []string
	for _, val := range params.WorkFlowTypes {
		if value, ok := m.WorkFlowMap[val]; ok {
			workDBTypes = append(workDBTypes, value)
		}
	}

	// Base query
	baseQuery := `
		FROM 
			workflow_instances wi
		LEFT JOIN tasks t
			ON wi.identifier_id::uuid = t.id::uuid
		JOIN task_types tt
			ON t.type_id = tt.id
		LEFT JOIN loan_application la
	    	ON la.loan_application_id = t.identifier_id
		JOIN users u
			ON la.user_id = u.user_id
		WHERE 1=1
	`

	// Build dynamic conditions
	var conditions []string
	var args []interface{}

	// Branch code condition
	getBranchCodeCondition, err := m.withBranchCodeCondition(params)
	if err != nil {
		logger.WithContext(ctx).Errorf("[buildBaseQueryAndConditions] failed to build branch code condition. err: %v", err)
		return "", nil, nil, err
	}
	if getBranchCodeCondition != nil && len(getBranchCodeCondition.Condition) > 0 {
		conditions = append(conditions, getBranchCodeCondition.Condition)
		args = append(args, getBranchCodeCondition.Args...)
	}

	// Assignee user condition
	if cond := myqueue.WithAssigneeUserCondition(params); cond != nil {
		conditions = append(conditions, cond.Condition)
		args = append(args, cond.Args...)
	}

	// Workflow state condition
	cond, _ := myqueue.GetWorkflowStateCondition(params)
	if cond != nil {
		conditions = append(conditions, cond.Condition)
		args = append(args, cond.Args...)
	}

	// custom filter condition
	if cond := myqueue.GetCustomFilterCondition(params); cond != nil {
		conditions = append(conditions, cond.Condition)
		args = append(args, cond.Args...)
	}

	// Task types condition
	getTaskTypesCondition, err := withTaskTypes(workDBTypes)
	if err != nil {
		logger.WithContext(ctx).Errorf("[buildBaseQueryAndConditions] failed to build task types condition. err: %v", err)
		return "", nil, nil, err
	}
	if getTaskTypesCondition != nil && len(getTaskTypesCondition.Condition) > 0 {
		conditions = append(conditions, getTaskTypesCondition.Condition)
		args = append(args, getTaskTypesCondition.Args...)
	}

	return baseQuery, conditions, args, nil
}

// withBranchCodeCondition handles branch code filtering
func (m *MFLLAPMyQueueFetcher) withBranchCodeCondition(params *myqueue.MyQueueLenderDashboardParams) (*myqueue.QueryCondition, error) {
	if params != nil && len(params.BranchCodes) == 0 {
		return nil, nil
	}

	query, args, err := sqlx.In("(u.dynamic_user_info::jsonb->'loanBranchDetails'->>'branchCode') IN (?)", params.BranchCodes)
	if err != nil {
		return nil, err
	}

	return &myqueue.QueryCondition{
		Condition: query,
		Args:      args,
	}, nil
}

// withTaskTypes handles task type filtering
func withTaskTypes(taskTypes []string) (*myqueue.QueryCondition, error) {
	if len(taskTypes) == 0 {
		taskTypes = []string{MFLLAPWorkFlowPMain}
	}

	query, args, err := sqlx.In(`( tt.name ) IN (?)`, taskTypes)
	if err != nil {
		return nil, err
	}

	return &myqueue.QueryCondition{
		Condition: query,
		Args:      args,
	}, nil
}
