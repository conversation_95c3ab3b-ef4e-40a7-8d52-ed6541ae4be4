package myqueue

// MyQueueLenderDashboardParams request and response
type MyQueueLenderDashboardParams struct {
	BranchCodes     []string
	WorkFlowTypes   []string
	CurrentAssignee string
	WorkflowStates  []string
	LenderID        string
	LoggedInUserID  string
	BearerToken     string
	DashboardType   string
	Page            int
	PageSize        int
	SerachType      string
	SerachQuery     string
}

type MyQueueResponse struct {
	LenderQueueDetails  []LenderQueueDetail `json:"lenderQueueDetails"`
	IsNextPageAvailable bool                `json:"isNextPageAvailable"`
	TotalCount          int                 `json:"totalCount"`
}

type LenderQueueDetail struct {
	WorkflowType       string      `json:"workflowType"`
	AssignedGroup      string      `json:"assignedGroup"`
	LoanApplicationID  string      `json:"loanApplicationID"`
	LoanApplicationNo  string      `json:"loanApplicationNo"`
	KYCStatus          int         `json:"kycStatus"`
	Status             int         `json:"status"`
	ReceivedAt         string      `json:"receivedAt"`
	Name               string      `json:"name"`
	AssignedTo         string      `json:"assignedTo"`
	AssignedAt         string      `json:"assignedAt"`
	WorkflowInstanceID string      `json:"workflowInstanceID"`
	CurrentState       string      `json:"currentState"`
	Category           string      `json:"category"`
	SourceType         string      `json:"sourceType"`
	BranchCode         string      `json:"branchCode"`
	BranchName         string      `json:"branchName"`
	Zone               string      `json:"zone"`
	Cluster            string      `json:"cluster"`
	State              string      `json:"state"`
	Email              string      `json:"email"`
	Mobile             string      `json:"mobile"`
	Permissions        Permissions `json:"permissions"`
}

type Permissions struct {
	Actions map[string]interface{} `json:"actions"`
}

// my queue variables
var (
	myqueueRegistry = make(map[string]IMyQueueFetcher)
	defaultFetcher  = &DefaultMyQueueFetcher{} // Default implementation
)

type DefaultMyQueueFetcher struct{}

// QueryCondition represents a SQL condition with its arguments
type QueryCondition struct {
	Condition string
	Args      []interface{}
}

// errors

var (
	ErrQueueFetcherNotFound = "Queue fetcher not found for the given lender ID"
)
