package dashboard

import (
	"context"
	"encoding/json"

	"finbox/go-api/authorization/authProviders"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/service/myqueue"
	"finbox/go-api/thirdparty/redgate"
)

// ExecuteMyQueuePolicy executes the MYQUEUE_WORKFLOW_PERMISSIONS policy
func ExecuteMyQueuePolicy(ctx context.Context, req *redgate.ExecutePolicyRequest) (resp *GetMyQueuePolicy, err error) {
	clientAuthorizer := authProviders.NewAuthorizer(req.AppClientID)
	if clientAuthorizer == nil {
		logger.WithContext(ctx).Warnf("[ExecuteMyQueuePolicy] no authorizer found for req: %+v", req)
		return nil, nil
	}

	workflowData := map[string]interface{}{
		"workflow": "main",
	}

	authorizeUserRequest := redgate.ExecutePolicyRequest{
		PolicyName:  "MYQUEUE_WORKFLOW_PERMISSIONS",
		AppClientID: req.AppClientID,
		AppID:       constants.AppIdLendingDashboard,
		BearerToken: req.BearerToken,
		UserID:      req.UserID,
		Data:        workflowData,
	}

	authResp, err := clientAuthorizer.ExecutePolicyWithoutUserInfo(ctx, authorizeUserRequest)
	if err != nil {
		logger.WithContext(ctx).Errorf("[ExecuteMyQueuePolicy] failed to execute policy for req: %+v, error: %v", req, err)
		return nil, err
	}

	data, err := json.Marshal(authResp)
	if err != nil {
		logger.WithContext(ctx).Errorf("[ExecuteMyQueuePolicy] failed to marshal policy response for req: %+v, error:%v", req, err)
		return nil, err
	}

	logger.WithContext(ctx).Infof("[ExecuteMyQueuePolicy] policy response data: %s, request: %+v", string(data), authorizeUserRequest)

	var myQueuePolicyResponse GetMyQueuePolicy
	err = json.Unmarshal(data, &myQueuePolicyResponse)
	if err != nil {
		logger.WithContext(ctx).Errorf("[ExecuteMyQueuePolicy] failed to unmarshal policy for req: %+v, error:%v", req, err)
		return nil, err
	}

	return &myQueuePolicyResponse, nil
}

// ProcessMyQueuePermissions applies the policy to filter and modify MyQueueResponse
func ProcessMyQueuePermissions(ctx context.Context, policy *GetMyQueuePolicy, response *myqueue.MyQueueResponse) *myqueue.MyQueueResponse {
	if policy == nil || response == nil {
		logger.WithContext(ctx).Warnf("[ProcessMyQueuePermissions] policy or response is nil, returning original response")
		return response
	}

	var filteredQueueDetails []myqueue.LenderQueueDetail

	// Process each queue detail
	for _, queueDetail := range response.LenderQueueDetails {
		// Check if the current state exists in the policy
		if stateMetadata, exists := policy.Data[queueDetail.CurrentState]; exists {
			// Check if the state is visible
			if stateMetadata.IsVisible {
				// Create a copy of the queue detail to modify
				processedDetail := queueDetail

				// Process actions and map them to permissions
				processedDetail.Permissions = processQueueDetailActions(stateMetadata.Actions)

				// Add to filtered list
				filteredQueueDetails = append(filteredQueueDetails, processedDetail)
			}
			// If isVisible is false, the queue detail is excluded from the response
		} else {
			// If state is not in policy, exclude it
			logger.WithContext(ctx).Debugf("[ProcessMyQueuePermissions] state %s not found in policy, excluding from response", queueDetail.CurrentState)
		}
	}

	// Return modified response
	return &myqueue.MyQueueResponse{
		LenderQueueDetails:  filteredQueueDetails,
		IsNextPageAvailable: response.IsNextPageAvailable,
		TotalCount:          len(filteredQueueDetails), // Update total count to reflect filtered results
	}
}

// processQueueDetailActions processes the actions from policy and maps them to permissions
func processQueueDetailActions(actions map[string]MyQueueActionMetadata) myqueue.Permissions {
	permissions := myqueue.Permissions{
		Actions: make(map[string]interface{}),
	}

	// Map each action from policy to permissions
	for actionName, actionMetadata := range actions {
		permissions.Actions[actionName] = map[string]interface{}{
			"isVisible": actionMetadata.IsVisible,
		}
	}

	return permissions
}
