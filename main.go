package main

import (
	"context"
	"finbox/go-api/constants"
	"finbox/go-api/controller/overdraft"
	"finbox/go-api/controller/user/v2"
	"finbox/go-api/core/repository/auditrepository"
	"finbox/go-api/core/repository/bureaurepository"
	"finbox/go-api/core/repository/kycrepository"
	"finbox/go-api/core/repository/multiuserrepository"
	"finbox/go-api/core/repository/offerrepository"
	"finbox/go-api/core/repository/schedulerworkflowconfigrepository"
	"finbox/go-api/core/repository/thirdpartyrepository"
	"finbox/go-api/core/repository/userapirepository"
	"finbox/go-api/core/repository/usersrepository"
	"finbox/go-api/core/services/apistackservice"
	"finbox/go-api/core/services/bureauservice"
	"finbox/go-api/core/services/datasources"
	"finbox/go-api/core/services/journeyservice"
	"finbox/go-api/core/services/lisaservice"
	"finbox/go-api/core/services/offerservice"
	"finbox/go-api/core/services/panservice"
	"finbox/go-api/core/services/schedulerworkflowconfigservice"
	"finbox/go-api/core/services/sentinelService"
	"finbox/go-api/core/services/thirdpartyservice"
	"finbox/go-api/core/services/userprofileservice"
	singlesubmit "finbox/go-api/core/services/userprofileservice/singlesubmit"
	userprofilecoreservice "finbox/go-api/core/services/userprofileservice/singlesubmit"
	"finbox/go-api/functions/taskmanagement"
	"finbox/go-api/functions/taskmanagement/event"
	"finbox/go-api/internal/repository/psql/enachthirdparty"
	"finbox/go-api/internal/repository/psql/misc"
	"finbox/go-api/migrations"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/partnerdataschema"
	"finbox/go-api/models/rbacapimetadata"
	"finbox/go-api/rbac"
	"finbox/go-api/rediswatcher"
	clientrepository "finbox/go-api/studio/repository/client"
	mastersrepository "finbox/go-api/studio/repository/masters"
	resourcerepository "finbox/go-api/studio/repository/resources"
	stagingrepository "finbox/go-api/studio/repository/staging"
	stagingworkflowconfigrepository "finbox/go-api/studio/repository/stagingworkflowconfig"
	"finbox/go-api/studio/repository/transactionprovider"
	masterservice "finbox/go-api/studio/services/masters"
	resourceservice "finbox/go-api/studio/services/resources"
	stagingservice "finbox/go-api/studio/services/staging"
	stagingworkflowconfigservice "finbox/go-api/studio/services/stagingworkflowconfig"
	masterstransport "finbox/go-api/studio/transport/masters/rest"
	stagingtransport "finbox/go-api/studio/transport/staging/rest"
	stagingworkflowconfigtransport "finbox/go-api/studio/transport/stagingworkflowconfig/rest"
	"finbox/go-api/temporal"
	schedulerworkflowconfigtransport "finbox/go-api/transport/schedulerworkflowconfig"

	"finbox/go-api/utils/apischemamapper"
	"finbox/go-api/utils/schemamapper"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/finbox-in/octoclient"
	"github.com/google/uuid"
	"github.com/riandyrn/otelchi"

	"github.com/go-chi/cors"

	"finbox/go-api/async/producer"
	"finbox/go-api/async/worker"

	amortizationInit "finbox/go-api/internal/service/amortizationcalculator/registry"
	bpmworkflowmanagementInit "finbox/go-api/internal/service/bpmworkflowmanagement/registry"

	sentry "github.com/getsentry/sentry-go"
	sentryhttp "github.com/getsentry/sentry-go/http"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/sirupsen/logrus"

	"finbox/go-api/authentication"
	"finbox/go-api/conf"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/lenders/axis"
	"finbox/go-api/functions/lenders/capitalfloat"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/cache"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/otelcollector"
	"finbox/go-api/infra/redis"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/internal/app"
	httpRepo "finbox/go-api/internal/http"
	"finbox/go-api/internal/repository/graphql"
	v1 "finbox/go-api/router/v1"
	v2 "finbox/go-api/router/v2"
	"finbox/go-api/seedlocal"

	"finbox/go-api/internal/repository/psql"
	auditlogssql "finbox/go-api/internal/repository/psql/auditlogs"
	configsql "finbox/go-api/internal/repository/psql/configmanagement"
	dashboardsql "finbox/go-api/internal/repository/psql/dashboard"
	insurancesql "finbox/go-api/internal/repository/psql/insurance"
	loansql "finbox/go-api/internal/repository/psql/loan"
	sourceentitysql "finbox/go-api/internal/repository/psql/sourceentity"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	usersql "finbox/go-api/internal/repository/psql/user"

	configsrv "finbox/go-api/internal/service/configmanagement"
	myqueueregistry "finbox/go-api/internal/service/myqueue/registry"

	cdashboard "finbox/go-api/controller/dashboard"
	clender "finbox/go-api/controller/lender"
	cmasterDashboard "finbox/go-api/controller/masterDashboard"

	ddtracer "gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	ddprofiler "gopkg.in/DataDog/dd-trace-go.v1/profiler"

	"finbox/go-api/internal/service/pickle"
)

var log = logger.Log

func main() {
	var MODE = os.Getenv("MODE")
	startServer(MODE)
}

func startServer(serverType string) {
	switch serverType {
	case constants.ServerTypeApiServer:
		startAPIServer()
	case constants.ServerTypeApiWorker:
		startWorkerServer()
	default:
		fmt.Println("Unknown server_type")
		os.Exit(1)
	}
}

func startAPIServer() {
	logger.Log = logrus.New()
	logger.Log.SetReportCaller(true)
	logger.Log.SetOutput(os.Stdout)
	if conf.ENV == conf.ENV_PROD || conf.IsBuilderEnv() {
		logger.Log.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logger.Log.SetFormatter(&logrus.TextFormatter{})
	}
	if conf.ENV == conf.ENV_PROD {
		logger.Log.SetLevel(logrus.InfoLevel)
	} else {
		logger.Log.SetLevel(logrus.DebugLevel)
	}

	ctx := context.Background()
	otelComponents := otelcollector.InitOtel(ctx, conf.LendingServiceName)
	defer otelcollector.ShutdownOtel(ctx, otelComponents)

	// init sentry
	if conf.ENV == conf.ENV_PROD {
		err := sentry.Init(sentry.ClientOptions{
			Dsn:           conf.SentryDSN,
			Environment:   conf.ENV,
			EnableTracing: false, // we will use sentry only for error tracking not for transactions
			Release:       os.Getenv("DD_VERSION"),
		})
		if err != nil {
			logger.Log.Panicf("sentry.Init: %s", err)
		}
		defer sentry.Flush(time.Second)

		// do not start datadog in local
		// starting datadog tracer
		ddtracer.Start(
			ddtracer.WithDogstatsdAddress("datadog-agent:8125"),
			ddtracer.WithEnv(conf.ENV),
			ddtracer.WithRuntimeMetrics(),
			ddtracer.WithService(conf.LendingServiceName),
		)
		defer ddtracer.Stop()

		// starting datadog profiler
		err = ddprofiler.Start(
			ddprofiler.WithAgentAddr("datadog-agent:8126"),
			ddprofiler.WithEnv(conf.ENV),
			ddprofiler.WithService(conf.LendingServiceName),
			ddprofiler.WithProfileTypes(
				ddprofiler.CPUProfile,
				ddprofiler.HeapProfile,
				ddprofiler.BlockProfile,
				ddprofiler.GoroutineProfile,
				ddprofiler.MetricsProfile,
				ddprofiler.MutexProfile,
			),
		)
		if err != nil {
			logger.Log.Panicf("error while startirn datadog profiler: %s", err)
		}
		defer ddprofiler.Stop()
	}

	// init redis
	enableSSL, _ := conf.RedisConf["SSL"].(bool)
	endpoint, _ := conf.RedisConf["Addr"].(string)
	replicaEndpoint, _ := conf.RedisConf["ReplicaAddr"].(string)
	redis.Init(enableSSL, endpoint, replicaEndpoint)

	// check if location exists
	_, err := time.LoadLocation("Asia/Calcutta")
	if err != nil {
		logger.Log.Println(err)
		panic(err)
	}

	err = rbac.InitializeLenderDashboardRBACEnforcer(logger.Log)
	if err != nil {
		logger.Log.Println(err.Error())
		panic(err)
	}

	rbacAPIMetadataWatcher, err := rediswatcher.NewWatcher(
		rediswatcher.WatcherOptions{
			Channel:    "rbac_api_metadata",
			IgnoreSelf: false,
			LocalID:    uuid.New().String(),
		},
	)
	if err != nil {
		logger.Log.Println(err.Error())
		panic(err)
	}

	err = rbac.InitializeMasterDashboardRBACEnforcer(logger.Log, rbacAPIMetadataWatcher)
	if err != nil {
		logger.Log.Println(err.Error())
		panic(err)
	}

	// initializing worker client
	producer.InitNewAsynqClient()

	temporalclient.Init()

	// make server provider
	makeServerProvider()

	startTemporalWorkers(app.Srv)
	startBuilderTemporalWorkers(app.Srv)

	// graphQLSchema
	go func() {
		defer errorHandler.RecoveryNoResponse()
		graphql.GraphqlSchema = graphql.BuildGraphqlSchema()
	}()

	// make single http clients respect to each external service
	httpRepo.InitHTTPpClientWithCircuitBreaker()

	// run migrations
	migrations.MigrateDB()

	err = rbacapimetadata.Init()
	if err != nil {
		logger.Log.Println(err.Error())
		panic(err)
	}

	// Set PEM keys
	_, err = capitalfloat.SetRSAPublicKeyInRedis()
	if err != nil {
		logger.Log.Error(err)
		sentry.CaptureException(err)
	}

	// Set Axis PEM cert file
	_, err = axis.SetPEMCertInCache()
	if err != nil {
		logger.Log.Error(err)
		//sentry.CaptureException(err)
	}

	// Load partner push config
	if err := schemamapper.InitConfig(); err != nil {
		logger.Log.Error(err)
		sentry.CaptureException(err)
	}

	// Read submit data config
	err = apischemamapper.LoadConfigInCache()
	if err != nil {
		logger.Log.Error(err)
		sentry.CaptureException(err)
	}

	// update api keys if missing and local environment
	if conf.ENV == conf.ENV_LOCAL {
		seedlocal.UpdateAPIKeys()
	}

	r := chi.NewRouter()

	// add middlewares
	sentryMiddleware := sentryhttp.New(sentryhttp.Options{
		Repanic: true, // this is important if Recoverer middleware is used
	})

	if conf.ENV == conf.ENV_LOCAL || strings.HasPrefix(conf.ENV, conf.ENV_DEV) {
		r.Use(cors.Handler(cors.Options{
			// AllowedOrigins:   []string{"https://foo.com"}, // Use this to allow specific origin hosts
			AllowOriginFunc: func(r *http.Request, origin string) bool {
				return strings.HasSuffix(origin, "finbox.in") || strings.Contains(origin, "localhost")
			},
			AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"},
			AllowedHeaders: []string{"*"},
			// AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token", "token", "page-header", "Origin", "DNT", "X-CustomHeader", "Keep-Alive", "User-Agent", "X-Requested-With", "If-Modified-Since", "Cache-Control", "Content-Range", "Range"},
			ExposedHeaders:   []string{"Link"},
			AllowCredentials: false,
			MaxAge:           300, // Maximum value not ignored by any of major browsers
		}))
	}
	r.Use(otelchi.Middleware(conf.LendingPodName, otelchi.WithChiRoutes(r)))
	r.Use(middleware.RequestID)
	r.Use(middleware.Recoverer)
	r.Use(middleware.RealIP) //can access ip address by r.RemoteAddr anywhere
	r.Use(logger.LoggerSetTagsContext(logger.Log))
	r.Use(sentryMiddleware.Handle) // make sure this is included after Recoverer middleware

	// simple health check on base url hit
	r.With(authentication.NonAuthFilter).Get("/", func(w http.ResponseWriter, r *http.Request) {
		_, _ = w.Write([]byte("health ok"))
	})
	r.With(authentication.NonAuthFilter).Get("/health", func(w http.ResponseWriter, r *http.Request) {
		_, _ = w.Write([]byte("health ok"))
	})

	// API version 1.
	r.Route("/v1", v1.V1Router)

	// Add your API version here
	r.Route("/v2", v2.V2Router)

	logger.Log.Println("go-api server started")

	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	go func() {
		<-sig
		rbacAPIMetadataWatcher.Close()
	}()

	server := &http.Server{
		Addr:              ":3333",
		ReadHeaderTimeout: 3 * time.Minute,
		Handler:           r,
	}
	err = server.ListenAndServe()
	if err != nil {
		panic(err)
	}
}

func makeServerProvider() {

	bpmworkflowmanagementInit.Init()
	database := db.GetDB()
	amortizationInit.InitBPICalculatorFactory()

	// init my queue fetchere
	go func() {
		defer errorHandler.RecoveryNoResponse()
		myqueueregistry.InitMyQueueFetchers(myqueueregistry.MyQueueFetcherRegistrar{
			DB: database,
		})
	}()

	// initialize DB repository
	creditLineRepository := psql.NewCreditlineRepository(database)
	masterDashboardRepository := psql.NewMasterDasboardRepository(database)
	dashboardWorkflowRepository := psql.NewDasboardWorkflowRepository(database)
	loanDBRepository := loansql.NewLoanDBRepository(database)
	userDBRepository := usersql.NewUserDBRepository(database)
	enachThirdPartyDBRepository := enachthirdparty.NewEnachThirdPartyDBRepository(database)
	miscRepositoryProvider := misc.NewMiscDBRepository(database)
	dashboardRepository := dashboardsql.NewDashboardRepository(database)
	insuranceRepository := insurancesql.NewInsuranceRepository(database)
	sourceEntityDBRepository := sourceentitysql.NewSourceEntityDBRepository(database)
	txnProvider := psql.NewPsqlTransaction(database)
	taskManagementService := taskmanagement.NewTaskManagementService(event.Factory)
	featureFlagDBRepositoryProvider := featureflag.NewFeatureFlagDBRepository(database)
	partnerDataSchemaRepository := partnerdataschema.NewPartnerDataSchemaDBRepository(database)
	configManageemntDBRepository := configsql.NewConfigManagementDBRepository(database)
	taskManagementDBRepository := taskmanagementsql.NewTaskManagementDBRepository(database)
	ActivityLogDBRepositoryProvider := auditlogssql.NewActivityLogRepository(database)
	resourceRepository := resourcerepository.New(database)
	clientRepository := clientrepository.New(database)
	stagingRepository := stagingrepository.New(database)
	mastersRepository := mastersrepository.New(database)
	kycRepository := kycrepository.New(database)
	usersRepository := usersrepository.New(database)
	userAPIRepository := userapirepository.New(database)
	singleSubmitRepository := singlesubmit.New(database)
	multiuserrepository := multiuserrepository.New(database)
	bureauRepository := bureaurepository.New(database)
	auditRepository := auditrepository.New(database)
	offerRepository := offerrepository.New(database)
	schedulerWorkflowConfigRepository := schedulerworkflowconfigrepository.New(database)
	thirdPartyRepository := thirdpartyrepository.New(database)

	octoClient := octoclient.New(octoclient.Options{
		BaseURL:       conf.OctopusBaseURL,            // Octopus URL ( UAT or Prod provided )
		Token:         conf.OctopusConfig["ClientID"], // Token or ClientID provided
		Authorization: fmt.Sprintf("Bearer %s", conf.OctopusConfig["BearerAuth"]),
	})

	// Initialize Pickle clients for different namespaces
	journeyPickleClient, err := pickle.GetJourneyClient(context.Background())
	if err != nil {
		logger.Log.Errorf("Failed to initialize Journey Pickle client: %v", err)
		sentry.CaptureException(err)
	}

	dashboardPickleClient, err := pickle.GetDashboardClient(context.Background())
	if err != nil {
		logger.Log.Errorf("Failed to initialize Dashboard Pickle client: %v", err)
		sentry.CaptureException(err)
	}

	// initialize internal srv repository
	configManageemntSrvRepository := configsrv.NewConfigManagementSrvRepository(configsrv.ConfigManagementSrvRepository{
		ConfigDBRepositoryProvider: configManageemntDBRepository,
		JourneyPickleClient:        journeyPickleClient,
		DashboardPickleClient:      dashboardPickleClient,
	})

	// initialize API srv repository
	overdraftService := overdraft.NewOverDraftService(
		overdraft.OverDraftService{
			CreditlineRepository: creditLineRepository,
		},
	)

	masterDashboardService := cmasterDashboard.NewMasterDashboardService(
		cmasterDashboard.MasterDashboardService{
			MasterDashBoardDB:   masterDashboardRepository,
			DashBoardWorkflowDB: dashboardWorkflowRepository,
		},
	)

	lenderDashboardService := clender.NewLenderDashboardService(
		clender.LenderDashboardService{
			MasterDashBoardDB:   masterDashboardRepository,
			DashBoardWorkflowDB: dashboardWorkflowRepository,
		},
	)

	stagingWorkflowConfigRepository := stagingworkflowconfigrepository.New(database)
	stagingWorkflowConfigService := stagingworkflowconfigservice.New(stagingWorkflowConfigRepository, auditRepository)

	userProfileService := userprofileservice.New(usersRepository, multiuserrepository)

	stagingService := stagingservice.New(stagingRepository, resourceRepository, clientRepository, stagingWorkflowConfigService, auditRepository)
	resourceService := resourceservice.New(resourceRepository)
	mastersMangementService := masterservice.New(mastersRepository)
	mastersMangementServiceProvider := masterstransport.New(mastersMangementService)

	lisaservice := lisaservice.New(auditRepository)
	thirdpartyService := thirdpartyservice.New(auditRepository, octoClient, thirdPartyRepository, clientRepository)

	panService := panservice.New(kycRepository, thirdpartyService)
	journeyService := journeyservice.New(usersRepository)
	bureauService := bureauservice.New(bureauRepository, thirdpartyService, userProfileService)
	stagingWorkflowConfigTransport := stagingworkflowconfigtransport.New(stagingWorkflowConfigService, stagingService)

	singleSubmitHandler := singlesubmit.NewSingleSubmitHandler(usersRepository, mastersMangementService, singleSubmitRepository, auditRepository, temporalclient.Client)
	transactionProvider := transactionprovider.New(database)

	offerService := offerservice.New(offerservice.LoanOfferServiceParams{
		OfferRepository:     offerRepository,
		ResourceRepository:  resourceRepository,
		LoanDBRepository:    loanDBRepository,
		AuditRepository:     ActivityLogDBRepositoryProvider,
		TransactionProvider: transactionProvider,
	})
	sentinelService := sentinelService.New()
	redisProvider := cache.NewRedis()
	masterRepository := mastersrepository.New(database)
	mastersService := masterservice.New(masterRepository)
	dataSourcesRunner := datasources.New(panService, userProfileService, mastersService, bureauService, redisProvider)
	stagingServiceProvider := stagingtransport.New(stagingService, resourceService)
	apiStackService := apistackservice.New(apistackservice.APIStackServiceDeps{
		TemporalClient:              temporalclient.Client,
		UserAPIRepository:           userAPIRepository,
		FeatureFlagDBRepository:     featureFlagDBRepositoryProvider,
		PartnerDataSchemaRepository: partnerDataSchemaRepository,
		SingleSubmitHandler:         singleSubmitHandler,
		ProfileService:              userProfileService,
		AuditRepository:             ActivityLogDBRepositoryProvider,
		LoanRepository:              loanDBRepository,
	})

	schedulerWorkflowConfigService := schedulerworkflowconfigservice.New(schedulerWorkflowConfigRepository)
	schedulerWorkflowConfigProvider := schedulerworkflowconfigtransport.New(schedulerWorkflowConfigService)

	usersCoreRepository := usersrepository.New(database)
	userProfileCoreService := userprofilecoreservice.New(database)
	singleSubmitService := userprofilecoreservice.NewSingleSubmitHandler(usersCoreRepository, mastersService, userProfileCoreService, auditRepository, temporalclient.Client)

	dashboardService := cdashboard.NewDashboardService(
		cdashboard.DashboardServiceRepository{
			LoanDBRepositoryProvider:              loanDBRepository,
			UserDBRepositoryProvider:              userDBRepository,
			DashboardDBRepositoryProvider:         dashboardRepository,
			InsuranceDBRepositoryProvider:         insuranceRepository,
			TransactionProvider:                   txnProvider,
			ConfigManagementSrvRepositoryProvider: configManageemntSrvRepository,
			TaskManagementService:                 taskManagementService,
			FeatureFlagDBRepositoryProvider:       featureFlagDBRepositoryProvider,
			TaskManagementDBRepositoryProvider:    taskManagementDBRepository,
			ActivityLogDBRepositoryProvider:       ActivityLogDBRepositoryProvider,
			EnachThirdPartyRepositoryProvider:     enachThirdPartyDBRepository,
			SourceEntityDBRepositoryProvider:      sourceEntityDBRepository,
			MiscRepositoryProvider:                miscRepositoryProvider,
			MastersRepositoryService:              mastersMangementService,
			SingleSubmitService:                   singleSubmitService,
		},
	)

	userV2Controller := user.NewUserV2Controller(user.UserV2ControllerDeps{
		APIStackService: apiStackService,
	})

	// map to serverProvider
	app.Srv = app.Server{
		OverDraftService:                    overdraftService,
		MasterDashBoardService:              masterDashboardService,
		LenderDashBoardService:              lenderDashboardService,
		DashboardService:                    dashboardService,
		ConfigManagementService:             configManageemntSrvRepository,
		StagingManagementService:            stagingServiceProvider,
		StagingWorkflowConfigBuilderService: stagingWorkflowConfigTransport,
		MastersManagementService:            mastersMangementServiceProvider,
		MastersService:                      mastersService,
		PANService:                          panService,
		JourneyService:                      journeyService,
		BureauService:                       bureauService,
		UserV2Controller:                    userV2Controller,
		UserProfileService:                  userProfileService,
		OfferService:                        offerService,
		SentinelService:                     sentinelService,
		ThirdPartyService:                   thirdpartyService,
		SchedulerWorkflowConfigService:      schedulerWorkflowConfigProvider,
		JourneyPickleClient:                 journeyPickleClient,
		DashboardPickleClient:               dashboardPickleClient,
		DataSourcesRunner:                   dataSourcesRunner,
		LisaService:                         lisaservice,
		StagingService:                      stagingService,
		ResourceService:                     resourceService,
	}
}

func startWorkerServer() {
	logger.Log = logrus.New()
	logger.Log.SetReportCaller(true)
	logger.Log.SetOutput(os.Stdout)
	if conf.ENV == conf.ENV_PROD {
		logger.Log.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logger.Log.SetFormatter(&logrus.TextFormatter{})
	}
	if conf.ENV == conf.ENV_PROD {
		logger.Log.SetLevel(logrus.InfoLevel)
	} else {
		logger.Log.SetLevel(logrus.DebugLevel)
	}

	ctx := context.Background()
	otelComponents := otelcollector.InitOtel(ctx, conf.LendingWorkers)
	defer otelcollector.ShutdownOtel(ctx, otelComponents)

	// init sentry
	if conf.ENV == conf.ENV_PROD {
		err := sentry.Init(sentry.ClientOptions{
			Dsn:           conf.SentryDSN,
			Environment:   conf.ENV,
			EnableTracing: false, // we will use sentry only for error tracking not for transactions
			Release:       os.Getenv("DD_VERSION"),
		})
		if err != nil {
			logger.Log.Panicf("sentry.Init: %s", err)
		}
		defer sentry.Flush(time.Second)

		// do not start datadog in local
		// starting datadog tracer
		ddtracer.Start(
			ddtracer.WithAgentAddr("datadog-agent:8126"),
			ddtracer.WithDogstatsdAddress("datadog-agent:8125"),
			ddtracer.WithEnv(conf.ENV),
			ddtracer.WithRuntimeMetrics(),
			ddtracer.WithService(conf.LendingWorkers),
		)
		defer ddtracer.Stop()

		// starting datadog profiler
		err = ddprofiler.Start(
			ddprofiler.WithAgentAddr("datadog-agent:8126"),
			ddprofiler.WithEnv(conf.ENV),
			ddprofiler.WithService(conf.LendingWorkers),
			ddprofiler.WithProfileTypes(
				ddprofiler.CPUProfile,
				ddprofiler.HeapProfile,
				ddprofiler.BlockProfile,
				ddprofiler.GoroutineProfile,
				ddprofiler.MetricsProfile,
				ddprofiler.MutexProfile,
			),
		)
		if err != nil {
			logger.Log.Panicf("error while startirn datadog profiler: %s", err)
		}
		defer ddprofiler.Stop()
	}

	// init redis
	enableSSL, _ := conf.RedisConf["SSL"].(bool)
	endpoint, _ := conf.RedisConf["Addr"].(string)
	replicaEndpoint, _ := conf.RedisConf["ReplicaAddr"].(string)
	redis.Init(enableSSL, endpoint, replicaEndpoint)

	// check if location exists
	_, err := time.LoadLocation("Asia/Calcutta")
	if err != nil {
		logger.Log.Println(err)
		panic(err)
	}

	// make server provider
	makeServerProvider()

	// init temporal client
	temporalclient.Init()

	// create temporal worker
	workerSrvTemporalWorker := temporal.NewTemporalWorker(temporalclient.WorkerClient.Client, temporalclient.WorkerClient.Queue)

	// Start the scheduler worker in a go routine, once and if temporal client is initiated
	initStruct := <-temporalclient.WorkerClient.InitStatusChannel
	if initStruct.Error != nil {
		logger.Log.Errorln("error initiating temporal client: ", initStruct.Error)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{}, initStruct.Error)
		// close temporal client
		temporalclient.WorkerClient.Close()
	}
	if initStruct.IsInitialized {
		logger.Log.Infoln("temporal client initiated")
		go func() {
			workerSrvTemporalWorker.Start(app.Srv)
		}()
	}

	// run migrations
	migrations.MigrateDB()

	producer.InitNewAsynqClient()

	// starting asynq workers
	server := worker.NewServer()
	err = server.StartWorkers()
	if err != nil {
		err = fmt.Errorf("shutting down workers err: %s", err.Error())
		log.Error(err)
		sentry.CaptureException(err)
	}

	// stop temporal worker on termination signal
	sig := make(chan os.Signal, 1)
	signal.Notify(sig, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT, os.Interrupt)
	go func() {
		<-sig
		workerSrvTemporalWorker.Stop()
	}()

}

func startTemporalWorkers(app app.Server) {
	for queue, client := range temporalclient.WorkerConfig {
		workerCount := temporalclient.QueueWorkerCount[queue]
		for workerCount > 0 {
			log.Infoln("StartWorker initiated for: ", queue)
			go temporal.StartWorker(queue, *client, app)
			workerCount--
		}
	}
}

func startBuilderTemporalWorkers(app app.Server) {
	log.Infoln("Start BuilderWorker 1")
	go temporal.StartBuilderWorker(constants.BuilderTaskQueue, temporalclient.Client, app)
	log.Infoln("Start BuilderWorker 2")
	go temporal.StartBuilderWorker(constants.BuilderTaskQueue, temporalclient.Client, app)
}
