package dashboard

import (
	"finbox/go-api/constants"
	"finbox/go-api/internal/service/ratelimiter"
	"time"
)

// error constants
const (
	AuthorizationInsufficientAccess = "Insufficient Access"
	EmptySourceEntityID             = "Empty SourceEntity ID"
	EmptyUserID                     = "empty userID"
	InvalidUserType                 = "invalid user type"
	EmptyLoanApplicationID          = "Empty LoanApplication ID"
	EmptyBankDetailsID              = "Empty Bank Details ID"
	InvalidBankDetailsID            = "Invalid Bank Details ID"
	EmptyPartnerCode                = "Empty Partner code"
	InvalidUserID                   = "Invalid user ID"
	InvalidProgramID                = "Invalid program ID"
	ConfigDataNotFound              = "Config Data Not Found"
	LoanOfferNotFound               = "Loan offer Not Found"

	//loan related errors
	InvalidLoanID                    = "Invalid LoanApplication ID"
	ErrFetchingLoanDetails           = "Failed to get loan details"
	LoanDetailsNotFound              = "loan details not found"
	BusinessLoanOfferDetailsNotFound = "business loan offer details not found"

	// user related errors
	UserNotFound                = "User not found"
	UserDetailsNotFound         = "user details not found"
	UserBusinessDetailsNotFound = "user business details not found"
	UserUTMDetailsNotFound      = "user utm details not found"
	ErrFetchingUserDetails      = "Failed to get user details"

	MalformedRequestBody = "malformed request body"

	CouldNotFetchLogsFromLisa        = "Could not fetch logs for given user"
	InvalidLimitValue                = "invalid value for limit"
	InvalidPageValue                 = "invalid value for page"
	InvalidToDateValue               = "invalid value for to Date"
	InvalidFromDateValue             = "invalid value for from date"
	GetCreditLineTransactionNotFound = "creditline transaction not found"
	InvalidMobile                    = "invalid mobile"
	InvaliPAN                        = "invalid pan"
	EmptyIndustry                    = "Empty Industry"
	EmptySubIndustry                 = "Empty Sub Insutry"
	EmptyNatureOfBusiness            = "Empty Nature Of Business"
	ReportNotAvailable               = "Report not available"
	InvalidLenderID                  = "Invalid lender ID"
	InvalidPincode                   = "invalid pincode"
	InvalidRequestBody               = "Invalid request body"
	InvalidEmail                     = "Invalid Email"
	EmptyGroupName                   = "Empty Group Name"
	MissingRequiredFields            = "Missing Required Fields"
	InvalidMasterTaskID              = "Invalid master task ID"
	InvalidCurrentState              = "Invalid current state"

	InvalidTaskID     = "Invalid task ID"
	InvalidApiName    = "Invalid API Name"
	InvalidDate       = "Invalid date"
	InvalidFromToDate = "Invalid from and to date"
	InvalidStatus     = "Invalid status"
	InvalidSegment    = "Invalid segment"
	NoResultFound     = "no result found"

	// myqueue related errors
	ErrFetchingBranchCodes = "Failed to fetch branch codes"

	//workflow related errors
	ErrCheckingTriggerCondition = "Failed to check workflow trigger condition"
	WorkflowAlreadyRunning      = "Workflow already running"
	InvalidWorkflowInstanceID   = "Invalid workflow instance ID"
	ErrStartingWorkflow         = "Failed to start workflow"
	InvalidWorkflowAction       = "Invalid workflow action"
	WorkflowInstanceNotFound    = "Workflow instance not found"

	// task related errors
	EmptyTaskType = "Empty Task Type"
	TaskNotFound  = "task not found"

	InvalidMediaID = "Invalid media ID"

	// Rate limit related errors
	ForgetPasswordRateLimitExceeded = "Limit exceeded for forget password, try after 1 minute"
)

// user radius flag constant
const (
	UserRadiusResultCantDecide = "CAN'T DECIDE"
	UserRadiusResultPass       = "PASS"
	UserRadiusResultFail       = "FAIL"
)

// boolean string constant
const (
	BooleanTrueStringConstant  = "true"
	BooleanFalseStringConstant = "false"
)

// resource constant name
const (
	service             = "lenderservice_"
	GetUserLogsResource = service + "get_user_logs"
)

const (
	RedGateservice  = "redgateservice"
	InitiateSSO     = RedGateservice + "initiate_sso"
	ValidateSession = RedGateservice + "validate_session"
)

const (
	// MFL PreApproved Loan Utility
	DefaultPreApprovedtSourceEntityID = "8efd25fb-9c1f-4adc-90a8-7e2cc0b17775"
	DefaultPreApprovedtPartnerCode    = "8efd25fb-9c1f-4adc-90a8-7e2cc0b17775"
)

// documents constants
const (
	CategoryBasedDocumentsReqType = "category-based"
	DocIdsBasedDocumentReqType    = "doc-ids-based"
)

// disbursements
// DisbursementRequestType represents the type of disbursement request
const (
	DisbursementRequested = "requested"
	PendingOnBPO          = "pending_on_bpo"
	PendingOnOps          = "pending_on_ops"
	LenderInitiated       = "lender_initiated"
	DisbursementRejected  = "rejected"
	DisbursementFailed    = "failed"
	DisbursementCompleted = "completed"
)

// DisbursementStatus represents the status of a disbursement request
const (
	DisbursementStatusPending                   = 1
	DisbursementStatusSuccess                   = 2
	DisbursementStatusFailed                    = 3
	DisbursementRequestStatusPending            = 1
	DisbursementRequestStatusSuccess            = 2
	DisbursementRequestStatusFailed             = 3
	DisbursementRequestStatusPartiallyCompleted = 4
	DisbursementPaymentInitiated                = 5
)

var DisbursementStatus = map[int]string{
	DisbursementStatusPending: "Initiated",
	DisbursementStatusSuccess: "Disbursed",
	DisbursementStatusFailed:  "Failed",
}

// disbursement type constants
const (
	ELECTRONIC = "ELECTRONIC FUND TRANSFER"
	CHEQUE     = "CHEQUE"
)

// worfkflow constants
const (
	WorkflowType         = "disbursment_workflow"
	WorkflowResourceName = "disbursement_workflow"
)

// GetTaskStateLabelText returns a human-readable string for each task state
func GetTaskStateLabelText(state string) string {
	switch state {
	case "ro_qde":
		return "RO Journey"
	case "co_dde":
		return "DDE Review"
	case "bpo_verification":
		return "BPO Review 1"
	case "bcm_decision":
		return "Under Review"
	case "approved":
		return "Approved"
	case "rejected":
		return "Rejected"
	case "bpo_manager":
		return "BPO Manager"
	default:
		return state
	}
}

const (
	RCUManagerVerification = "rcu_manager_verification"
	RCUManagerGroup        = "RCU Manager Group"
)

// unleash flag
const (
	UnleashFlagForDashboardNewStulDeviation = "dashboard_New_STULDeviation"
)

// Temporal workflow constants
const (
	MFL_GenerateDocsWorkflow = "MFL_GenerateDocsWorkflow"
)

const (
	SignedLoanAgreementDocumentID = "4a7191d1-f588-4ddd-9a2d-48629cc084b2"
	SignedMergedDocumentID        = "26595a2e-4b38-456b-80f7-1d0172619b5a"
	InvalidDocumentID             = "Invalid document ID"
	OFFLINE_MODE                  = "offline"
)

// error message
const (
	E_SIGN_ATTEMPT_FETCH_ERROR_MSG                          = "Error fetching esign attempts"
	DOCUMENTS_FETCH_ERROR_MSG                               = "Error fetching documents"
	E_SIGN_ATTEMPT_CREATION_FAILED_ERROR_MSG                = "Failed to create e-sign attempts"
	E_SIGN_ATTEMPT_CREATED_SUCCESSFULLY                     = "E-Sign attempts created successfully"
	UnAuthorisedUserAction                                  = "user not authorised to take action"
	WorkflowStateNotFound                                   = "workflow state not found"
	ActionNotAllowed                                        = "action not allowed"
	CompleteAllTaskErrMsg                                   = "Please complete all mandatory verifications to initiate approval flow"
	DeviationNotResolvedErrMsg                              = "deviation not in resolved state"
	EmailOtpVerificationFailedErrorMsg                      = "OTP verification Failed. Please try again"
	DifferentEmailRequiredForPrimaryAndCoapplicantsErrorMsg = "Email ID already in use by another applicant. Please enter a different email."
)

// lap loan offer
const (
	ErrInvalidProcessingFee = "Processing fee is invalid"
	ErrInvalidTenure        = "Tenure must be a non-negative number"
	ErrFetchLoanApp         = "Fetch loan application failed"
	ErrFetchRiskCategory    = "Fetch risk category failed"
	InvalidROI              = "ROI must be greater than base interest rate (10)"
	InvalidState            = "Invalid state"
	InvalidLoanTenure       = "Invalid tenure value"
)

// Insurance workflow error messages
const (
	MessageInsuranceConfigNotFound = "Insurance configuration not found"
)

// Insurance Record Status Constants
const (
	// Individual Insurance Record Statuses
	InsuranceStatusInactive   = 0 // Deactivated/Cancelled
	InsuranceStatusPending    = 1 // Active but not filled/submitted
	InsuranceStatusInProgress = 2 // Partially filled/In progress
	InsuranceStatusCompleted  = 3 // Completed/Finalized
)

// Overall Insurance Status Constants (for grouped/summary status)
const (
	// Overall Status for Insurance Groups/Summary
	OverallStatusInactive        = 0 // No active insurances
	OverallStatusAllPending      = 1 // All insurances are pending
	OverallStatusPartiallyFilled = 2 // Mix of statuses or some in progress
	OverallStatusAllFilled       = 3 // All insurances are filled (status 2)
	OverallStatusAllCompleted    = 4 // All insurances are completed (status 3)
)

// Status Groups for common filtering
var (
	ActiveStatuses     = []int{InsuranceStatusPending, InsuranceStatusInProgress, InsuranceStatusCompleted}
	FilledStatuses     = []int{InsuranceStatusInProgress, InsuranceStatusCompleted}
	PendingStatuses    = []int{InsuranceStatusPending}
	CompletedStatuses  = []int{InsuranceStatusCompleted}
	InProgressStatuses = []int{InsuranceStatusInProgress}
)

// Insurance Types
var (
	InsuranceTypeLife    = "life"
	InsuranceTypeGeneral = "general"
)

var (
	InsuranceConfigResourceName = "insurance_ref"
)

const (
	CamReportDocumentID = "36da56b3-df3e-4dee-8e93-c58f19b8a694"
	TaskNotResolved     = "task not resolved"
)

const (
	TASKS_TABLE_NAME     = "tasks"
	METADATA_COLUMN_NAME = "metadata"
	ID_COLUMN_NAME       = "id"
)

var DeviationTaskSupportedLenders = []string{constants.MFLID}

func IsLoanDeviationSupportedViaTaskManagement(lenderID string) bool {
	for _, id := range DeviationTaskSupportedLenders {
		if id == lenderID {
			return true
		}
	}
	return false
}

// task status IDs
const (
	LoanDeviationPendingStatusMFL = "30efbbd6-076f-4f62-9517-6bfec887c0bf"
)

// workflow states
const (
	APPROVED = "APPROVED"
	REJECTED = "REJECTED"
)

var ResetPasswordVelocityConfig = ratelimiter.RateLimitConfig{
	MaxAttempts: 3,
	TTL:         60 * time.Second,
	Prefix:      "reset_password_velocity",
}

const (
	MaxBulkInsertSize = 1000
)

const (
	ABFLPLCreditWorkflowStatus = "credit_wf_status"
	ABFLPLRCUWorkflowStatus    = "rcu_wf_status"
)

const (
	UsersTable            = "users"
	DynamicUserInfoColumn = "dynamic_user_info"
)
