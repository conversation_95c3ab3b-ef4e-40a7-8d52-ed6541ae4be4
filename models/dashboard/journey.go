package dashboard

type GenerateWebLinkParam struct {
	UserID                    string  `json:"userID" validate:"required"`
	WithdrawAmount            float64 `json:"withdrawAmount"`
	TransactionID             string  `json:"transactionID"`
	ProgramName               string  `json:"programName"`
	MerchantName              string  `json:"merchantName"`
	MerchantBankAccountNumber string  `json:"merchantBankAccountNumber"`
	MerchantIfscCode          string  `json:"merchantIfscCode"`
	MerchantGstNumber         string  `json:"merchantGstNumber"`
	InvoiceIDs                string  `json:"invoiceIDs"`
	InvoiceDates              string  `json:"invoiceDates"`
	MerchantEmail             string  `json:"merchantEmail" validate:"omitempty,email"`
	MerchantMobile            string  `json:"merchantMobile" validate:"omitempty,len=10"`
	OrderName                 string  `json:"orderName"`
	RedirectURL               string  `json:"redirectURL"`
}
