package dashboard

import (
	"context"
	"encoding/json"
	"net/http"

	"finbox/go-api/errorHandler"
	mdashboard "finbox/go-api/models/dashboard"
)

// GenerateWebLink handles the request validation for GenerateWebLink endpoint
func GenerateWebLink(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Parse and validate request body using existing model
		var reqBody mdashboard.GenerateWebLinkParam
		if err := json.NewDecoder(r.Body).Decode(&reqBody); err != nil {
			log.WithContext(ctx).Errorf("[GenerateWebLink] failed to decode request body: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.MalformedRequestBody)
			return
		}

		// Add validated request and attributes to context
		ctx = context.WithValue(ctx, "req", reqBody)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
