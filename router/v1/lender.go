package v1

import (
	"finbox/go-api/internal/app"
	"finbox/go-api/responseHandler/responseCommon"

	"github.com/go-chi/chi/v5"

	auth "finbox/go-api/authentication"
	dashboardAuthorization "finbox/go-api/authorization/middleware/dashboard"
	lenderContV2 "finbox/go-api/controller/dashboard"
	kycCont "finbox/go-api/controller/kyc/v2"
	lenderCont "finbox/go-api/controller/lender"
	paymentCont "finbox/go-api/controller/payment"
	userReq "finbox/go-api/requestHandler/user/v2"

	dashboardRequestV2 "finbox/go-api/requestHandler/dashboard"
	lenderReq "finbox/go-api/requestHandler/lender"
	paymentReq "finbox/go-api/requestHandler/payment"
	servicesReq "finbox/go-api/requestHandler/services"
	lenderRes "finbox/go-api/responseHandler/lender"
)

// LenderRouter for /lender pattern
func LenderRouter(r chi.Router) {
	r.Route("/", lenderRoutes)
}

// add new routes here
func lenderRoutes(r chi.Router) {

	r.With(
		auth.<PERSON><PERSON><PERSON><PERSON>ilter,
		lenderReq.LoginReq,
		lenderCont.LoginCont,
	).Post("/login", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardRequestV2.LoginSSOReq,
		lenderContV2.LoginSSOCont,
	).Post("/login-sso", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardRequestV2.GenerateTokenReq,
		lenderContV2.GenerateTokenCont,
	).Post("/generate-token", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		lenderReq.SendLoginOTPOnEmailReq,
		lenderCont.SendLoginOTPOnEmailCont,
	).Post("/sendLoginOTPOnEmail", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		lenderReq.VerifyLoginOTPUsingEmailReq,
		lenderCont.VerifyLoginOTPUsingEmailCont,
	).Post("/verifyLoginOTPUsingEmail", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanListReq,
		lenderCont.GetLoanListCont,
	).Get("/getLoanList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LoanDateRangeDumpReq,
		lenderCont.LoanDateRangeDumpCont,
	).Get("/loanDateRangeDump", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		dashboardRequestV2.MisLoanDateRangeDumpReq,
		lenderContV2.MisLoanDateRangeDumpCont,
	).Get("/misloanDateRangeDump", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CollectionDumpReq,
		lenderCont.CollectionDumpCont,
	).Get("/collectionDump", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ApproveLoanReq,
		lenderCont.ApproveLoanCont,
	).Post("/approveLoan", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RejectLoanReq,
		lenderCont.RejectLoanCont,
	).Post("/rejectLoan", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LoanActivityReq,
		lenderCont.LoanActivityCont,
	).Get("/loanActivity", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetLoanDetailsCont,
	).Get("/getLoanDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetBankDetailsReq,
		app.Srv.DashboardService.GetBankDetailsCont,
	).Get("/getBankDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.UserBankConnectDetailsReq,
		lenderContV2.ListBankConnectAttempts,
	).Get("/listBankConnectAttempts", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.BankConnectReportsByIDReq,
		lenderContV2.GetBankConnectReportByID,
	).Get("/getBankConnectReportByID", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetLoanOfferCont,
	).Get("/getLoanOffer", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetLoanKYCCont,
	).Get("/getLoanKYC", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetPartnerDataCont,
	).Get("/partnerData", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetUnderwritingCont,
	).Get("/getUnderwriting", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetBureauReportCont,
	).Get("/getBureauReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetMultipleBureauReportsCont,
	).Get("/getBureauReports", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetUnsignedAgreementCont,
	).Get("/getUnsignedAgreement", lenderRes.GetUnsignedAgreementRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetSignedAgreementCont,
	).Get("/getSignedAgreement", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetKYCZipCont,
	).Get("/getKycZip", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetRepaymentScheduleCont,
	).Get("/getRepaymentSchedule", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.DisburseLoanReq,
		lenderCont.DisburseLoanCont,
	).Post("/disburseLoan", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RegisterLoanRepaymentReq,
		lenderCont.RegisterLoanRepaymentCont,
	).Post("/registerLoanRepayment", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.FetchDeviceConnectDetailsCont,
	).Get("/fetchDevice", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.DeviceConnectFetchDetailsCont,
	).Get("/fetchDeviceDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetPreLimitCont,
	).Get("/getPreLimit", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ApproveLoanReq,
		lenderCont.UpdatePreLimitCont,
	).Post("/updatePreLimit", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateProfileOTPReq,
		lenderCont.UpdateProfileOTPCont,
	).Post("/updateProfileOTP", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateUserProfileReq,
		lenderCont.UpdateUserProfileCont,
	).Post("/updateUserProfile", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		lenderReq.ResetExpiredPasswordReq,
		lenderCont.ResetExpiredPasswordCont,
	).Post("/resetExpiredPassword", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		lenderReq.SendForgetPasswordLinkReq,
		lenderCont.SendForgetPasswordLinkCont,
	).Post("/sendForgetPasswordLink", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.SendChangePasswordLinkReq,
		lenderCont.SendChangePasswordLinkCont,
	).Post("/sendChangePasswordLink", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		lenderReq.ResetUserPasswordReq,
		lenderCont.ResetUserPasswordCont,
	).Post("/resetPassword", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ChangePasswordReq,
		lenderCont.ChangePasswordCont,
	).Post("/changePassword", responseCommon.GenericRes)

	// assisted journey specific requests

	r.With(
		auth.LenderAuthFilter,
		lenderReq.AssistedReviewReq,
		lenderCont.AssistedReviewCont,
	).Post("/assistedReview", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LimitReq,
		lenderCont.GetAssistedReviewCont,
	).Get("/getAssistedReview", responseCommon.GenericRes)

	// credit line specific APIs

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ApproveCreditLineReq,
		lenderCont.ApproveCreditLineCont,
	).Post("/approveCreditLine", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.DisburseCreditLineTxnReq,
		lenderCont.DisburseCreditLineTxnCont,
	).Post("/disburseCreditLineTxn", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RaiseQueryReq,
		lenderCont.RaiseQueryCont,
	).Post("/raiseQuery", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetCreditLineDetailsCont,
	).Get("/getCreditLineDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetCreditLineTransactionsCont,
	).Get("/getCreditLineTransactions", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LimitReq,
		lenderCont.GetConfirmedTransactionsCont,
	).Get("/getConfirmedTransactions", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateCreditLineStatusReq,
		lenderCont.UpdateCreditLineStatusCont,
	).Post("/updateCreditLineStatus", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetBusinessDetailsCont,
	).Get("/getBusinessDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetGSTDetailsCont,
	).Get("/getGSTDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetBankStatementsCont,
	).Get("/getBankStatements", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetBankConnectReportCont,
	).Get("/getBankConnectReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetBankConnectReportContV2,
	).Get("/getBankConnectReportV2", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateCurrentAddressReq,
		lenderCont.UpdateCurrentAddressCont,
	).Post("/updateCurrentAddress", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateDOBReq,
		lenderCont.UpdateDOBCont,
	).Post("/updateDOB", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.ResendLenderKYCEmail,
	).Get("/resendLenderKycEmail", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.RegenerateKYCZipCont,
	).Get("/regenerateKYCZip", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RegenerateAgreementReq,
		lenderCont.RegenerateAgreementCont,
	).Get("/regenerateAgreement", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateBusinessDetailsReq,
		lenderContV2.UpdateBusinessDetailsCont,
	).Post("/updateBusinessDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateCreditLineTxnStatusReq,
		lenderContV2.UpdateCreditLineTxnStatusCont,
	).Post("/updateCreditLineTxnStatus", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateBusinessAddressReq,
		lenderCont.UpdateBusinessAddressCont,
	).Post("/updateBusinessAddress", responseCommon.GenericRes)

	// Refund & transactions tab
	r.With(
		auth.LenderAuthFilter,
		lenderReq.TransactionListReq,
		lenderCont.TransactionListCont,
	).Get("/transactions", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.TransactionListDumpReq,
		lenderCont.TransactionListDumpCont,
	).Get("/transactionsDump", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateGenderReq,
		lenderCont.UpdateGenderCont,
	).Post("/updateGender", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateFathersNameReq,
		lenderCont.UpdateFathersNameCont,
	).Post("/updateFathersName", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateKYCDocReq,
		lenderCont.UpdateKYCDocCont,
	).Post("/updateKYCDoc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.RetriggerKYCEngineCont,
	).Get("/retriggerKYCEngine", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateBankDetailsReq,
		lenderCont.UpdateBankDetailsCont,
	).Post("/updateBankDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.LoanPaymentsHistoryCont,
	).Get("/payments", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReqForMandate,
		lenderCont.GenerateEmandateURLCont,
	).Get("/generateEmandateUrl", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.BCSaveCont,
	).Get("/refetchStatements", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.RefetchGSTCont,
	).Get("/refetchGST", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.CancelNACHCont,
	).Get("/cancelNACH", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateLoanOfferReq,
		lenderCont.UpdateLoanOfferCont,
	).Post("/updateLoanOffer", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RemoveInsuranceReq,
		lenderCont.RemoveInsuranceCont,
	).Post("/removeInsurance", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ListPartnersReq,
		lenderCont.ListPartnersCont,
	).Get("/listPartners", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.ListSourcesCont,
	).Get("/listSources", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetExportHistoryReq,
		lenderCont.GetExportHistoryCont,
	).Get("/getExportHistory", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CancelEKYCReq,
		lenderCont.CancelEKYCCont,
	).Post("/cancelEKYC", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RetriggerExperianReq,
		lenderCont.RetriggerExperianCont,
	).Post("/retriggerExperian", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CancelEKYCReq,
		lenderCont.RerunLastPolicyCont,
	).Post("/rerunLastPolicy", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ChangeCreditLimitReq,
		lenderCont.ChangeCreditLimitCont,
	).Post("/changeCreditLimit", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.BoosterStatusCont,
	).Get("/showBoosterStatus", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateBoosterRequestReq,
		lenderCont.UpdateBoosterRequestCont,
	).Post("/updateBoosterRequest", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetODBillsReq,
		lenderCont.GetODBillsCont,
	).Get("/getODBills", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateBankAccountTypeReq,
		lenderCont.UpdateBankAccountTypeCont,
	).Post("/updateBankAccountType", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LOSPushReq,
		lenderCont.LOSTxnDisbursalCont,
	).Post("/losTxnDisbursal", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LoanApplicationReq,
		lenderCont.CreatePhysicalMandateCont,
	).Post("/createPhysicalMandate", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.SignPhysicalMandateReq,
		lenderCont.SignPhysicalMandateCont,
	).Post("/signPhysicalMandate", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LOSPushReq,
		lenderCont.LOSTxnDocPushCont,
	).Post("/losTxnDocPush", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.MarkDisbursedToLender,
	).Post("/markDisbursed", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.MarkDisbursedLoanInBulkReq,
		lenderCont.MarkLoanDisbursedInBulkCont,
	).Post("/markLoanDisbursedInBulk", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.MarkDisbursedInBulkReq,
		lenderCont.MarkCreditLineDisbursedInBulkCont,
	).Post("/markCreditLineDisbursedInBulk", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.SampleMarkDisbursedToLenderCont,
	).Get("/sampleMarkPaidToLender", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.SampleMarkLoanDisbursedInBulkCont,
	).Get("/sampleMarkLoanDisbursedInBulk", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.SampleMarkCreditLineDisbursedInBulkCont,
	).Get("/sampleMarkCreditLineDisbursedInBulk", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.SampleMarkCreditLineDisbursedFromInvoice,
	).Get("/sampleMarkCreditLineDisbursedFromInvoice", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.MarkLoanRejectInBulkReq,
		lenderCont.MarkLoanRejectInBulkCont,
	).Post("/markLoanRejectInBulk", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.SampleMarkLoanRejectInBulkCont,
	).Get("/sampleMarkLoanRejectInBulk", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.MarkDisbursedInBulkReq,
		lenderCont.MarkCreditLineDisbursedFromInvoice,
	).Post("/markCreditLineDisbursedFromInvoice", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateLoanUTRReq,
		lenderCont.UpdateLoanUTRCont,
	).Post("/updateLoanUTR", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateTxnUTRReq,
		lenderCont.UpdateTxnUTRCont,
	).Post("/updateTxnUTR", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateCLRepaymentDetailsReq,
		lenderCont.UpdateCLRepaymentDetailsCont,
	).Post("/updateCLRepaymentDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LoanApplicationReq,
		lenderCont.CloseLimitCont,
	).Post("/closeLimit", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CancelEKYCReq,
		lenderCont.AddDualNameClauseCont,
	).Post("/addNameClause", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.NachUpdateReportReq,
		lenderCont.NachUpdateReportCont,
	).Get("/nachUpdateReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ODBillsDumpReq,
		lenderCont.ODBillsDumpCont,
	).Get("/ODbillsDump", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetWaitStateCont,
	).Get("/getWaitState", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.RetriggerIIFLDecisionCont,
	).Get("/retriggerIIFLDecision", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.RetriggerIIFLCont,
	).Get("/retriggerIIFL", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.RetriggerIIFLCIBILCont,
	).Get("/retriggerIIFLCIBIL", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetFlagsCont,
	).Get("/getFlags", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetEKYCPDFCont,
	).Get("/getEKYCPDF", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LimitReq,
		lenderCont.GetManualBoosterCasesCont,
	).Get("/getManualBoosterCases", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.UploadAdhocNachCSVCont,
	).Post("/uploadAdhocNachFile", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.AdhocEnachPresentationReq,
		lenderCont.AdhocEnachPresentationCont,
	).Post("/triggerAdhocEnachDag", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.SourceEntityIDReq,
		lenderCont.ServiceablePincodeCont,
	).Get("/serviceablePincode", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RuleEngineDumpReq,
		lenderCont.RuleEngineDumpCont,
	).Get("/ruleEngineDump", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RuleEngineDumpReq,
		lenderCont.DeviceDataDumpCont,
	).Get("/deviceDataDump", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.NachUpdateReportByRequestIDReq,
		lenderCont.NachUpdateReportByReqIDCont,
	).Get("/nachUpdateReportByRequestID", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CancelLoanReq,
		lenderCont.CancelLoanCont,
	).Post("/cancelLoan", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		paymentReq.GetVirtualAccountDetailsReq,
		lenderCont.GetVirtualAccountDetailsCont,
	).Get("/getVirtualAccountDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetRequestDetailsReq,
		lenderCont.GetRequestDetailsCont,
	).Get("/getRequestDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetNachPresentationReportsReq,
		lenderCont.GetAllNachPresentationReportsCont,
	).Get("/getAllNachPresentationReports", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ExportLedgerReq,
		paymentCont.ExportLedgerCont,
	).Get("/exportLedger", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CheckPincodeReq,
		lenderCont.AddPincodeCont,
	).Post("/addSinglePincode", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CheckPincodeReq,
		lenderCont.RemovePincodeCont,
	).Post("/deleteSinglePincode", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CheckBulkPincodeReq,
		lenderCont.BulkAddPincodeCont,
	).Post("/bulkAddPincode", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CheckBulkPincodeReq,
		lenderCont.BulkReplacePincodeCont,
	).Post("/bulkReplacePincode", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.GetAllSEPincodeListForLender,
	).Get("/getPincodeSEList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetPincodeListReq,
		lenderCont.GetPincodeList,
	).Get("/getPincodeList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.AddNewPincodeListForPartner,
		lenderCont.AddNewPincodeListForPartnerCont,
	).Post("/addNewPincodeList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CheckDeletePincodeSEListReq,
		lenderCont.DeletePincodeSEList,
	).Delete("/deletePincodeSEList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UserListReq,
		lenderCont.UserListCont,
	).Get("/members", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.AddDSAReq,
		lenderCont.AddDSACont,
	).Post("/addDSA", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateDSADetailsReq,
		app.Srv.DashboardService.UpdateDSADetailsCont,
	).Post("/updateDSADetail", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.ListTagController,
	).Get("/getTagsList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ListAppliedTagRequest,
		lenderCont.ListAppliedTagController,
	).Get("/getAppliedTagsList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ApplyTagRequest,
		lenderCont.ApplyTagController,
	).Post("/applyTag", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RemoveTagRequest,
		lenderCont.RemoveTagController,
	).Post("/removeTag", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.DeactivateUserReq,
		lenderCont.DeactivateUserCont,
	).Post("/deactivateUser", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UploadAdditionalDocsReq,
		lenderCont.UploadAdditionalDocsCont,
	).Post("/uploadDocs", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetAdditionalDocsReq,
		lenderCont.GetAdditionalDocsCont,
	).Get("/docs", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.GetDocumentsReq,
		app.Srv.DashboardService.GetDocumentsCont,
	).Post("/get-documents", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.DownloadDocumentsReq,
		app.Srv.DashboardService.DownloadDocumentsCont,
	).Post("/download-documents", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.ListMastersDataReq,
		app.Srv.DashboardService.ListMastersDataCont,
	).Post("/list-masters-data", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.DeleteDocumentReq,
		lenderCont.DeleteDocumentCont,
	).Post("/deleteDocs", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.AddNoteReq,
		lenderCont.AddNoteCont,
	).Post("/addNote", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetNotesReq,
		lenderCont.GetNotesCont,
	).Get("/notes", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CheckExistingMDEmailReq,
		lenderCont.CheckExistingMDEmailCont,
	).Post("/checkExistingMDEmail", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateDSACredsReq,
		lenderCont.UpdateDSACredsCont,
	).Post("/updateDSACreds", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CloseApplicationReq,
		lenderCont.CloseApplicationCont,
	).Post("/closeApplication", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateCurrentAddressReq,
		lenderCont.UpdatePermanentAddressCont,
	).Post("/updatePermanentAddress", responseCommon.GenericRes)

	// internal
	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetPincodeResponseFileReq,
		lenderCont.GetPincodeResponseFileCont,
	).Get("/getPincodeResponseFile", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.GetPreApprovalSheetTemplateCont,
	).Get("/getPreApproveCustomers", responseCommon.CSVResponse)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.PreApproveReq,
		lenderCont.PreApproveCont,
	).Post("/preApproveCustomers", responseCommon.GenericRes)

	// Top-UP
	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetTopUpEligibilityReq,
		lenderCont.GetTopUpEligibilityCont,
	).Get("/getTopUpEligibility", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.PostTopUpEligibilityReq,
		lenderCont.PostTopUpEligibilityCont,
	).Post("/postTopUpEligibility", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.PreApproveReq,
		lenderCont.RevokePreApprovalCont,
	).Delete("/revokePreApproveCustomers", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanListReq,
		lenderCont.LoanStatusAggregateCount,
	).Get("/loanStatusAggregateCount", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.ListGroupsCont,
	).Get("/rbac/getAllGroups", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetGroupPermissionsReq,
		lenderCont.ListGroupPermissionCont,
	).Post("/rbac/getGroupPermissions", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.AddNewGroupPermissionsReq,
		lenderCont.AddNewGroupPermissionsCont,
	).Post("/rbac/addNewGroupPermissions", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateGroupPermissionsReq,
		lenderCont.UpdateGroupPermissionsCont,
	).Post("/rbac/updateGroupPermissions", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.DeleteGroupPermissionsReq,
		lenderCont.DeleteGroupPermissionsCont,
	).Post("/rbac/deleteGroupPermissions", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.DeleteGroupReq,
		lenderCont.DeleteGroupCont,
	).Post("/rbac/deleteGroup", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.GetPageAPIMappingCont,
	).Get("/rbac/getPageAPIMapping", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateUserGroupReq,
		lenderCont.UpdateUserGroupCont,
	).Post("/rbac/updateUserGroup", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.DeleteUsersReq,
		lenderCont.DeleteUsersCont,
	).Post("/rbac/deleteUsers", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ActivateUsersReq,
		lenderCont.ActivateUsersCont,
	).Post("/rbac/activateUsers", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetAllUsersReq,
		lenderCont.GetAllUsersCont,
	).Get("/rbac/getAllUsers", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateMFAStatusReq,
		lenderCont.UpdateMFAStatusCont,
	).Post("/rbac/updateMFAStatus", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.AddMemberReq,
		lenderCont.AddMemberCont,
	).Post("/rbac/addMember", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ValidateGSTINReq,
		lenderCont.ValidateGSTINCont,
	).Post("/validateGstin", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ValidateBankAccountReq,
		lenderCont.ValidateBankAccountCont,
	).Post("/validateBankAccount", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ApproveLoanKYCReq,
		lenderCont.ApproveLoanKYCCont,
	).Post("/approveLoanKyc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateManualReq,
		lenderCont.UpdateManualKYCCont,
	).Post("/updateManualKyc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.SubmitKYCReq,
		lenderCont.ResubmitKYCCont,
	).Post("/resubmitKYC", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RejectSingleDocReq,
		lenderCont.RejectSingleDocCont,
	).Post("/rejectSingleDoc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.SubmitKYCReq,
		lenderCont.SubmitKYCCont,
	).Post("/submitKYC", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.UploadDashboardMediaCont,
	).Post("/uploadDashboardMedia", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetWFsReq,
		lenderCont.GetWFsCont,
	).Get("/getWorkflows", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetKYCDetailsCont,
	).Get("/getKYCDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetReferenceDataReq,
		lenderCont.GetReferenceDataCont,
	).Get("/getReferenceData", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.RequestKYCDocuments,
	).Post("/requestKYCDoc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetWFTimelineReq,
		lenderCont.GetWFTimelineCont,
	).Get("/getWorkflowTimeline", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetOfferNegotiationStatusCont,
	).Get("/getOfferNegotiationWFStatus", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetBREWFTimelineReq,
		lenderCont.GetBREWFTimelineCont,
	).Get("/getBREWorkflowTimeline", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetManualCreditReviewDetailsCont,
	).Get("/getManualCreditReviewDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateWorkFlowStatusReq,
		lenderCont.ApproveManualCreditReviewDetailsCont,
	).Post("/approveManualCreditReviewDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateWorkFlowStatusReq,
		lenderCont.RejectManualCreditReviewDetailsCont,
	).Post("/rejectManualCreditReviewDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.BreWorkflowStateTransitionReq,
		lenderCont.BreWorkflowStateTransitionCont,
	).Post("/breWorkflowStateTransition", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.ApproveBusinessDocs,
	).Post("/approveBusinessDocs", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.GetWorkflowTaskReport,
	).Post("/getWorkflowTaskReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateAdditionalKYCDocReq,
		lenderCont.UpdateAdditonalKYCDoc,
	).Post("/updateAdditionalKYCDoc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderCont.AddAdditonalKYCDoc,
	).Post("/addAdditionalKYCDoc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.DownloadCIBILReportCont,
	).Get("/downloadCIBILReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetCAMReportsCont,
	).Get("/getCAMReports", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetProcessedReportCont,
	).Get("/getCibilReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.LoanDedupeCheckListCont,
	).Get("/loanDedupeChecksList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.LoanDedupeChecksReq,
		lenderCont.LoanDedupeChecksCont,
	).Get("/loanDedupeChecks", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateBusinessIndustryReq,
		lenderCont.UpdateBusinessIndustryCont,
	).Post("/updateBusinessIndustry", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetInsuranceDetailsCont,
	).Get("/getInsuranceDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateInsuranceDetailsReq,
		lenderCont.UpdateInsuranceDetailsCont,
	).Post("/updateInsuranceDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetQueryParams[lenderReq.GetSimulatedBCSessionURLStruct],
		lenderCont.GetSimulatedBCSessionURLCont,
	).Get("/getSimulatedBCSessionURL", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetRequestBody[lenderReq.GetSimulatedOffer],
		lenderCont.GetSimulatedOfferCont,
	).Post("/simulateOffer", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetQueryParams[lenderReq.GetSimulationPredictors],
		lenderCont.GetSimulationPredictorsCont,
	).Get("/getSimulationPredictors", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		lenderCont.GetProcessedPDFReportCont,
	).Get("/getCibilPDFReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.ABFLCustomReportReq,
		lenderCont.ABFLCustomReportCont,
	).Post("/abflCustomReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CustomReportReq,
		lenderCont.ABFLUTMReportCont,
	).Post("/abflUTMReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetQueryParams[lenderReq.GetSimulatedBCReport],
		lenderCont.GetSimulatedBankConnectReportCont,
	).Get("/getSimulatedBankConnectReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetCategoriesDropdown,
		app.Srv.LenderDashBoardService.GetCategoriesDropdown,
	).Get("/dropdownL2", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.PushAdditionalDocs,
		app.Srv.LenderDashBoardService.PushAdditionalDocs,
	).Post("/pushAdditionalDocs", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.WorkflowRejectionReversalReq,
		lenderCont.WorkflowRejectionReversalCont,
	).Post("/workflowRejectionReversal", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetRequestBody[lenderReq.UpdateBCSimulationStage],
		lenderCont.UpdateBCSimulationStage,
	).Post("/updateBCSimulationStage", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetRequestBody[lenderReq.ConvertSimulationToOffer],
		lenderCont.ConvertSimulationToOffer,
	).Post("/convertSimulationToOffer", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetWorkflowRequests,
		app.Srv.LenderDashBoardService.GetWorkflowRequests,
	).Get("/workflow/workflowTasks", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		app.Srv.LenderDashBoardService.GetWorkflowAllFilters,
	).Get("/workflow/allFilters", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetWorkflowFilteredTasks,
		app.Srv.LenderDashBoardService.GetWorkflowFilteredTasks,
	).Get("/workflow/getFilteredTasks", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetWorkflowFilteredUsers,
		app.Srv.LenderDashBoardService.GetWorkflowFilteredUsers,
	).Get("/workflow/getFilteredUsers", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetGroupUsers,
		app.Srv.LenderDashBoardService.GetGroupUsers,
	).Get("/workflow/groupUsers", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetWorkflowAssingedTaskUsers,
		app.Srv.LenderDashBoardService.GetWorkflowAssingedTaskUsers,
	).Get("/workflow/getAssignedTaskUsers", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetFilteredTaskForBulkClaim,
		app.Srv.LenderDashBoardService.GetFilteredTaskForBulkClaim,
	).Get("/workflow/filteredTaskForBulkClaim", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.WorkflowBulkUnAssignTaskFromUsers,
		app.Srv.LenderDashBoardService.WorkflowBulkUnAssignTaskFromUsers,
	).Put("/workflow/bulkUnAssignTaskFromUsers", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.BulkAssignTask,
		app.Srv.LenderDashBoardService.BulkAssignTask,
	).Put("/workflow/bulkAssignTask", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.BulkClaimTask,
		app.Srv.LenderDashBoardService.BulkClaimTask,
	).Put("/workflow/bulkClaimTask", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.WorkflowAssignTask,
		app.Srv.LenderDashBoardService.WorkflowAssignTask,
	).Put("/workflow/assignTask", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.WorkflowUnAssignTask,
		app.Srv.LenderDashBoardService.WorkflowUnAssignTask,
	).Put("/workflow/unAssignTask", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.WorkflowReAssignTask,
		app.Srv.LenderDashBoardService.WorkflowReAssignTask,
	).Put("/workflow/reAssignTask", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.WorkflowMarkTaskInitiated,
		app.Srv.LenderDashBoardService.WorkflowMarkTaskInitiated,
	).Put("/workflow/markTaskInitiated", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetRequestBody[lenderReq.ConvertSimulationToOffer],
		lenderCont.ConvertSimulationToOffer,
	).Post("/convertSimulationToOffer", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetQueryParams[lenderReq.GetSimulationHistory],
		lenderCont.GetSimulationHistoryCont,
	).Get("/getSimulationHistory", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetQueryParams[lenderReq.GetSimulationDetails],
		lenderCont.GetSimulationDetailsCont,
	).Get("/getSimulationDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetLoanDetailsReq,
		app.Srv.LenderDashBoardService.GetCoApplicantsKYC,
	).Get("/getCoApplicantsLoanKYC", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.AddReferencesForUser,
		lenderCont.AddReferencesForUser,
	).Put("/addReferencesForUser", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.CreateCoApplicant,
		app.Srv.DashboardService.CreateCoApplicant,
	).Put("/createCoApplicant", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateCoApplicant,
		app.Srv.LenderDashBoardService.UpdateCoApplicant,
	).Put("/updateCoApplicant", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateCoApplicantKYCDocReviewStatusReq,
		lenderCont.UpdateCoApplicantKYCDocReviewStatusCont,
	).Put("/updateCoApplicantDocReviewStatus", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetCoApplicantList,
		app.Srv.LenderDashBoardService.GetCoApplicantList,
	).Get("/getCoApplicants", responseCommon.GenericRes)

	r.With(auth.LenderAuthFilter,
		lenderReq.GetUpdateDirectorReq,
		app.Srv.LenderDashBoardService.UpdateDirector,
	).Put("/updateDirectorDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.MarkNACHSuccessReq,
		lenderCont.MarkNACHSuccessCont,
	).Post("/markNACHSuccess", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		app.Srv.LenderDashBoardService.AddCoApplicantKYCDocCont,
	).Post("/addCoApplicantKYCDoc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.DeleteCoApplicantReq,
		app.Srv.LenderDashBoardService.DeleteCoApplicantCont,
	).Put("/deleteCoApplicant", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		app.Srv.LenderDashBoardService.UpdateCoApplicantKYCDocCont,
	).Post("/updateCoApplicantKYCDoc", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CheckPincodeWithoutSourceEntityReq,
		lenderCont.CheckPincodeCont,
	).Get("/checkPincode", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetRequestBody[lenderReq.UpdateNameReq],
		lenderCont.UpdateNameCont,
	).Post("/updateName", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		servicesReq.CSVFileUploadReq,
		lenderCont.BulkGCIDUploadCont,
	).Post("/BulkGCIDUpload", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetUserLogsReq,
		lenderContV2.GetUserLogsCont,
	).Get("/getUserLogs", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetWorkflowKeyReq,
		app.Srv.LenderDashBoardService.GetWorkflowKeyCont,
	).Get("/workflowKey", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateUdyamDetailsReq,
		lenderCont.UpdateUdyamDetailsCont,
	).Post("/updateUdyamDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetRequestBody[lenderReq.RecomputeBCPredictorsReq],
		lenderCont.RecomputeBCPredictorsCont,
	).Post("/recomputeBCPredictors", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		dashboardRequestV2.VKycInvokeReq,
		lenderContV2.VKycInvokeCont,
	).Post("/invokeVKYC", responseCommon.GenericRes)

	r.With(
		dashboardRequestV2.VKycWebhookReq,
		lenderContV2.VKycWebhookCont,
	).Post("/vKycWebhook", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetCamReportReq,
		lenderCont.GetCAMReportCont,
	).Post("/getCamReport", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateUTRDetailsReq,
		lenderCont.UpdateUTRDetailsCont,
	).Post("/updateUTRDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.UpdateDisbursalDetailsReq,
		lenderCont.UpdateDisbursalDetailsCont,
	).Post("/updateDisbursalDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.CancelLoanBulkReq,
		lenderCont.CancelLoanBulkCont,
	).Post("/cancelLoanBulk", responseCommon.GenericRes)

	r.With(
		dashboardRequestV2.GetUserGraphQLReq,
	).Post("/graphql-server", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.GetSentinelPolicyLinkReq,
		lenderCont.GetSentinelPolicyLinkCont,
	).Get("/sentinelPolicyLink", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetLoanOfferDetailReq,
		lenderContV2.GetLoanOfferDetail,
	).Get("/loanOfferDetail", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		lenderReq.RetriggerCoAppBureauReq,
		app.Srv.LenderDashBoardService.RetriggerCoAppBureauCont,
	).Post("/retriggerCoAppBureau", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateUserHunterStatusReq,
		lenderContV2.UpdateUserHunterStatusCont,
	).Post("/updateHunterDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		userReq.RegenerateKYCDocsReq,
		kycCont.RegenerateKYCDocs,
	).Post("/regenrate-kyc-docs", responseCommon.GenericResV3)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetMarginReq,
		lenderContV2.GetMarginCont,
	).Get("/getMargin", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateUserMetaDataReq,
		lenderContV2.UpdateUserMetaDataCont,
	).Post("/updateUserMetadata", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.SendSessionLinkToCustomerReq,
		lenderContV2.SendSessionToCustomerCont,
	).Post("/sendSessionLinkToCustomer", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateRelationManagerDetailsReq,
		lenderContV2.UpdateRelationManagerDetailsCont,
	).Post("/updateRelationManagerDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetDropdownConfigReq,
		lenderContV2.GetDropdownConfigCont,
	).Get("/getDropdownConfig", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.FetchUIConfigReq,
		app.Srv.DashboardService.FetchUIConfig,
	).Post("/fetch-uiconfig", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.FethUIPermissionsReq,
		app.Srv.DashboardService.FetchUIPermissions,
	).Post("/fetch-ui-permissions", responseCommon.GenericRes)

	// Route registration
	// Generic POST endpoint that acts as a bridge for executing various GraphQL queries with pagination support.
	// POST method is used as request body contains complex query parameters and follows GraphQL convention of using POST for queries.
	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.FetchGraphQLDataReq,
		lenderContV2.FetchDataByConfigAndGraphQL,
	).Post("/fetch-graphql-data", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.CreateDeviationReq,
		lenderContV2.CreateDeviationCont,
	).Post("/loan-deviation", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateDeviationReq,
		lenderContV2.UpdateDeviationCont,
	).Post("/update-deviation", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetLoanDeviationReq,
		app.Srv.DashboardService.GetLoanDeviationCont,
	).Get("/get-loan-deviation", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetDeviationRuleReq,
		lenderContV2.GetDeviationRuleCont,
	).Get("/get-deviation-rule", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.ApproveBankFraud,
		lenderContV2.ApproveBankFraud,
	).Post("/approve-bank-fraud", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.HandleEventDetailsReq,
		app.Srv.DashboardService.HandleEventDetailsCont,
	).Post("/handle-event", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateCreditAndRCUReq,
		lenderContV2.UpdateCreditAndRCUCont,
	).Post("/update-credit-rcu-task", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetQuestionnaireReq,
		app.Srv.DashboardService.GetQuestionnaireCont,
	).Get("/questionnaire", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		lenderReq.UpdateLenderDropdownReq,
		lenderCont.UpdateLenderDropdownCont,
	).Put("/lenderdropdown", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.FetchInvestigationDetailsReq,
		app.Srv.DashboardService.FetchInvestigationDetailsCont,
	).Get("/fetchInvestigationDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetCreditAndRCUReq,
		lenderContV2.GetCreditAndRCUCont,
	).Get("/credit-rcu-task", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetTaskListReq,
		app.Srv.DashboardService.GetTaskListCont,
	).Post("/getTaskList", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetTasksByLenderAndTypeReq,
		app.Srv.DashboardService.GetTasksByLenderAndTypeCont,
	).Post("/getTasksByLenderAndType", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetSoftApproveStatusReq,
		app.Srv.DashboardService.GetSoftApproveStatusCont,
	).Get("/getSoftApproveStatus", responseCommon.GenericRes)
	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateSoftApproveStatusReq,
		app.Srv.DashboardService.UpdateSoftApproveStatusCont,
	).Post("/updateSoftApproveStatus", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		lenderReq.UpdateLenderDropdownReq,
		lenderCont.UpdateLenderDropdownCont,
	).Put("/lenderdropdown", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		app.Srv.DashboardService.LogoutCont,
	).Post("/logout", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateAdditionalDocsReq,
		lenderContV2.UpdateAdditionalDocsCont,
	).Post("/updateAdditionalDocs", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.BulkLoanClosureReq,
		app.Srv.DashboardService.BulkLoanClosureCont,
	).Post("/bulk-loan-closure", responseCommon.GenericRes)

	r.With(
		auth.LenderServerAuthFilter,
		dashboardRequestV2.CreateLenderUserReq,
		lenderContV2.CreateLenderUserCont,
	).Put("/createLenderUser", responseCommon.GenericRes)

	r.With(
		auth.LenderServerAuthFilter,
		dashboardRequestV2.UpdateLenderUserReq,
		lenderContV2.UpdateLenderUserCont,
	).Post("/updateLenderUser", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.WorkflowAction,
		app.Srv.DashboardService.WorkflowActionCont,
	).Post("/workflow-action", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.WorkflowData,
		lenderContV2.WorkflowDataCont,
	).Get("/workflow-data", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.UpdateGSTDetailsReq,
		lenderContV2.UpdateGSTDetailsCont,
	).Post("/updateGSTDetails", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetThirdPartReq,
		app.Srv.DashboardService.GetThirdPartyCont,
	).Post("/third-party-data", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.SearcLenderDropDownReq,
		lenderCont.SearchLenderDropDownCont,
	).Post("/lenderDropdownSearch", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.TriggerFieldInvestigationReq,
		lenderContV2.TriggerFieldInvestigationCont,
	).Post("/trigger-field-investigation", responseCommon.GenericRes)

	r.With(
		//auth.LenderAuthFilterV2,
		//dashboardAuthorization.Authorization,
		dashboardRequestV2.FieldInvestigationCallbackReq,
		lenderContV2.FieldInvestigationCallbackCont,
	).Post("/field-investigation-callback", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetFieldInvestigationDetailsReq,
		lenderContV2.GetFieldInvestigationDetailsCont,
	).Get("/get-field-investigation-details", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetGenericDropDownReq,
		lenderContV2.GetGenricDropDownCont,
	).Post("/generic-dropdown-list", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetPennyDropReq,
		lenderContV2.DoPennyDrop,
	).Post("/doPennyDrop", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.FetchUIBuilderConfigReq,
		app.Srv.DashboardService.FetchUIBuilderConfig,
	).Post("/fetch-uibuilder-config", responseCommon.GenericRes)

	r.With(
		dashboardRequestV2.GenerateWebLink,
		app.Srv.DashboardService.GenerateWebLink,
	).Post("/generate-journey-web-link", responseCommon.GenericRes)

	r.With(
		dashboardRequestV2.GetUnleashFlagReq,
	).Post("/unleash-test", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.CreateCoApplicantV2Req,
		app.Srv.DashboardService.CreateCoApplicantV2,
	).Post("/add-coapplicant", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.UpsertReferenceReq,
		app.Srv.DashboardService.UpsertReferenceCont,
	).Post("/upsert-reference", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		lenderCont.UpdateLenderDropdownV2Cont,
	).Put("/lenderdropdownV2", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.CreateDisbursement,
		lenderContV2.CreateDisbursement,
	).Post("/create-disbursement", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.ListDisbursementReq,
		lenderContV2.ListDisbursements,
	).Get("/fetch-disbursements", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.SubmitFormDataReq,
		dashboardAuthorization.RedGateAuthorization,
		app.Srv.DashboardService.SubmitFormDataCont,
	).Post("/submit-form-data", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.SubmitFormDataReq,
		app.Srv.DashboardService.SetLoanOfferData,
	).Post("/single-submit-loanoffer", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.ValidateFormDataReq,
		app.Srv.DashboardService.ValidateFormDataCont,
	).Post("/validate-form-data", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.InitGenerateDocsWorkflowReq,
		app.Srv.DashboardService.InitGenerateDocsWorkflowCont,
	).Post("/workflow/init-generate-docs", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetESignAttemptReq,
		app.Srv.DashboardService.GetESignAttemptCont,
	).Get("/e-sign-attempt", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.CreateESignAttemptReq,
		app.Srv.DashboardService.CreateESignAttemptCont,
	).Post("/e-sign-attempt", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardAuthorization.Authorization,
		dashboardRequestV2.GetInsuranceConfigDataReq,
		app.Srv.DashboardService.GetInsuranceWorkflowDataCont,
	).Get("/insurance-config-data", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.GenerateOTPReq,
		app.Srv.DashboardService.GenerateEmailOTPCont,
	).Post("/generate-email-otp", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.VerifyOTPReq,
		app.Srv.DashboardService.VerifyOTPCont,
	).Post("/verify-email-otp", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.MyQueueData,
		lenderContV2.MyQueueData,
	).Post("/my-queue", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilterV2,
		dashboardRequestV2.AssignWorkflow,
		lenderContV2.AssignWorkflow,
	).Post("/assign-workflow", responseCommon.GenericRes)
}
