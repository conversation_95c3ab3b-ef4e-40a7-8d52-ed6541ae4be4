package models

import (
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"finbox/go-api/functions/logger"
	"finbox/go-api/studio/constants"
	"finbox/go-api/utils/general"
	"fmt"
)

type Group struct {
	GroupID                   string                `json:"groupID" db:"group_id"`
	OrganizationID            string                `json:"organizationID" db:"organization_id"`
	ParentGroupID             string                `json:"parentGroupID"`
	DBParentGroupIDNullable   sql.NullString        `json:"-" db:"parent_group_id"`
	Status                    string                `json:"status" db:"status"`
	GroupConfig               GroupConfig           `json:"groupConfig"`
	DBGroupConfig             string                `json:"-" db:"group_config"`
	ChangelogText             string                `json:"changelog"`
	DBChangelogNullable       sql.NullString        `json:"-" db:"changelog"`
	GroupName                 string                `json:"groupName" db:"group_name"`
	CreatedAt                 string                `json:"createdAt" db:"created_at"`
	UpdatedAt                 string                `json:"updatedAt"`
	DBUpdatedAtNullable       sql.NullString        `json:"-" db:"updated_at"`
	DashboardUserID           string                `json:"dashboardUserID"`
	DBDashboardUserIDNullable sql.NullString        `json:"-" db:"dashboard_user_id"`
	Rollouts                  []GroupSourceRollouts `db:"-" json:"rollouts"`
	ProgramName               string                `json:"programName" db:"program_name"`
	SourceEnvironment         string                `json:"sourceEnvironment,omitempty"`
	DefaultSourceEntityID     string                `json:"defaultSourceEntityID" db:"default_source_entity_id"`
}

type GroupBase64 struct {
	GroupEnc       string `json:"groupEnc"`
	SourceEntityID string `json:"sourceEntityID"`
}

func (g *GroupBase64) GetGroup() (*Group, error) {

	var group Group
	bytes, err := base64.StdEncoding.DecodeString(g.GroupEnc)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(bytes, &group)
	if err != nil {
		return nil, err
	}
	return &group, nil
}

type ChildWorkflowConfigDetails struct {
	WorkflowConfigDetails
	ResourceName string `json:"resourceName"`
}

type WorkflowConfigDetails struct {
	ConfigID           string           `json:"configID"`
	IsStaged           bool             `json:"isStaged"`
	BackendConfigData  *json.RawMessage `json:"backendConfigData,omitempty"`
	BusinessConfigData *json.RawMessage `json:"businessConfigData,omitempty"`
}

type SectionConfigDetails struct {
	SubModuleName string           `json:"subModuleName"`
	ConfigID      string           `json:"configID"`
	IsStaged      bool             `json:"isStaged"`
	ConfigData    *json.RawMessage `json:"configData,omitempty"`
}

type SingleSubmitConfigDetails struct {
	ResourceName string           `json:"resourceName"`
	ConfigID     string           `json:"configID"`
	IsStaged     bool             `json:"isStaged"`
	ConfigData   *json.RawMessage `json:"configData,omitempty"`
}

type GroupConfig struct {
	Modules          []GroupModuleConfig `json:"modules"`
	TSMConfig        TSMConfig           `json:"tsmConfig"`
	ChildGroupConfig *GroupConfig        `json:"childGroupConfig,omitempty"`
}

type TSMConfig struct {
	ModuleName     string                `json:"moduleName"`
	WorkflowConfig WorkflowConfigDetails `json:"workflowConfig"`
}

type GroupModuleConfig struct {
	ModuleName          string                       `json:"moduleName"`
	WorkflowConfig      WorkflowConfigDetails        `json:"workflowConfig"`
	SectionConfig       []SectionConfigDetails       `json:"sectionConfig"`
	SingleSubmitConfig  []SingleSubmitConfigDetails  `json:"singleSubmitConfig"`
	ChildWorkflowConfig []ChildWorkflowConfigDetails `json:"childWorkflowConfig,omitempty"`
}

func (g *Group) GetGroupConfigJSON() (string, error) {

	bytes, err := json.Marshal(g.GroupConfig)
	return string(bytes), err
}

func (g *Group) IsPublished() bool {
	return g.Status == constants.StatusPublished
}

func (g *Group) IsDraft() bool {
	return g.Status == constants.StatusDraft
}

func (g *Group) IsDeployedToPROD() bool {
	return g.Status == constants.StatusDeployedToPROD
}

func (g *Group) ModuleExists(moduleName string, childGroup bool) bool {
	if childGroup && g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
				return true
			}
		}
	} else {
		for i := range g.GroupConfig.Modules {
			if g.GroupConfig.Modules[i].ModuleName == moduleName {
				return true
			}
		}
	}
	return false
}

func (g *Group) GetWorkflowConfigDetails(moduleName string) WorkflowConfigDetails {
	var workflowDetails WorkflowConfigDetails

	if g.GroupConfig.TSMConfig.ModuleName == moduleName {
		return g.GroupConfig.TSMConfig.WorkflowConfig
	}

	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			return g.GroupConfig.Modules[i].WorkflowConfig
		}
	}
	return workflowDetails

}

func (g *Group) GetChildGroupWorkflowConfigDetails(moduleName string) WorkflowConfigDetails {
	var workflowDetails WorkflowConfigDetails

	if g.GroupConfig.ChildGroupConfig == nil {
		return workflowDetails
	}

	if g.GroupConfig.ChildGroupConfig.TSMConfig.ModuleName == moduleName {
		return g.GroupConfig.ChildGroupConfig.TSMConfig.WorkflowConfig
	}

	for _, module := range g.GroupConfig.ChildGroupConfig.Modules {
		if module.ModuleName == moduleName {
			return module.WorkflowConfig
		}
	}
	return workflowDetails

}

func (g *Group) GetChildWorkflowConfigDetails(moduleName, resourceName string) WorkflowConfigDetails {
	var workflowDetails WorkflowConfigDetails

	for _, module := range g.GroupConfig.Modules {
		if module.ModuleName == moduleName {
			for _, childWorkflow := range module.ChildWorkflowConfig {
				if childWorkflow.ResourceName == resourceName {
					return childWorkflow.WorkflowConfigDetails
				}
			}
		}
	}
	return workflowDetails
}

func (g *Group) GetChildWorkflowConfigDetailsForChildGroup(moduleName, resourceName string) WorkflowConfigDetails {
	var workflowDetails WorkflowConfigDetails

	if g.GroupConfig.ChildGroupConfig == nil {
		return workflowDetails
	}

	for _, module := range g.GroupConfig.ChildGroupConfig.Modules {
		if module.ModuleName == moduleName {
			for _, childWorkflow := range module.ChildWorkflowConfig {
				if childWorkflow.ResourceName == resourceName {
					return childWorkflow.WorkflowConfigDetails
				}
			}
		}
	}
	return workflowDetails

}

func (g *Group) GetWorkflowConfigDetailsByID(configID string) (WorkflowConfigDetails, bool, string) {
	var workflowDetails WorkflowConfigDetails

	if g.GroupConfig.TSMConfig.WorkflowConfig.ConfigID == configID {
		return g.GroupConfig.TSMConfig.WorkflowConfig, true, "TEMPORAL_STATE_MANAGEMENT"
	}

	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].WorkflowConfig.ConfigID == configID {
			return g.GroupConfig.Modules[i].WorkflowConfig, false, g.GroupConfig.Modules[i].ModuleName
		}
	}
	return workflowDetails, false, ""

}

func (g *Group) GetChildWorkflowConfigDetailsByID(configID string) (WorkflowConfigDetails, string, string) {
	var workflowDetails WorkflowConfigDetails

	for i := range g.GroupConfig.Modules {
		for j := range g.GroupConfig.Modules[i].ChildWorkflowConfig {
			if g.GroupConfig.Modules[i].ChildWorkflowConfig[j].ConfigID == configID {
				return g.GroupConfig.Modules[i].ChildWorkflowConfig[j].WorkflowConfigDetails, g.GroupConfig.Modules[i].ModuleName, g.GroupConfig.Modules[i].ChildWorkflowConfig[j].ResourceName
			}
		}
	}
	return workflowDetails, "", ""
}

func (g *Group) GetChildWorkflowConfigDetailsForChildGroupByID(configID string) (WorkflowConfigDetails, string, string) {
	var workflowDetails WorkflowConfigDetails

	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig {
				if g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].ConfigID == configID {
					return g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].WorkflowConfigDetails, g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName, g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].ResourceName
				}
			}
		}
	}

	return workflowDetails, "", ""
}

func (g *Group) GetChildGroupWorkflowConfigByID(configID string) (WorkflowConfigDetails, bool, string) {
	var workflowDetails WorkflowConfigDetails

	if g.GroupConfig.ChildGroupConfig == nil {
		return workflowDetails, false, ""
	}

	if g.GroupConfig.ChildGroupConfig.TSMConfig.WorkflowConfig.ConfigID == configID {
		return g.GroupConfig.ChildGroupConfig.TSMConfig.WorkflowConfig, true, "TEMPORAL_STATE_MANAGEMENT"
	}

	for i := range g.GroupConfig.ChildGroupConfig.Modules {
		if g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.ConfigID == configID {
			return g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig, false, g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName
		}
	}
	return workflowDetails, false, ""

}

func (g *Group) GetSubModuleConfigDetails(moduleName, subModuleName string) SectionConfigDetails {
	var subModuleDetails SectionConfigDetails

	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SectionConfig {
				if g.GroupConfig.Modules[i].SectionConfig[j].SubModuleName == subModuleName {
					return g.GroupConfig.Modules[i].SectionConfig[j]
				}
			}
		}
	}
	return subModuleDetails

}

func (g *Group) GetChildGroupSubModuleConfigDetails(moduleName, subModuleName string) SectionConfigDetails {
	var subModuleDetails SectionConfigDetails

	if g.GroupConfig.ChildGroupConfig == nil {
		return subModuleDetails
	}

	for _, module := range g.GroupConfig.ChildGroupConfig.Modules {
		if module.ModuleName == moduleName {
			for _, sectionConfig := range module.SectionConfig {
				if sectionConfig.SubModuleName == subModuleName {
					return sectionConfig
				}
			}
		}
	}
	return subModuleDetails

}

func (g *Group) GetGroupSubModuleConfigByID(configID string) (SectionConfigDetails, string) {
	var subModuleDetails SectionConfigDetails
	for _, module := range g.GroupConfig.Modules {
		for _, sc := range module.SectionConfig {
			if sc.ConfigID == configID {
				return sc, module.ModuleName
			}
		}
	}
	return subModuleDetails, ""

}

func (g *Group) GetChildGroupSubModuleConfigByID(configID string) (SectionConfigDetails, string) {
	var subModuleDetails SectionConfigDetails

	if g.GroupConfig.ChildGroupConfig == nil {
		return subModuleDetails, ""
	}

	for _, module := range g.GroupConfig.ChildGroupConfig.Modules {
		for _, sc := range module.SectionConfig {
			if sc.ConfigID == configID {
				return sc, module.ModuleName
			}
		}
	}
	return subModuleDetails, ""

}

func (g *Group) GetAllModuleNames() []string {

	modules := make([]string, len(g.GroupConfig.Modules))
	for i := range g.GroupConfig.Modules {
		modules = append(modules, g.GroupConfig.Modules[i].ModuleName)
	}
	return modules

}

func (g *Group) GetAllWorkflowConfigIDs() []string {

	workflowConfigIDs := make([]string, len(g.GroupConfig.Modules))
	for i := range g.GroupConfig.Modules {
		workflowConfigIDs = append(workflowConfigIDs, g.GroupConfig.Modules[i].WorkflowConfig.ConfigID)
	}
	return workflowConfigIDs
}

func (g *Group) GetAllSectionConfigIDs() []string {

	sectionConfigIDs := make([]string, len(g.GroupConfig.Modules))
	for i := range g.GroupConfig.Modules {
		for j := range g.GroupConfig.Modules[i].SectionConfig {
			sectionConfigIDs = append(sectionConfigIDs, g.GroupConfig.Modules[i].SectionConfig[j].ConfigID)
		}
	}
	return sectionConfigIDs
}

func (g *Group) GetSectionConfigIDsByModule(moduleName string) []string {

	var sectionConfigIDs []string
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SectionConfig {
				sectionConfigIDs = append(sectionConfigIDs, g.GroupConfig.Modules[i].SectionConfig[j].ConfigID)
			}
			return sectionConfigIDs
		}
	}
	return sectionConfigIDs
}

func (g *Group) GetChildSectionConfigIDsByModule(moduleName string) []string {

	var sectionConfigIDs []string
	for i := range g.GroupConfig.ChildGroupConfig.Modules {
		if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig {
				sectionConfigIDs = append(sectionConfigIDs, g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].ConfigID)
			}
			return sectionConfigIDs
		}
	}
	return sectionConfigIDs
}

func (g *Group) GetChildSingleSubmitConfigIDsByModule(moduleName string) []string {

	var singleSubmitIDs []string
	for i := range g.GroupConfig.ChildGroupConfig.Modules {
		if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig {
				singleSubmitIDs = append(singleSubmitIDs, g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID)
			}
			return singleSubmitIDs
		}
	}
	return singleSubmitIDs
}

func (g *Group) GetSingleSubmitConfigIDsByModule(moduleName string) []string {

	var singleSubmitIDs []string
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SingleSubmitConfig {
				singleSubmitIDs = append(singleSubmitIDs, g.GroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID)
			}
			return singleSubmitIDs
		}
	}
	return singleSubmitIDs
}

func (g *Group) GetStagedAndPublishedWorkflowConfigs() ([]WorkflowConfig, []WorkflowConfig) {

	var stagedWorkflowConfigIDs []WorkflowConfig
	var publishedWorkflowConfigIDs []WorkflowConfig
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].WorkflowConfig.IsStaged {
			stagedWorkflowConfigIDs = append(stagedWorkflowConfigIDs, WorkflowConfig{
				ConfigID:   g.GroupConfig.Modules[i].WorkflowConfig.ConfigID,
				ModuleName: g.GroupConfig.Modules[i].ModuleName,
			})
		} else {
			publishedWorkflowConfigIDs = append(publishedWorkflowConfigIDs, WorkflowConfig{
				ConfigID:   g.GroupConfig.Modules[i].WorkflowConfig.ConfigID,
				ModuleName: g.GroupConfig.Modules[i].ModuleName,
			})
		}

		for j := range g.GroupConfig.Modules[i].ChildWorkflowConfig {
			if g.GroupConfig.Modules[i].ChildWorkflowConfig[j].IsStaged {
				stagedWorkflowConfigIDs = append(stagedWorkflowConfigIDs, WorkflowConfig{
					ConfigID:   g.GroupConfig.Modules[i].ChildWorkflowConfig[j].ConfigID,
					ModuleName: g.GroupConfig.Modules[i].ModuleName,
				})
			} else {
				publishedWorkflowConfigIDs = append(publishedWorkflowConfigIDs, WorkflowConfig{
					ConfigID:   g.GroupConfig.Modules[i].ChildWorkflowConfig[j].ConfigID,
					ModuleName: g.GroupConfig.Modules[i].ModuleName,
				})
			}
		}
	}
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.IsStaged {
				stagedWorkflowConfigIDs = append(stagedWorkflowConfigIDs, WorkflowConfig{
					ConfigID:   g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.ConfigID,
					ModuleName: g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName,
				})
			} else {
				publishedWorkflowConfigIDs = append(publishedWorkflowConfigIDs, WorkflowConfig{
					ConfigID:   g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.ConfigID,
					ModuleName: g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName,
				})
			}

			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig {
				if g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].IsStaged {
					stagedWorkflowConfigIDs = append(stagedWorkflowConfigIDs, WorkflowConfig{
						ConfigID:   g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].ConfigID,
						ModuleName: g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName,
					})
				} else {
					publishedWorkflowConfigIDs = append(publishedWorkflowConfigIDs, WorkflowConfig{
						ConfigID:   g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].ConfigID,
						ModuleName: g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName,
					})
				}
			}
		}
	}
	return stagedWorkflowConfigIDs, publishedWorkflowConfigIDs
}

func (g *Group) GetTSMConfig() TSMConfig {
	return g.GroupConfig.TSMConfig
}

func (g *Group) GetChildTSMConfig() TSMConfig {
	if g.GroupConfig.ChildGroupConfig != nil {
		return g.GroupConfig.ChildGroupConfig.TSMConfig
	}
	return TSMConfig{}
}

func (g *Group) GetAllStagedAndPublishedSectionConfigs() ([]SectionConfig, []SectionConfig) {

	var stagedSectionConfigIDs []SectionConfig
	var publishedSectionConfigIDs []SectionConfig
	for i := range g.GroupConfig.Modules {
		for j := range g.GroupConfig.Modules[i].SectionConfig {
			if g.GroupConfig.Modules[i].SectionConfig[j].IsStaged {
				stagedSectionConfigIDs = append(stagedSectionConfigIDs, SectionConfig{
					ID:            g.GroupConfig.Modules[i].SectionConfig[j].ConfigID,
					ModuleName:    g.GroupConfig.Modules[i].ModuleName,
					SubModuleName: g.GroupConfig.Modules[i].SectionConfig[j].SubModuleName,
				})
			} else {
				publishedSectionConfigIDs = append(publishedSectionConfigIDs, SectionConfig{
					ID:            g.GroupConfig.Modules[i].SectionConfig[j].ConfigID,
					ModuleName:    g.GroupConfig.Modules[i].ModuleName,
					SubModuleName: g.GroupConfig.Modules[i].SectionConfig[j].SubModuleName,
				})
			}
		}
	}
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig {
				if g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].IsStaged {
					stagedSectionConfigIDs = append(stagedSectionConfigIDs, SectionConfig{
						ID:            g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].ConfigID,
						ModuleName:    g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName,
						SubModuleName: g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].SubModuleName,
					})
				} else {
					publishedSectionConfigIDs = append(publishedSectionConfigIDs, SectionConfig{
						ID:            g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].ConfigID,
						ModuleName:    g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName,
						SubModuleName: g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].SubModuleName,
					})
				}
			}
		}
	}
	return stagedSectionConfigIDs, publishedSectionConfigIDs
}

func (g *Group) GetStagedAndPublishedSingleSubmit() ([]SingleSubmitConfigDetails, []SingleSubmitConfigDetails) {
	var stagedSingleSubmits []SingleSubmitConfigDetails
	var publishedSingleSubmits []SingleSubmitConfigDetails
	for i := range g.GroupConfig.Modules {
		for j := range g.GroupConfig.Modules[i].SingleSubmitConfig {
			if g.GroupConfig.Modules[i].SingleSubmitConfig[j].IsStaged {
				stagedSingleSubmits = append(stagedSingleSubmits, SingleSubmitConfigDetails{
					ResourceName: g.GroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName,
					ConfigID:     g.GroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID,
				})
			} else {
				publishedSingleSubmits = append(publishedSingleSubmits, SingleSubmitConfigDetails{
					ResourceName: g.GroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName,
					ConfigID:     g.GroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID,
				})
			}
		}
	}
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig {
				if g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].IsStaged {
					stagedSingleSubmits = append(stagedSingleSubmits, SingleSubmitConfigDetails{
						ResourceName: g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName,
						ConfigID:     g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID,
					})
				} else {
					publishedSingleSubmits = append(publishedSingleSubmits, SingleSubmitConfigDetails{
						ResourceName: g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName,
						ConfigID:     g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID,
					})
				}
			}
		}
	}
	return stagedSingleSubmits, publishedSingleSubmits
}

func (g *Group) GetStagedandPublishedSectionConfigIDsByModule(moduleName string) ([]string, []string) {

	var stagedSectionConfigIDs []string
	var publishedSectionConfigIDs []string
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SectionConfig {
				if g.GroupConfig.Modules[i].SectionConfig[j].IsStaged {
					stagedSectionConfigIDs = append(stagedSectionConfigIDs, g.GroupConfig.Modules[i].SectionConfig[j].ConfigID)
				} else {
					publishedSectionConfigIDs = append(publishedSectionConfigIDs, g.GroupConfig.Modules[i].SectionConfig[j].ConfigID)
				}
			}
			return stagedSectionConfigIDs, publishedSectionConfigIDs
		}
	}
	return stagedSectionConfigIDs, publishedSectionConfigIDs
}

func (g *Group) GetStagedAndPublishedSingleSubmitIDsByModule(moduleName string) ([]SingleSubmitConfigDetails, []SingleSubmitConfigDetails) {

	var stagedSingleSubmits []SingleSubmitConfigDetails
	var publishedSingleSubmits []SingleSubmitConfigDetails
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SingleSubmitConfig {
				if g.GroupConfig.Modules[i].SingleSubmitConfig[j].IsStaged {
					stagedSingleSubmits = append(stagedSingleSubmits, g.GroupConfig.Modules[i].SingleSubmitConfig[j])
				} else {
					publishedSingleSubmits = append(publishedSingleSubmits, g.GroupConfig.Modules[i].SingleSubmitConfig[j])
				}
			}
			return stagedSingleSubmits, publishedSingleSubmits
		}
	}
	return stagedSingleSubmits, publishedSingleSubmits
}

func (g *Group) GetStagedAndPublishedChildSingleSubmitIDsByModule(moduleName string) ([]SingleSubmitConfigDetails, []SingleSubmitConfigDetails) {

	var stagedSingleSubmits []SingleSubmitConfigDetails
	var publishedSingleSubmits []SingleSubmitConfigDetails
	for i := range g.GroupConfig.ChildGroupConfig.Modules {
		if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig {
				if g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].IsStaged {
					stagedSingleSubmits = append(stagedSingleSubmits, g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j])
				} else {
					publishedSingleSubmits = append(publishedSingleSubmits, g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j])
				}
			}
			return stagedSingleSubmits, publishedSingleSubmits
		}
	}
	return stagedSingleSubmits, publishedSingleSubmits
}

func (g *Group) GetSingleSubmitConfigDetails(moduleName, resourceName string) SingleSubmitConfigDetails {
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SingleSubmitConfig {
				if g.GroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName == resourceName {
					return g.GroupConfig.Modules[i].SingleSubmitConfig[j]
				}
			}
		}
	}
	return SingleSubmitConfigDetails{}
}

func (g *Group) GetChildGroupSingleSubmitConfigDetails(moduleName, resourceName string) SingleSubmitConfigDetails {
	if g.GroupConfig.ChildGroupConfig != nil {
		for _, module := range g.GroupConfig.ChildGroupConfig.Modules {
			if module.ModuleName == moduleName {
				for _, singleSubmitConfig := range module.SingleSubmitConfig {
					if singleSubmitConfig.ResourceName == resourceName {
						return singleSubmitConfig
					}
				}
			}
		}
	}
	return SingleSubmitConfigDetails{}
}

func (g *Group) GetSingleSubmitConfigByID(configID string) (SingleSubmitConfigDetails, string) {
	for i := range g.GroupConfig.Modules {
		for j := range g.GroupConfig.Modules[i].SingleSubmitConfig {
			if g.GroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID == configID {
				return g.GroupConfig.Modules[i].SingleSubmitConfig[j], g.GroupConfig.Modules[i].ModuleName
			}
		}
	}
	return SingleSubmitConfigDetails{}, ""
}

func (g *Group) GetChildSingleSubmitConfigByID(configID string) (SingleSubmitConfigDetails, string) {
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig {
				if g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID == configID {
					return g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j], g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName
				}
			}
		}
	}
	return SingleSubmitConfigDetails{}, ""
}

func (g *Group) PopulateDBNullables() {
	if !g.DBParentGroupIDNullable.Valid {
		g.DBParentGroupIDNullable = sql.NullString{String: g.ParentGroupID, Valid: general.ValidateUUID(g.ParentGroupID)}
	}
	if !g.DBChangelogNullable.Valid {
		g.DBChangelogNullable = sql.NullString{String: g.ChangelogText, Valid: g.ChangelogText != ""}
	}
	if !g.DBDashboardUserIDNullable.Valid {
		g.DBDashboardUserIDNullable = sql.NullString{String: g.DashboardUserID, Valid: g.DashboardUserID != ""}
	}
	if !g.DBUpdatedAtNullable.Valid {
		g.DBUpdatedAtNullable = sql.NullString{String: g.UpdatedAt, Valid: g.UpdatedAt != ""}
	}
	bytes, _ := json.Marshal(g.GroupConfig)
	g.DBGroupConfig = string(bytes)
}

func (g *Group) GenerateGroupID() {
	g.GroupID = general.GetUUID()
}

func (g *Group) GetConfigJSON() (string, error) {
	bytes, err := json.Marshal(g.GroupConfig)
	if err != nil {
		return "{}", err
	}
	g.DBGroupConfig = string(bytes)
	return string(bytes), nil
}

func (g *Group) GetJSON() (string, error) {
	bytes, err := json.MarshalIndent(g, "", "  ")
	if err != nil {
		return "{}", err
	}
	return string(bytes), nil
}

func (g *Group) GetJSONBase64() (string, error) {
	bytes, err := json.Marshal(g)
	if err != nil {
		return "{}", err
	}
	return base64.StdEncoding.EncodeToString(bytes), nil
}

func (g *Group) MoveConfigsFromStagingToPublished() {
	for i := range g.GroupConfig.Modules {
		g.GroupConfig.Modules[i].WorkflowConfig.IsStaged = false
		for j := range g.GroupConfig.Modules[i].SectionConfig {
			g.GroupConfig.Modules[i].SectionConfig[j].IsStaged = false
		}
		for k := range g.GroupConfig.Modules[i].SingleSubmitConfig {
			g.GroupConfig.Modules[i].SingleSubmitConfig[k].IsStaged = false
		}
		for k := range g.GroupConfig.Modules[i].ChildWorkflowConfig {
			g.GroupConfig.Modules[i].ChildWorkflowConfig[k].IsStaged = false
		}
	}
	g.GroupConfig.TSMConfig.WorkflowConfig.IsStaged = false
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.IsStaged = false
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig {
				g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].IsStaged = false
			}
			for k := range g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig {
				g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[k].IsStaged = false
			}
			for k := range g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig {
				g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[k].IsStaged = false
			}
		}
		g.GroupConfig.ChildGroupConfig.TSMConfig.WorkflowConfig.IsStaged = false
	}
}

func (g *Group) ClearAllConfigData() {
	for i := range g.GroupConfig.Modules {
		g.GroupConfig.Modules[i].WorkflowConfig.BackendConfigData = nil
		g.GroupConfig.Modules[i].WorkflowConfig.BusinessConfigData = nil
		for j := range g.GroupConfig.Modules[i].SectionConfig {
			g.GroupConfig.Modules[i].SectionConfig[j].ConfigData = nil
		}
		for k := range g.GroupConfig.Modules[i].SingleSubmitConfig {
			g.GroupConfig.Modules[i].SingleSubmitConfig[k].ConfigData = nil
		}
	}
	g.GroupConfig.TSMConfig.WorkflowConfig.BackendConfigData = nil
	g.GroupConfig.TSMConfig.WorkflowConfig.BusinessConfigData = nil
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.BackendConfigData = nil
			g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.BusinessConfigData = nil
			for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig {
				g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].ConfigData = nil
			}
			for k := range g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig {
				g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[k].ConfigData = nil
			}
		}
		g.GroupConfig.ChildGroupConfig.TSMConfig.WorkflowConfig.BackendConfigData = nil
		g.GroupConfig.ChildGroupConfig.TSMConfig.WorkflowConfig.BusinessConfigData = nil
	}
}

func (g *Group) SetSectionConfigID(configID, moduleName, subModuleName string, isStaged bool) {
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SectionConfig {
				if g.GroupConfig.Modules[i].SectionConfig[j].SubModuleName == subModuleName {
					logger.Log.Warnln(configID, isStaged)
					g.GroupConfig.Modules[i].SectionConfig[j].ConfigID = configID
					g.GroupConfig.Modules[i].SectionConfig[j].IsStaged = isStaged
				}
			}
		}
	}
}

func (g *Group) SetSectionConfigIDForChildGroup(configID, moduleName, subModuleName string, isStaged bool) {
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
				for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig {
					if g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].SubModuleName == subModuleName {
						g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].ConfigID = configID
						g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].IsStaged = isStaged
					}
				}
			}
		}
	}
}

func (g *Group) AddNewConfigID(configID, moduleName, subModuleName string, isStaged bool) error {
	logger.Log.Warnln(configID, moduleName, subModuleName, isStaged)
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SectionConfig {
				if g.GroupConfig.Modules[i].SectionConfig[j].SubModuleName == subModuleName {
					return fmt.Errorf("submodule %s already exists", subModuleName)
				}
			}
			sectionConfig := SectionConfigDetails{
				SubModuleName: subModuleName,
				ConfigID:      configID,
				IsStaged:      isStaged,
			}
			g.GroupConfig.Modules[i].SectionConfig = append(g.GroupConfig.Modules[i].SectionConfig, sectionConfig)
		}
	}
	return nil
}

func (g *Group) AddNewConfigIDForChildGroup(configID, moduleName, subModuleName string, isStaged bool) error {
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
				for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig {
					if g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].SubModuleName == subModuleName {
						return fmt.Errorf("submodule %s already exists", subModuleName)
					}
				}
				sectionConfig := SectionConfigDetails{
					SubModuleName: subModuleName,
					ConfigID:      configID,
					IsStaged:      isStaged,
				}
				g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig = append(g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig, sectionConfig)
			}
		}
	}
	return nil
}

func (g *Group) AddNewSingleSubmitConfigID(configID, moduleName, resourceName string, isStaged bool) error {
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SingleSubmitConfig {
				if g.GroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName == resourceName {
					return fmt.Errorf("resource %s already exists", resourceName)
				}
			}
			singleSubmitConfig := SingleSubmitConfigDetails{
				ResourceName: resourceName,
				ConfigID:     configID,
				IsStaged:     isStaged,
			}
			g.GroupConfig.Modules[i].SingleSubmitConfig = append(g.GroupConfig.Modules[i].SingleSubmitConfig, singleSubmitConfig)
		}
	}
	return nil
}

func (g *Group) AddNewSingleSubmitConfigIDForChildGroup(configID, moduleName, resourceName string, isStaged bool) error {
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
				for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig {
					if g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName == resourceName {
						return fmt.Errorf("resource %s already exists", resourceName)
					}
				}
				singleSubmitConfig := SingleSubmitConfigDetails{
					ResourceName: resourceName,
					ConfigID:     configID,
					IsStaged:     isStaged,
				}
				g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig = append(g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig, singleSubmitConfig)
			}
		}
	}
	return nil
}

func (g *Group) SetWorkflowConfigID(configID, moduleName string, isStaged bool) {
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			g.GroupConfig.Modules[i].WorkflowConfig.ConfigID = configID
			g.GroupConfig.Modules[i].WorkflowConfig.IsStaged = isStaged
		}
	}
}

func (g *Group) SetWorkflowConfigIDForChildGroup(configID, moduleName string, isStaged bool) {
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
				g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.ConfigID = configID
				g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.IsStaged = isStaged
			}
		}
	}
}

func (g *Group) SetChildWorkflowConfigID(configID, moduleName, resourceName string, isStaged bool) {
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].ChildWorkflowConfig {
				if g.GroupConfig.Modules[i].ChildWorkflowConfig[j].ResourceName == resourceName {
					g.GroupConfig.Modules[i].ChildWorkflowConfig[j].ConfigID = configID
					g.GroupConfig.Modules[i].ChildWorkflowConfig[j].IsStaged = isStaged
				}
			}
		}
	}
}

func (g *Group) SetChildWorkflowConfigIDForChildGroup(configID, moduleName, resourceName string, isStaged bool) {
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
				for j := range g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig {
					if g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].ResourceName == resourceName {
						g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].ConfigID = configID
						g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].IsStaged = isStaged
					}
				}
			}
		}
	}
}

func (g *Group) SetTSMConfig(configID string, isStaged bool) {
	g.GroupConfig.TSMConfig.WorkflowConfig.ConfigID = configID
	g.GroupConfig.TSMConfig.WorkflowConfig.IsStaged = isStaged
}

func (g *Group) SetTSMConfigForChildGroup(configID string, isStaged bool) {
	if g.GroupConfig.ChildGroupConfig != nil {
		g.GroupConfig.ChildGroupConfig.TSMConfig.WorkflowConfig.ConfigID = configID
		g.GroupConfig.ChildGroupConfig.TSMConfig.WorkflowConfig.IsStaged = isStaged
	}
}

func (g *Group) SetSingleSubmitConfig(configID, moduleName, resourceName string, isStaged bool) {
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].SingleSubmitConfig {
				if g.GroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName == resourceName {
					g.GroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID = configID
					g.GroupConfig.Modules[i].SingleSubmitConfig[j].IsStaged = isStaged
				}
			}
		}
	}
}

func (g *Group) SetSingleSubmitConfigForChildGroup(configID, moduleName, resourceName string, isStaged bool) {
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
				for j := range g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig {
					if g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ResourceName == resourceName {
						g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].ConfigID = configID
						g.GroupConfig.ChildGroupConfig.Modules[i].SingleSubmitConfig[j].IsStaged = isStaged
					}
				}
			}
		}
	}
}

func (g *Group) PopulatefieldsFromDBNullables() error {

	if g.DBParentGroupIDNullable.Valid {
		g.ParentGroupID = g.DBParentGroupIDNullable.String
	}
	if g.DBChangelogNullable.Valid {
		g.ChangelogText = g.DBChangelogNullable.String
	}
	if g.DBDashboardUserIDNullable.Valid {
		g.DashboardUserID = g.DBDashboardUserIDNullable.String
	}
	if g.DBUpdatedAtNullable.Valid {
		g.UpdatedAt = g.DBUpdatedAtNullable.String
	}
	if g.DBGroupConfig != "" {
		err := json.Unmarshal([]byte(g.DBGroupConfig), &g.GroupConfig)
		if err != nil {
			return err
		}
	}
	return nil

}

func (g *Group) GetAllConfigMetadataByModule(moduleName string) ([]SectionConfigDetails, []SingleSubmitConfigDetails, WorkflowConfigDetails, []ChildWorkflowConfigDetails) {
	for idx := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[idx].ModuleName == moduleName {
			return g.GroupConfig.Modules[idx].SectionConfig, g.GroupConfig.Modules[idx].SingleSubmitConfig, g.GroupConfig.Modules[idx].WorkflowConfig, g.GroupConfig.Modules[idx].ChildWorkflowConfig
		}
	}
	return []SectionConfigDetails{}, []SingleSubmitConfigDetails{}, WorkflowConfigDetails{}, []ChildWorkflowConfigDetails{}
}

func (g *Group) GetAllConfigMetadataByModuleChild(moduleName string) ([]SectionConfigDetails, []SingleSubmitConfigDetails, WorkflowConfigDetails, []ChildWorkflowConfigDetails) {
	if g.GroupConfig.ChildGroupConfig != nil {
		for idx := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[idx].ModuleName == moduleName {
				return g.GroupConfig.ChildGroupConfig.Modules[idx].SectionConfig, g.GroupConfig.ChildGroupConfig.Modules[idx].SingleSubmitConfig, g.GroupConfig.ChildGroupConfig.Modules[idx].WorkflowConfig,
					g.GroupConfig.ChildGroupConfig.Modules[idx].ChildWorkflowConfig
			}
		}
	}
	return []SectionConfigDetails{}, []SingleSubmitConfigDetails{}, WorkflowConfigDetails{}, []ChildWorkflowConfigDetails{}
}

func (g *Group) AddNewChildWorkflowConfigID(configID, moduleName, resourceName string, isStaged bool) error {
	for i := range g.GroupConfig.Modules {
		if g.GroupConfig.Modules[i].ModuleName == moduleName {
			for j := range g.GroupConfig.Modules[i].ChildWorkflowConfig {
				if g.GroupConfig.Modules[i].ChildWorkflowConfig[j].ResourceName == resourceName {
					return fmt.Errorf("resource %s already exists", resourceName)
				}
			}
			childWorkflowConfig := ChildWorkflowConfigDetails{
				ResourceName: resourceName,
			}
			childWorkflowConfig.ConfigID = configID
			childWorkflowConfig.IsStaged = isStaged
			g.GroupConfig.Modules[i].ChildWorkflowConfig = append(g.GroupConfig.Modules[i].ChildWorkflowConfig, childWorkflowConfig)
		}
	}
	return nil
}

func (g *Group) AddNewChildWorkflowConfigIDForChildGroup(configID, moduleName, resourceName string, isStaged bool) error {
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := range g.GroupConfig.ChildGroupConfig.Modules {
			if g.GroupConfig.ChildGroupConfig.Modules[i].ModuleName == moduleName {
				for j := range g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig {
					if g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig[j].ResourceName == resourceName {
						return fmt.Errorf("resource %s already exists", resourceName)
					}
				}
				childWorkflowConfig := ChildWorkflowConfigDetails{
					ResourceName: resourceName,
				}
				childWorkflowConfig.ConfigID = configID
				childWorkflowConfig.IsStaged = isStaged
				g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig = append(g.GroupConfig.ChildGroupConfig.Modules[i].ChildWorkflowConfig, childWorkflowConfig)
			}
		}
	}
	return nil
}

func (g *Group) GetSubModulesForWorkflowConfig(workflowConfigID string) []string {
	subModules := make([]string, 0)
	for i := 0; i < len(g.GroupConfig.Modules); i++ {
		if g.GroupConfig.Modules[i].WorkflowConfig.ConfigID == workflowConfigID {
			for j := 0; j < len(g.GroupConfig.Modules[i].SectionConfig); j++ {
				subModules = append(subModules, g.GroupConfig.Modules[i].SectionConfig[j].SubModuleName)
			}
		}
	}
	if g.GroupConfig.ChildGroupConfig != nil {
		for i := 0; i < len(g.GroupConfig.ChildGroupConfig.Modules); i++ {
			if g.GroupConfig.ChildGroupConfig.Modules[i].WorkflowConfig.ConfigID == workflowConfigID {
				for j := 0; j < len(g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig); j++ {
					subModules = append(subModules, g.GroupConfig.ChildGroupConfig.Modules[i].SectionConfig[j].SubModuleName)
				}
			}
		}
	}
	return subModules
}
