package stagingworkflowconfigtransport

import (
	"context"
	"encoding/json"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/db"
	"finbox/go-api/requestHandler/requestCommon"
	studioconstants "finbox/go-api/studio/constants"
	"finbox/go-api/studio/models"
	stagingworkflowconfigrepository "finbox/go-api/studio/repository/stagingworkflowconfig"
	stagingservice "finbox/go-api/studio/services/staging"
	stagingworkflowconfigservice "finbox/go-api/studio/services/stagingworkflowconfig"
	"finbox/go-api/utils/general"
	"fmt"
	"net/http"
	"strings"

	"github.com/finbox-in/road-runner/runner"
	validator "github.com/go-playground/validator/v10"
)

var database = db.GetDB()

var log = logger.Log
var recovery = requestCommon.Recovery

type StagingWorkflowConfigHandler struct {
	StagingWorkflowConfigBuilder stagingworkflowconfigservice.IStagingWorkflowConfigBuilder
	StagingService               stagingservice.IStagingService
}

func New(stagingWorkflowConfigBuilder stagingworkflowconfigservice.IStagingWorkflowConfigBuilder, stagingService stagingservice.IStagingService) *StagingWorkflowConfigHandler {
	return &StagingWorkflowConfigHandler{
		StagingWorkflowConfigBuilder: stagingWorkflowConfigBuilder,
		StagingService:               stagingService,
	}
}

func (s *StagingWorkflowConfigHandler) GetAllConfigsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		var attributes = make(map[string]interface{})

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) NewConfigReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Title       string          `json:"title"`
			Version     string          `json:"version"`
			Description string          `json:"description"`
			Metadata    json.RawMessage `json:"metadata"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if reqObj.Title == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "title is required")
			return
		}

		var attributes = map[string]interface{}{
			"title":       reqObj.Title,
			"version":     reqObj.Version,
			"description": reqObj.Description,
			"metadata":    reqObj.Metadata,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) ValidateBuilderConfigReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID []string `json:"builderConfigID" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]interface{}{
			"builderConfigID": reqObj.BuilderConfigID[0],
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) DeleteConfigReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID string `json:"builderConfigID" validate:"required"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		var attributes = map[string]interface{}{
			"builderConfigID": reqObj.BuilderConfigID,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AddNodeReq to builder/requestHandler
func (s *StagingWorkflowConfigHandler) AddNodeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID string                            `json:"builderConfigID" validate:"required"`
			TemplateID      string                            `json:"templateID"`
			NodeType        string                            `json:"nodeType" validate:"required"`
			NodeName        string                            `json:"nodeName"`
			NodeData        models.StagedWorkflowNodeData     `json:"nodeData"`
			NodeMetadata    models.StagedWorkflowNodeMetadata `json:"metadata"`
			FromNodeID      string                            `json:"fromNodeID"`
			FromHandle      string                            `json:"fromHandle"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.InArr(reqObj.NodeType, stagingworkflowconfigrepository.NodeTypeArr) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid nodeType")
			return
		}

		if reqObj.NodeType == stagingworkflowconfigrepository.NodeTypeOperation && reqObj.NodeMetadata.SubNodeType != stagingworkflowconfigrepository.SubNodeTypeSubWorkflow && !general.ValidateUUID(reqObj.TemplateID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "templateID is required")
			return
		}

		if reqObj.NodeName == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "nodeName is required")
			return
		}

		if reqObj.FromNodeID != "" && !general.ValidateUUID(reqObj.FromNodeID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid fromNodeID")
			return
		}

		if reqObj.FromNodeID != "" && reqObj.FromHandle == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid fromHandle")
			return
		}

		attributes := map[string]interface{}{
			"builderConfigID": reqObj.BuilderConfigID,
			"nodeName":        reqObj.NodeName,
			"nodeType":        reqObj.NodeType,
			"nodeData":        reqObj.NodeData,
			"templateID":      reqObj.TemplateID,
			"nodeMetadata":    reqObj.NodeMetadata,
			"fromNodeID":      reqObj.FromNodeID,
			"fromHandle":      reqObj.FromHandle,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AddBusinessTemplateReq handles the request for adding a template to the business_templates table
func (s *StagingWorkflowConfigHandler) AddBusinessTemplateReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		// Request struct to parse incoming data
		type requestStruct struct {
			TemplateID   string          `json:"templateID" validate:"required"`
			DisplayName  string          `json:"displayName" validate:"required"`
			Description  string          `json:"description"`
			StartState   string          `json:"startState" validate:"required"`
			EndState     string          `json:"endState" validate:"required"`
			TemplateJSON json.RawMessage `json:"templateJSON" validate:"required"`
			Placeholders json.RawMessage `json:"placeholders"`
			Output       json.RawMessage `json:"output"`
			Status       string          `json:"status" validate:"required"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		// Attributes for context
		attributes := map[string]interface{}{
			"templateID":   reqObj.TemplateID,
			"displayName":  reqObj.DisplayName,
			"description":  reqObj.Description,
			"startState":   reqObj.StartState,
			"endState":     reqObj.EndState,
			"templateJSON": reqObj.TemplateJSON,
			"placeholders": reqObj.Placeholders,
			"output":       reqObj.Output,
			"status":       reqObj.Status,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) EditNodeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID    string                        `json:"builderConfigID" validate:"required"`
			NodeID             string                        `json:"nodeID" validate:"required"`
			NodeType           string                        `json:"nodeType" validate:"required"`
			TemplateID         string                        `json:"templateID"`
			NodeName           string                        `json:"nodeName"`
			NodeData           models.StagedWorkflowNodeData `json:"nodeData"`
			UpdateOnlyNodeName bool                          `json:"updateOnlyNodeName"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.InArr(reqObj.NodeType, stagingworkflowconfigrepository.NodeTypeArr) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid nodeType")
			return
		}

		attributes := map[string]interface{}{
			"builderConfigID":    reqObj.BuilderConfigID,
			"nodeID":             reqObj.NodeID,
			"nodeType":           reqObj.NodeType,
			"nodeData":           reqObj.NodeData,
			"nodeName":           reqObj.NodeName,
			"templateID":         reqObj.TemplateID,
			"updateOnlyNodeName": reqObj.UpdateOnlyNodeName,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) UpdateNodesMetadataReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID     string                                    `json:"builderConfigID" validate:"required"`
			NodeMetaDataUpdates []models.StagedWorkflowNodeMetadataUpdate `json:"nodeMetaDataUpdates"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		for idx := range reqObj.NodeMetaDataUpdates {
			if reqObj.NodeMetaDataUpdates[idx].NodeID == "" {
				errorHandler.CustomError(w, http.StatusBadRequest, "required parameter nodeID missing")
				return
			}
		}

		attributes := map[string]interface{}{
			"builderConfigID":     reqObj.BuilderConfigID,
			"nodeMetaDataUpdates": reqObj.NodeMetaDataUpdates,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) UpdateNodeMetadataReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID    string                                  `json:"builderConfigID" validate:"required"`
			NodeMetaDataUpdate models.StagedWorkflowNodeMetadataUpdate `json:"nodeMetaDataUpdate"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		if reqObj.NodeMetaDataUpdate.NodeID == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "required parameter nodeID missing")
			return
		}

		attributes := map[string]interface{}{
			"builderConfigID":    reqObj.BuilderConfigID,
			"nodeMetaDataUpdate": reqObj.NodeMetaDataUpdate,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) DeleteNodeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID string `json:"builderConfigID" validate:"required"`
			NodeID          string `json:"nodeID" validate:"required"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := map[string]interface{}{
			"builderConfigID": reqObj.BuilderConfigID,
			"nodeID":          reqObj.NodeID,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) AddEdgeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID         string                            `json:"builderConfigID" validate:"required"`
			FromNodeID              string                            `json:"fromNodeID" validate:"required"`
			ToNodeID                string                            `json:"toNodeID"`
			AssociatedEdgeCondition string                            `json:"associatedEdgeCondition"`
			Metadata                models.StagedWorkflowEdgeMetadata `json:"metadata"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		if reqObj.ToNodeID == "" && reqObj.AssociatedEdgeCondition == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "toNodeID and associatedEdgeCondition both cannot be empty together")
			return
		}

		attributes := map[string]interface{}{
			"builderConfigID":         reqObj.BuilderConfigID,
			"fromNodeID":              reqObj.FromNodeID,
			"toNodeID":                reqObj.ToNodeID,
			"associatedEdgeCondition": reqObj.AssociatedEdgeCondition,
			"metadata":                reqObj.Metadata,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) EditEdgeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID         string                            `json:"builderConfigID" validate:"required"`
			EdgeID                  string                            `json:"edgeID"`
			FromNodeID              string                            `json:"fromNodeID"`
			ToNodeID                string                            `json:"toNodeID"`
			AssociatedEdgeCondition string                            `json:"associatedEdgeCondition"`
			Metadata                models.StagedWorkflowEdgeMetadata `json:"metadata"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := map[string]interface{}{
			"builderConfigID":         reqObj.BuilderConfigID,
			"edgeID":                  reqObj.EdgeID,
			"fromNodeID":              reqObj.FromNodeID,
			"toNodeID":                reqObj.ToNodeID,
			"associatedEdgeCondition": reqObj.AssociatedEdgeCondition,
			"metadata":                reqObj.Metadata,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) DeleteEdgeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID         string `json:"builderConfigID" validate:"required"`
			EdgeID                  string `json:"edgeID"`
			FromNodeID              string `json:"fromNodeID"`
			ToNodeID                string `json:"toNodeID"`
			AssociatedEdgeCondition string `json:"associatedEdgeCondition"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := map[string]interface{}{
			"builderConfigID":         reqObj.BuilderConfigID,
			"edgeID":                  reqObj.EdgeID,
			"fromNodeID":              reqObj.FromNodeID,
			"toNodeID":                reqObj.ToNodeID,
			"associatedEdgeCondition": reqObj.AssociatedEdgeCondition,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GetActiveNodesAndEdgesReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID []string `json:"builderConfigID" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]interface{}{
			"builderConfigID": reqObj.BuilderConfigID[0],
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) ImportServerlessWorkflowConfigReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			InputServerless json.RawMessage `json:"inputServerless"`
		}

		var reqObj requestStruct
		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := map[string]interface{}{
			"inputServerless": reqObj.InputServerless,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GenerateServerlessWorkflowConfigReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID []string `json:"builderConfigID" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]interface{}{
			"builderConfigID": reqObj.BuilderConfigID[0],
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GetServerlessStateDataReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			GroupID         []string `json:"groupID"`
			BuilderConfigID []string `json:"builderConfigID" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		var groupID string
		if len(reqObj.GroupID) > 0 {
			groupID = reqObj.GroupID[0]
		}

		attributes := map[string]interface{}{
			"builderConfigID": reqObj.BuilderConfigID[0],
			"groupID":         groupID,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GenerateBackendServerlessWorkflowConfigReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BuilderConfigID []string `json:"builderConfigID" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]interface{}{
			"builderConfigID": reqObj.BuilderConfigID[0],
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GetAllConfigsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		log.Println("attributes:", attributes)

		configs, err := s.StagingWorkflowConfigBuilder.GetAllActiveStagedWorkflowConfigs(r.Context())
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"configs": configs,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func (s *StagingWorkflowConfigHandler) NewConfigCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		title := attributes["title"].(string)
		version := attributes["version"].(string)
		description := attributes["description"].(string)
		metadata := attributes["metadata"].(json.RawMessage)

		if version == "" {
			version = "v1"
		}

		builderConfigID := general.GetUUID()
		metadataMap := make(map[string]interface{})
		json.Unmarshal(metadata, &metadataMap)

		err := s.StagingWorkflowConfigBuilder.AddNewStagedWorkflowConfig(r.Context(), models.StagedWorkflowConfig{
			ID:          builderConfigID,
			Title:       title,
			Version:     version,
			Description: description,
			Metadata:    metadataMap,
		}, nil)
		if err != nil && err == stagingworkflowconfigrepository.ErrTitleVersionExists {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err.Error())
		} else if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"configID": builderConfigID,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) DeleteConfigCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)

		err := s.StagingWorkflowConfigBuilder.DeleteStagedWorkflowConfig(r.Context(), builderConfigID, nil)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"message": "Config deleted successfully",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) ValidateBuilderConfigCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})
		builderConfigID := attributes["builderConfigID"].(string)

		wfErrors, err := s.StagingWorkflowConfigBuilder.ValidateStagedWorkflowConfigID(r.Context(), builderConfigID)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"validationErrors": wfErrors,
			"message":          "please ensure all validationErrors are passed!",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// Add to builder/controller

func (s *StagingWorkflowConfigHandler) AddNodeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)
		templateID := attributes["templateID"].(string)
		nodeType := attributes["nodeType"].(string)
		nodeName := attributes["nodeName"].(string)
		nodeData := attributes["nodeData"].(models.StagedWorkflowNodeData)
		nodeMetadata := attributes["nodeMetadata"].(models.StagedWorkflowNodeMetadata)
		fromNodeID := attributes["fromNodeID"].(string)
		fromHandle := attributes["fromHandle"].(string)

		if nodeType == stagingworkflowconfigrepository.NodeTypeForEach && templateID != "" {
			isCompatible, err := s.StagingWorkflowConfigBuilder.CheckIfTemplateIDCompatibleForEachNode(r.Context(), templateID)
			if err != nil {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic(err)
			}

			if !isCompatible {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic("template is not compatible with foreach node")
			}
		}

		nodeID := general.GetUUID()

		err := s.StagingWorkflowConfigBuilder.AddNode(r.Context(), models.StagedWorkflowConfigNode{
			StagedWorkflowConfigID:  builderConfigID,
			NodeID:                  nodeID,
			NodeName:                nodeName,
			NodeType:                nodeType,
			TemplateID:              templateID,
			NodeData:                nodeData,
			Metadata:                nodeMetadata,
			FromNodeID:              fromNodeID,
			AssociatedEdgeCondition: fromHandle,
		}, nil)
		if err != nil && err == stagingworkflowconfigrepository.ErrActiveNodeNameExists {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err.Error())
		} else if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"nodeID": nodeID,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AddBusinessTemplateCont adds a template to the business_templates table
func (s *StagingWorkflowConfigHandler) AddBusinessTemplateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})

		// Extract data from context
		templateID := attributes["templateID"].(string)
		displayName := attributes["displayName"].(string)
		description := attributes["description"].(string)
		startState := attributes["startState"].(string)
		endState := attributes["endState"].(string)
		templateJSON := attributes["templateJSON"].(json.RawMessage)
		placeholders := attributes["placeholders"].(json.RawMessage)
		output := attributes["output"].(json.RawMessage)
		status := attributes["status"].(string)

		// Convert JSONB columns to string for insertion
		var templateJSONObj map[string]interface{}
		var placeholdersObj models.BusinessTemplatePlaceholdersStruct
		var outputObj models.BusinessTemplateOutputVariables

		err := json.Unmarshal(templateJSON, &templateJSONObj)
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		err = json.Unmarshal(placeholders, &placeholdersObj)
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		err = json.Unmarshal(output, &outputObj)
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		// Insert into database
		err = s.StagingWorkflowConfigBuilder.AddBusinessTemplate(r.Context(), models.BusinessTemplateStruct{
			TemplateID:      templateID,
			DisplayName:     displayName,
			Description:     description,
			StartState:      startState,
			EndState:        endState,
			TemplateJSONObj: templateJSONObj,
			PlaceHoldersObj: placeholdersObj,
			OutputObj:       outputObj,
			Status:          status,
		}, nil)
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		// Prepare response
		resData := map[string]interface{}{
			"templateID": templateID,
			"message":    "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GetBusinessTemplatesHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		type requestStruct struct {
			Limit         []string `json:"limit"`
			Page          []string `json:"page"`
			Search        []string `json:"search"`
			TemplateTypes []string `json:"templateTypes"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		limit, offset, err := handlePaginationRequestParams(reqObj.Limit, reqObj.Page)
		if err != nil {
			logger.WithRequest(r).Errorf("Invalid pagination parameters: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid pagination parameters")
			return
		}

		// Get search and template types from query parameters
		search := ""
		if len(reqObj.Search) > 0 {
			search = reqObj.Search[0]
		}

		templateTypes := []string{}
		if len(reqObj.TemplateTypes) > 0 && reqObj.TemplateTypes[0] != "" {
			templateTypes = strings.Split(reqObj.TemplateTypes[0], ",")
			for _, templateType := range templateTypes {
				if !general.InArr(templateType, studioconstants.ValidBusinessTemplateTypes) {
					errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("invalid template type: %s", templateType))
					return
				}
			}
		}

		templates, err := s.StagingWorkflowConfigBuilder.GetAllBusinessTemplate(r.Context(), stagingworkflowconfigrepository.ListBusinessTemplatesParams{
			Limit:         limit,
			Offset:        offset,
			Search:        search,
			TemplateTypes: templateTypes,
		})
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", templates)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) HandleCloneBuilderConfig(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		builderConfigID := r.URL.Query().Get("builderConfigID")

		if !general.ValidateUUID(builderConfigID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "inavlid builderConfigID")
			return
		}

		newBuilderConfigID, err := s.StagingWorkflowConfigBuilder.DuplicateStagedWorkflowConfigID(r.Context(), builderConfigID, nil)
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"newBuilderConfigID": newBuilderConfigID,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) EditNodeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)
		nodeID := attributes["nodeID"].(string)
		nodeType := attributes["nodeType"].(string)
		nodeName := attributes["nodeName"].(string)
		nodeData := attributes["nodeData"].(models.StagedWorkflowNodeData)
		templateID := attributes["templateID"].(string)
		updateOnlyNodeName := attributes["updateOnlyNodeName"].(bool)

		if nodeType == stagingworkflowconfigrepository.NodeTypeForEach && templateID != "" {
			isCompatible, err := s.StagingWorkflowConfigBuilder.CheckIfTemplateIDCompatibleForEachNode(r.Context(), templateID)
			if err != nil {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic(err)
			}

			if !isCompatible {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic("template is not compatible with foreach node")
			}
		}

		pass, err := s.StagingWorkflowConfigBuilder.CheckIfNodeAssociatedWithStagedWorkflowConfigID(r.Context(), models.StagedWorkflowConfigNode{
			NodeID:                 nodeID,
			StagedWorkflowConfigID: builderConfigID,
		})

		if !pass {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic("node does not belong to this builder config")
		}

		if updateOnlyNodeName {
			err = s.StagingWorkflowConfigBuilder.EditNode(r.Context(), models.StagedWorkflowConfigNode{
				NodeID:                 nodeID,
				StagedWorkflowConfigID: builderConfigID,
				NodeName:               nodeName,
			}, nil)
		} else {
			err = s.StagingWorkflowConfigBuilder.EditNode(r.Context(), models.StagedWorkflowConfigNode{
				NodeID:                 nodeID,
				StagedWorkflowConfigID: builderConfigID,
				NodeName:               nodeName,
				NodeType:               nodeType,
				TemplateID:             templateID,
				NodeData:               nodeData,
			}, nil)
		}

		if err != nil && err == stagingworkflowconfigrepository.ErrActiveNodeNameExists {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err.Error())
		} else if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"message": "Node updated successfully",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) UpdateNodesMetadataCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)
		updates := attributes["nodeMetaDataUpdates"].([]models.StagedWorkflowNodeMetadataUpdate)

		for i := 0; i < len(updates); i++ {
			pass, err := s.StagingWorkflowConfigBuilder.CheckIfNodeAssociatedWithStagedWorkflowConfigID(r.Context(), models.StagedWorkflowConfigNode{
				NodeID:                 updates[i].NodeID,
				StagedWorkflowConfigID: builderConfigID,
			})
			if err != nil {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic(err)
			}

			if !pass {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err, fmt.Sprintf("node: %s does not belong to this builder config", updates[i].NodeID))
				// panic(fmt.Sprintf("node: %s does not belong to this builder config", updates[i].NodeID))
			}
		}

		tx, err := database.Beginx()
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}
		defer tx.Rollback()

		err = s.StagingWorkflowConfigBuilder.UpdateNodesMetadata(r.Context(), builderConfigID, updates, tx)
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		err = tx.Commit()
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"message": "Nodes metadata updated successfully",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) UpdateNodeMetadataCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)
		update := attributes["nodeMetaDataUpdate"].(models.StagedWorkflowNodeMetadataUpdate)

		pass, err := s.StagingWorkflowConfigBuilder.CheckIfNodeAssociatedWithStagedWorkflowConfigID(r.Context(), models.StagedWorkflowConfigNode{
			NodeID:                 update.NodeID,
			StagedWorkflowConfigID: builderConfigID,
		})
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		if !pass {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err, fmt.Sprintf("node: %s does not belong to this builder config", update.NodeID))
			// panic(fmt.Sprintf("node: %s does not belong to this builder config", updates[i].NodeID))
		}

		err = s.StagingWorkflowConfigBuilder.UpdateNodeMetadata(r.Context(), builderConfigID, update, nil)
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"message": "Nodes metadata updated successfully",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) DeleteNodeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)
		nodeID := attributes["nodeID"].(string)

		pass, err := s.StagingWorkflowConfigBuilder.CheckIfNodeAssociatedWithStagedWorkflowConfigID(r.Context(), models.StagedWorkflowConfigNode{
			NodeID:                 nodeID,
			StagedWorkflowConfigID: builderConfigID,
		})
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		if !pass {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic("node does not belong to this builder config")
		}

		tx, err := database.Beginx()
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}
		defer tx.Rollback()

		err = s.StagingWorkflowConfigBuilder.DeleteNode(r.Context(), models.StagedWorkflowConfigNode{
			NodeID:                 nodeID,
			StagedWorkflowConfigID: builderConfigID,
		}, nil)
		// if err != nil && err == stagingworkflowconfigrepository.ErrNodeHasDownstreamEdges {
		// 	logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
		// 	panic("please delete downstream nodes before this node, and delete all conditions if on a switch node")
		// } else if err != nil {
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		err = tx.Commit()
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"message": "Node deleted successfully",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) AddEdgeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)
		fromNodeID := attributes["fromNodeID"].(string)
		toNodeID := attributes["toNodeID"].(string)
		associatedEdgeCondition := attributes["associatedEdgeCondition"].(string)
		metadata := attributes["metadata"].(models.StagedWorkflowEdgeMetadata)

		edgeID := general.GetUUID()

		edgeID, err := s.StagingWorkflowConfigBuilder.AddEdge(r.Context(), models.StagedWorkflowEdgeStruct{
			EdgeID:                  edgeID,
			StagedWorkflowConfigID:  builderConfigID,
			FromNodeID:              fromNodeID,
			ToNodeID:                toNodeID,
			AssociatedEdgeCondition: associatedEdgeCondition,
			Status:                  stagingworkflowconfigrepository.StagedWorkflowEdgeStatusActive,
			Metadata:                metadata,
		}, nil)
		if err != nil {
			if strings.Contains(err.Error(), "self-loops") {
				errorHandler.CustomError(w, http.StatusBadRequest, "self-loops are not permitted for this node type")
				return
			} else if err == stagingworkflowconfigrepository.ErrActiveEdgeExists {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic(err.Error())
			} else {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic(err)
			}
		}

		resData := map[string]interface{}{
			"edgeID":  edgeID,
			"message": "Edge added successfully",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) EditEdgeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)
		edgeID := attributes["edgeID"].(string)
		fromNodeID := attributes["fromNodeID"].(string)
		toNodeID := attributes["toNodeID"].(string)
		associatedEdgeCondition := attributes["associatedEdgeCondition"].(string)
		metadata := attributes["metadata"].(models.StagedWorkflowEdgeMetadata)

		if edgeID != "" {
			pass, err := s.StagingWorkflowConfigBuilder.CheckIfEdgeAssociatedWithStagedWorkflowConfigID(r.Context(), models.StagedWorkflowEdgeStruct{
				EdgeID:                 edgeID,
				StagedWorkflowConfigID: builderConfigID,
			})
			if err != nil {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic(err)
			}

			if !pass {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic("edge does not belong to this builder config")
			}
		}

		_, err := s.StagingWorkflowConfigBuilder.EditEdge(r.Context(), models.StagedWorkflowEdgeStruct{
			EdgeID:                  edgeID,
			StagedWorkflowConfigID:  builderConfigID,
			FromNodeID:              fromNodeID,
			ToNodeID:                toNodeID,
			AssociatedEdgeCondition: associatedEdgeCondition,
			Metadata:                metadata,
		}, nil)
		if err != nil && err == stagingworkflowconfigrepository.ErrNoActiveEdgeFound {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err.Error())
		} else if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"message": "Edge updated successfully",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) DeleteEdgeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)
		edgeID := attributes["edgeID"].(string)
		fromNodeID := attributes["fromNodeID"].(string)
		toNodeID := attributes["toNodeID"].(string)
		associatedEdgeCondition := attributes["associatedEdgeCondition"].(string)

		if edgeID != "" {
			pass, err := s.StagingWorkflowConfigBuilder.CheckIfEdgeAssociatedWithStagedWorkflowConfigID(r.Context(), models.StagedWorkflowEdgeStruct{
				EdgeID:                 edgeID,
				StagedWorkflowConfigID: builderConfigID,
			})
			if err != nil {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
				panic(err)
			}

			if !pass {
				err = fmt.Errorf("edge does not belong to this builder config")
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err.Error())
				panic(err.Error())
			}
		}

		err := s.StagingWorkflowConfigBuilder.DeleteEdge(r.Context(), models.StagedWorkflowEdgeStruct{
			EdgeID:                  edgeID,
			StagedWorkflowConfigID:  builderConfigID,
			FromNodeID:              fromNodeID,
			ToNodeID:                toNodeID,
			AssociatedEdgeCondition: associatedEdgeCondition,
		}, nil)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"message": "Edge deleted successfully",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GetActiveNodesAndEdgesCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)

		nodes, edges, err := s.StagingWorkflowConfigBuilder.GetAllActiveNodesAndEdges(r.Context(), builderConfigID, nil)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"nodes":  nodes,
			"edges":  edges,
			"status": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GenerateServerlessWorkflowConfigCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)

		serverlessWorkflow, err := s.StagingWorkflowConfigBuilder.GenerateServerlessFromStagedWorkflowConfigID(r.Context(), builderConfigID)
		if err != nil && err == stagingworkflowconfigrepository.ErrStartNodeNotFound {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
		} else if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err.Error())
		}

		bytes, err := json.Marshal(serverlessWorkflow)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		wf := make(map[string]interface{})

		err = json.Unmarshal(bytes, &wf)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"serverlessWorkflow": wf,
			"status":             "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GetServerlessStateDataCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})
		builderConfigID := attributes["builderConfigID"].(string)
		groupID := attributes["groupID"].(string)

		stateDataList, err := s.StagingWorkflowConfigBuilder.GetServerlessStatesData(r.Context(), builderConfigID)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		subModuleNames := make([]string, 0)
		if len(groupID) > 0 {
			group, err := s.StagingService.GetGroupDetails(r.Context(), groupID)
			if err != nil {
				logger.WithStagedWorkflowConfigID(builderConfigID).Errorf("error fetching group for ID: %s, err: %v", groupID, err)
				panic(err)
			}
			subModuleNames = group.GetSubModulesForWorkflowConfig(builderConfigID)
		}

		resData := map[string]interface{}{
			"stateData":      stateDataList,
			"subModuleNames": subModuleNames,
			"status":         "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) GenerateBackendServerlessWorkflowConfigCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		builderConfigID := attributes["builderConfigID"].(string)

		serverlessWorkflow, err := s.StagingWorkflowConfigBuilder.GenerateBackendServerlessFromStagedWorkflowConfigID(r.Context(), builderConfigID)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err.Error())
		}

		bytes, err := json.Marshal(serverlessWorkflow)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		wf := make(map[string]interface{})

		err = json.Unmarshal(bytes, &wf)
		if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"serverlessWorkflow": wf,
			"status":             "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) ImportServerlessWorkflowConfigCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})
		inputServerless := attributes["inputServerless"].(json.RawMessage)
		serverlessWorkflowBytes, err := inputServerless.MarshalJSON()
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		log.Println(string(serverlessWorkflowBytes))

		wf, err := runner.ParseWorkflowFromJSON(string(serverlessWorkflowBytes))
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		tx, err := database.Beginx()
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}
		defer tx.Rollback()

		builderConfigID, err := s.StagingWorkflowConfigBuilder.ImportStagedWorkflowConfigFromServerlessModel(r.Context(), *wf, map[string]interface{}{}, tx)
		if err != nil && err == stagingworkflowconfigrepository.ErrTitleVersionExists {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err.Error())
		} else if err != nil {
			logger.WithStagedWorkflowConfigID(builderConfigID).Errorln(err)
			panic(err)
		}

		err = tx.Commit()
		if err != nil {
			logger.WithContext(r.Context()).Errorln(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"builderConfigID": builderConfigID,
			"message":         "node and edges saved successfully!",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) RenameNodeHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		type requestStruct struct {
			BuilderConfigID string `json:"builderConfigID" validate:"required"`
			NodeID          string `json:"nodeID" validate:"required"`
			NodeName        string `json:"nodeName" validate:"required"`
		}
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			logger.Log.Errorln(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request body")
			return
		}

		customErr := s.StagingWorkflowConfigBuilder.RenameNode(r.Context(), reqObj.BuilderConfigID, reqObj.NodeID, reqObj.NodeName)
		if customErr != nil {
			logger.WithRequest(r).Errorln(customErr.WrapError())
			errorHandler.CustomBuilderError(w, *customErr)
			return
		}

		resData := map[string]interface{}{
			"message": "node renamed",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) UpdateNodeCoordinatesHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		type requestStruct struct {
			BuilderConfigID string  `json:"builderConfigID" validate:"required"`
			NodeID          string  `json:"nodeID" validate:"required"`
			X               float64 `json:"x" validate:"required"`
			Y               float64 `json:"y" validate:"required"`
		}
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			logger.Log.Errorln(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request body")
			return
		}

		customErr := s.StagingWorkflowConfigBuilder.UpdateNodeCoordinates(r.Context(), reqObj.BuilderConfigID, reqObj.NodeID, reqObj.X, reqObj.Y)
		if customErr != nil {
			logger.WithRequest(r).Errorln(customErr.WrapError())
			errorHandler.CustomBuilderError(w, *customErr)
			return
		}

		resData := map[string]interface{}{
			"message": "co-ordinates updated",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) UpdateNodeArgumentsHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		type requestStruct struct {
			BuilderConfigID string                             `json:"builderConfigID" validate:"required"`
			NodeID          string                             `json:"nodeID" validate:"required"`
			Args            models.StagedWorkflowNodeArguments `json:"args" validate:"required"`
		}
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			logger.Log.Errorln(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request body")
			return
		}

		customErr := s.StagingWorkflowConfigBuilder.UpdateNodeArguments(r.Context(), reqObj.BuilderConfigID, reqObj.NodeID, reqObj.Args)
		if customErr != nil {
			logger.WithRequest(r).Errorln(customErr.WrapError())
			errorHandler.CustomBuilderError(w, *customErr)
			return
		}

		resData := map[string]interface{}{
			"message": "co-ordinates updated",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) UpdateNodeJourneyMetadataHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		type requestStruct struct {
			BuilderConfigID string                                    `json:"builderConfigID" validate:"required"`
			NodeID          string                                    `json:"nodeID" validate:"required"`
			Metadata        models.StagedWorkflowGenericMetadataInput `json:"args" validate:"required"`
		}
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			logger.Log.Errorln(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request body")
			return
		}

		customErr := s.StagingWorkflowConfigBuilder.UpdateNodeJourneyMetadata(r.Context(), reqObj.BuilderConfigID, reqObj.NodeID, reqObj.Metadata)
		if customErr != nil {
			logger.WithRequest(r).Errorln(customErr.WrapError())
			errorHandler.CustomBuilderError(w, *customErr)
			return
		}

		resData := map[string]interface{}{
			"message": "metadata updated",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (s *StagingWorkflowConfigHandler) RearrangeNodesHandler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		type requestStruct struct {
			BuilderConfigID string                   `json:"builderConfigID" validate:"required"`
			Coordinates     []models.NodeCoordinates `json:"coordinates" validate:"required"`
		}
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			logger.Log.Errorln(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request body")
			return
		}

		customErr := s.StagingWorkflowConfigBuilder.RearrangeNodes(r.Context(), reqObj.BuilderConfigID, reqObj.Coordinates)
		if customErr != nil {
			logger.WithRequest(r).Errorln(customErr.WrapError())
			errorHandler.CustomBuilderError(w, *customErr)
			return
		}

		resData := map[string]interface{}{
			"message": "nodes re-arranged",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
